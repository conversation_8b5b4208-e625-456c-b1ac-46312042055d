using System;
using System.ComponentModel.DataAnnotations;
using HolyBless.Enums;

namespace HolyBless.Buckets.Dtos
{
    public class CreateUpdateBucketFileDto
    {
        [Required]
        public string FileName { get; set; } = default!;

        public string? Title { get; set; }
        public string RelativePathInBucket { get; set; } = "";

        [Required]
        public string? LanguageCode { get; set; }  //The language code of the media

        public MediaType MediaType { get; set; } = MediaType.Image; //Calculated based on file extension
        public ContentCategory ContentCategory { get; set; } = ContentCategory.Thumbnail;
        public DateTime? DeliveryDate { get; set; }
        public string BucketName { get; set; } = "";
        public long? Size { get; set; }
        public int Views { get; set; } = 0;
        public string? YoutubeId { get; set; }
        public string Environment { get; set; } = "";
    }
}