import { CustomAuraPreset, GreenModePreset } from '@/theme.config';
import { Injectable, signal, computed, inject } from '@angular/core';
import { PrimeNG } from 'primeng/config';

export type ThemeMode = 'default' | 'green' | 'dark';

export interface ThemeConfig {
  mode: ThemeMode;
  label: string;
  color?: string;
  className: string;
  preset?: any;
}

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  private primeng = inject(PrimeNG);
  themes: ThemeConfig[] = [
    {
      mode: 'default',
      label: '默认主题',
      color: '#FFF5EB',
      className: 'app-default',
      preset: CustomAuraPreset,
    },
    {
      mode: 'green',
      label: '绿色主题',
      color: '#C9E0CB',
      className: 'app-green',
      preset: GreenModePreset,
    },
    {
      mode: 'dark',
      label: '暗黑主题',
      color: '#333333',
      className: 'app-dark',
      preset: CustomAuraPreset,
    },
  ];
  theme = signal(this.getStoredMode() || 'default');
  currentThemeInfo = computed(() => {
    return this.themes.find((t) => t.mode === this.theme()) || this.themes[0];
  });
  constructor() {
    this.setTheme(this.theme());
  }
  setTheme(mode: ThemeMode) {
    const theme = this.themes.find((t) => t.mode === mode);
    if (!theme) return;

    // 移除所有主题类
    this.themes.forEach((t) => {
      if (t.className) {
        document.documentElement.classList.remove(t.className);
      }
    });

    // 添加新主题类
    if (theme.className) {
      document.documentElement.classList.add(theme.className);
    }

    // 如果有对应的 preset，更新 PrimeNG 主题
    if (theme.preset) {
      this.primeng.theme.set({
        preset: theme.preset,
        options: {
          darkModeSelector: `.app-dark`,
        },
      });
    }

    this.theme.set(mode);
    localStorage.setItem('themeMode', mode);
  }

  private getStoredMode(): ThemeMode | null {
    const stored = localStorage.getItem('themeMode') as ThemeMode;
    return this.themes.some((t) => t.mode === stored) ? stored : null;
  }
}
