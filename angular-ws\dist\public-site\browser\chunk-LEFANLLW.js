import{a as p}from"./chunk-T2K2OPF3.js";var r=(i=>(i[i.Audio=0]="Audio",i[i.Video=1]="Video",i))(r||{}),v=p(r);var O=(m=>(m[m.Lecture=0]="Lecture",m[m.Article=1]="Article",m[m.Remark=2]="Remark",m[m.StudentArticle=3]="StudentArticle",m[m.Extract=4]="Extract",m))(O||{}),R=p(O);var f=(m=>(m[m.Unspefied=0]="Unspefied",m[m.Collection=1]="Collection",m[m.Ebook=2]="Ebook",m[m.VirtualDisk=3]="VirtualDisk",m[m.PotCast=4]="PotCast",m))(f||{}),u=p(f);var s=(o=>(o[o.Thumbnail=0]="Thumbnail",o[o.Image=1]="Image",o[o.OriginalAudio=2]="OriginalAudio",o[o.OriginalVideo=3]="OriginalVideo",o[o.Document=4]="Document",o[o.NonOriginalAudio=5]="NonOriginalAudio",o[o.NonOriginalVideo=6]="NonOriginalVideo",o[o.Package=7]="Package",o))(s||{}),z=p(s);var E=(x=>(x[x.CreationTime=0]="CreationTime",x[x.LastModificationTime=1]="LastModificationTime",x[x.DeiveryDate=2]="DeiveryDate",x))(E||{}),H=p(E);var c=(m=>(m[m.ImageCard=0]="ImageCard",m[m.SummaryCard=1]="SummaryCard",m[m.ArticleTree=8]="ArticleTree",m[m.CollectionTree=9]="CollectionTree",m[m.CollectionArticleTree=10]="CollectionArticleTree",m))(c||{}),Q=p(c);var k=(m=>(m[m.Image=0]="Image",m[m.Audio=1]="Audio",m[m.Video=2]="Video",m[m.Document=3]="Document",m[m.Unknown=4]="Unknown",m))(k||{}),Y=p(k);var V=(x=>(x[x.Draft=0]="Draft",x[x.Published=1]="Published",x[x.ContentReviewed=2]="ContentReviewed",x))(V||{}),$=p(V);
