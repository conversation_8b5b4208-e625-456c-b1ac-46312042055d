import type { AuditedEntityDto } from '@abp/ng.core';
import type { BookType } from '../book-type.enum';

export interface ChapterTreeDto {
  id: number;
  eBookId: number;
  parentChapterId?: number;
  title?: string;
  description?: string;
  weight: number;
  views: number;
  likes: number;
  isRoot: boolean;
  articleCount: number;
  children: ChapterTreeDto[];
}

export interface EBookDto extends AuditedEntityDto<number> {
  title?: string;
  description?: string;
  weight: number;
  channelId?: number;
  type?: BookType;
  thumbnailFileId?: number;
  thumbnailUrl?: string;
  deliveryDate?: string;
  views: number;
  likes: number;
  languageCode?: string;
}
