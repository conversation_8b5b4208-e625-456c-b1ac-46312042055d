import type { AuditedEntityDto, EntityDto } from '@abp/ng.core';
import type { ArticleContentCategory } from '../../enums/article-content-category.enum';
import type { PublishStatus } from '../../enums/publish-status.enum';

export interface ArticleDto extends AuditedEntityDto<number> {
  deliveryDate?: string;
  languageCode?: string;
  title?: string;
  thumbnailFileId?: number;
  thumbnailUrl?: string;
  description?: string;
  keywords?: string;
  views: number;
  likes: number;
  articleContentCategory?: ArticleContentCategory;
  status?: PublishStatus;
  content?: string;
  memo?: string;
}

export interface ArticleTitleDto extends EntityDto<number> {
  title?: string;
}
