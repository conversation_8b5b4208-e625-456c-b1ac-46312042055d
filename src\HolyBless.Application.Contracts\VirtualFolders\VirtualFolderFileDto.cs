﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Interfaces;

namespace HolyBless.VirtualFolders
{
    public class VirtualFolderFileDto : IHaveFileUrl
    {
        public int FileId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string? Title { get; set; }
        public string? FileUrl { get; set; }
    }
}