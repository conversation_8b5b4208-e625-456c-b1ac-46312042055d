﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace HolyBless.Buckets
{
    public interface ICachedFileUrlAppService : IApplicationService
    {
        Task<Dictionary<int, string>> GetCachedComputeUrlAsync(List<int> fileIds, string providerCode);

        Task<Dictionary<int, string>> GetCachedComputeUrlAsync(List<int?> fileIds, string providerCode);

        Task<string> GetCachedComputeUrlAsync(int fileId, string providerCode);

        Task<string> GetCachedComputeUrlAsync(int? fileId, string providerCode);

        void ClearAllFileUrlCaches();

        void ClearFileUrlCache(int fileId, string providerCode);

        void ClearFileUrlCachesByFileId(int fileId);

        void ClearFileUrlCachesByProviderCode(string providerCode);
    }
}