using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Enums;
using HolyBless.Lookups;
using HolyBless.StorageProviders;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Timing;

namespace HolyBless.DataSeeders
{
    public class StorageProviderDataSeederContributor : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<StorageProvider, int> _storageProviderRepository;
        private readonly IRepository<StorageBucket, int> _storageBucketRepository;
        private readonly IRepository<Country, int> _countryRepository;

        public StorageProviderDataSeederContributor(
            IRepository<StorageProvider, int> storageProviderRepository
            , IRepository<StorageBucket, int> storageBucketRepository
            , IRepository<Country, int> countryRepository
            )
        {
            _storageProviderRepository = storageProviderRepository;
            _storageBucketRepository = storageBucketRepository;
            _countryRepository = countryRepository;
        }

        private static List<Country> GetCountry(List<Country> allCountries, string[] countryCode)
        {
            return allCountries.Where(c => countryCode.Contains(c.Code)).ToList();
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            await DeleteExistingData();

            var allCountries = await _countryRepository.GetListAsync();

            var env = EnvironmentConst.Dev.ToString();
            var domain = "xieanshuo.com";

            await _storageProviderRepository.InsertAsync(new StorageProvider(1)
            {
                ProviderName = "Cloudflare R2",
                ProviderCode = ProviderCodeConstants.CloudFlare,
                PreferCountries = GetCountry(allCountries, [CountryCodeConstants.China]),
                Environment = env,
                BindedDomain = domain,
                Description = "Cloudflare R2 storage for China region."
            }, autoSave: true);

            await _storageProviderRepository.InsertAsync(new StorageProvider(2)
            {
                ProviderName = "Cloudflare R2",
                ProviderCode = ProviderCodeConstants.CloudFlare,
                PreferCountries = GetCountry(allCountries, [CountryCodeConstants.UnitedStates]),
                Environment = env,
                BindedDomain = domain,
                Description = "Cloudflare R2 storage for US region and all other region outside China."
            }, autoSave: true);

            await _storageProviderRepository.InsertAsync(new StorageProvider(3)
            {
                ProviderName = "Cloudflare R2",
                ProviderCode = ProviderCodeConstants.CloudFlare,
                PreferCountries = GetCountry(allCountries, [CountryCodeConstants.HongKong, CountryCodeConstants.Macao]),
                Environment = env,
                BindedDomain = domain,
                Description = "Cloudflare R2 storage for HongKong and Macao region."
            }, autoSave: true);

            await _storageProviderRepository.InsertAsync(new StorageProvider(4)
            {
                ProviderName = "Cloudflare R2",
                ProviderCode = ProviderCodeConstants.CloudFlare,
                PreferCountries = GetCountry(allCountries, [CountryCodeConstants.Taiwan]),
                Environment = env,
                BindedDomain = domain,
                Description = "Cloudflare R2 storage for Taiwan region."
            }, autoSave: true);

            var allProviders = await _storageProviderRepository.GetListAsync();
            int bucketIdStart = 1;
            foreach (var provider in allProviders)
            {
                var country = provider.PreferCountries.FirstOrDefault();
                if (country == null)
                {
                    country = allCountries.First(x => x.Code == "US");
                }
                if (string.IsNullOrWhiteSpace(country.DefaultLangCode))
                {
                    country.DefaultLangCode = LangCode.English;
                }
                if (string.IsNullOrWhiteSpace(country.DefaultSpokenLangCode))
                {
                    country.DefaultSpokenLangCode = SpokenLangCode.English;
                }
                var inserted = await TaskSeedBucket(bucketIdStart, provider.Id, country.DefaultLangCode, country.DefaultSpokenLangCode);
                bucketIdStart += inserted;
            }
        }

        private async Task<int> TaskSeedBucket(int idStart, int providerId, string langCode, string spokenLangCode)
        {
            var buckets = new List<StorageBucket>();

            switch (langCode)
            {
                case LangCode.SimplifiedChinese:
                    buckets.Add(new StorageBucket(idStart++)
                    {
                        StorageProviderId = providerId,
                        BucketName = $"prod-img-zh-hans",
                        Description = $"images-{langCode}",
                        LanguageCode = langCode,
                        SpokenLangCode = null,
                        SubDomain = "prod-cf-img-zh-hans",
                        ContentType = ContentCategory.Image
                    });
                    buckets.Add(new StorageBucket(idStart++)
                    {
                        StorageProviderId = providerId,
                        BucketName = $"prod-thumb-zh-hans",
                        Description = $"thumbnail-{langCode}",
                        LanguageCode = langCode,
                        SpokenLangCode = null,
                        SubDomain = "prod-cf-thumb-zh-hans",
                        ContentType = ContentCategory.Thumbnail
                    });
                    buckets.Add(new StorageBucket(idStart++)
                    {
                        StorageProviderId = providerId,
                        BucketName = $"docs-zh-hans",
                        Description = $"docs-{langCode}",
                        LanguageCode = langCode,
                        SpokenLangCode = null,
                        SubDomain = "docs-zh-hans",
                        ContentType = ContentCategory.Document
                    });
                    break;

                case LangCode.TraditionalChinese:
                    buckets.Add(new StorageBucket(idStart++)
                    {
                        StorageProviderId = providerId,
                        BucketName = $"prod-img-zh-hant",
                        Description = $"images-{langCode}",
                        LanguageCode = langCode,
                        SpokenLangCode = null,
                        SubDomain = "prod-cf-img-zh-hant",
                        ContentType = ContentCategory.Image
                    });
                    buckets.Add(new StorageBucket(idStart++)
                    {
                        StorageProviderId = providerId,
                        BucketName = $"prod-thumb-zh-hant",
                        Description = $"thumbnail-{langCode}",
                        LanguageCode = langCode,
                        SpokenLangCode = null,
                        SubDomain = "prod-cf-thumb-zh-hant",
                        ContentType = ContentCategory.Thumbnail
                    });
                    buckets.Add(new StorageBucket(idStart++)
                    {
                        StorageProviderId = providerId,
                        BucketName = $"docs-zh-hant",
                        Description = $"docs-{langCode}",
                        LanguageCode = langCode,
                        SpokenLangCode = null,
                        SubDomain = "docs-zh-hant",
                        ContentType = ContentCategory.Document
                    });
                    break;

                case LangCode.English:
                    //This bucket doesn't exist in CF
                    buckets.Add(new StorageBucket(idStart++)
                    {
                        StorageProviderId = providerId,
                        BucketName = $"prod-img-en",
                        Description = $"images-{langCode}",
                        LanguageCode = langCode,
                        SpokenLangCode = null,
                        SubDomain = "prod-cf-img-en",
                        ContentType = ContentCategory.Image
                    });
                    //This bucket doesn't exist in CF
                    buckets.Add(new StorageBucket(idStart++)
                    {
                        StorageProviderId = providerId,
                        BucketName = $"prod-thumb-en",
                        Description = $"thumbnail-{langCode}",
                        LanguageCode = langCode,
                        SpokenLangCode = null,
                        SubDomain = "prod-cf-thumb-en",
                        ContentType = ContentCategory.Thumbnail
                    });
                    //This bucket doesn't exist in CF
                    buckets.Add(new StorageBucket(idStart++)
                    {
                        StorageProviderId = providerId,
                        BucketName = $"docs-en",
                        Description = $"docs-{langCode}",
                        LanguageCode = langCode,
                        SpokenLangCode = null,
                        SubDomain = "docs-en",
                        ContentType = ContentCategory.Document
                    });
                    break;
            }
            if (langCode == LangCode.SimplifiedChinese && spokenLangCode == SpokenLangCode.Mandarin)
            {
                buckets.Add(new StorageBucket(idStart++)
                {
                    StorageProviderId = providerId,
                    BucketName = "prod-ogvideo-zh-hans-cmn",
                    Description = $"original-video-{langCode}-{spokenLangCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "prod-cf-ogvideo-zh-hans-cmn",
                    ContentType = ContentCategory.OriginalVideo
                });

                buckets.Add(new StorageBucket(idStart++)
                {
                    StorageProviderId = providerId,
                    BucketName = "prod-notogvideo-zh-hans-cmn",
                    Description = $"non-original-video-{langCode}-{spokenLangCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "prod-cf-notogvideo-zh-hans-cmn",
                    ContentType = ContentCategory.NonOriginalVideo
                });
            }
            if (langCode == LangCode.TraditionalChinese && spokenLangCode == SpokenLangCode.Mandarin)
            {
                buckets.Add(new StorageBucket(idStart++)
                {
                    StorageProviderId = providerId,
                    BucketName = "prod-ogvideo-zh-hant-cmn",
                    Description = $"original-video-{langCode}-{spokenLangCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "prod-cf-ogvideo-zh-hant-cmn",
                    ContentType = ContentCategory.OriginalVideo
                });
            }
            //This bucket doesn't exist in CF
            if (langCode == LangCode.SimplifiedChinese && spokenLangCode == SpokenLangCode.Cantonese)
            {
                buckets.Add(new StorageBucket(idStart++)
                {
                    StorageProviderId = providerId,
                    BucketName = "prod-notogvideo-zh-hans-yue",
                    Description = $"non-original-video-{langCode}-{spokenLangCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "vyue",
                    ContentType = ContentCategory.NonOriginalVideo
                });
            }
            if (langCode == LangCode.TraditionalChinese && spokenLangCode == SpokenLangCode.Cantonese)
            {
                buckets.Add(new StorageBucket(idStart++)
                {
                    StorageProviderId = providerId,
                    BucketName = "prod-notogvideo-zh-hant-yue",
                    Description = $"non-original-video-{langCode}-{spokenLangCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "vyue",
                    ContentType = ContentCategory.NonOriginalVideo
                });
            }
            if (langCode == LangCode.English && spokenLangCode == SpokenLangCode.English)
            {
                buckets.Add(new StorageBucket(idStart++)
                {
                    StorageProviderId = providerId,
                    BucketName = $"prod-notogvideo-en-eng",
                    Description = $"non-original-video-{langCode}-{spokenLangCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "prod-cf-notogvideo-en-eng",
                    ContentType = ContentCategory.NonOriginalVideo
                });
            }
            if (spokenLangCode == SpokenLangCode.Mandarin)
            {
                buckets.Add(new StorageBucket(idStart++)
                {
                    StorageProviderId = providerId,
                    BucketName = $"prod-ogaudio-cmn",
                    Description = $"original-audio-{spokenLangCode}",
                    LanguageCode = null,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "prod-cf-ogaudio-cmn",
                    ContentType = ContentCategory.OriginalAudio
                });

                buckets.Add(new StorageBucket(idStart++)
                {
                    StorageProviderId = providerId,
                    BucketName = $"prod-notogaudio-cmn",
                    Description = $"non-original-audio-{spokenLangCode}",
                    LanguageCode = null,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "prod-cf-notogaudio-cmn",
                    ContentType = ContentCategory.NonOriginalAudio
                });
            }
            if (spokenLangCode == SpokenLangCode.Cantonese)
            {
                buckets.Add(new StorageBucket(idStart++)
                {
                    StorageProviderId = providerId,
                    BucketName = $"prod-notogaudio-yue",
                    Description = $"non-original-audio-{spokenLangCode}",
                    LanguageCode = null,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "prod-cf-notogaudio-yue",
                    ContentType = ContentCategory.NonOriginalAudio
                });
            }
            buckets.Add(
                new StorageBucket(idStart)
                {
                    StorageProviderId = providerId,
                    BucketName = $"prod-pkg",
                    Description = $"package-{langCode}",
                    LanguageCode = null,
                    SpokenLangCode = null,
                    SubDomain = "prod-pkg",
                    ContentType = ContentCategory.Package
                }
            );

            await _storageBucketRepository.InsertManyAsync(buckets, autoSave: true);
            return buckets.Count;
        }

        private async Task DeleteExistingData()
        {
            var existingAllStorageBucket = await _storageBucketRepository.GetListAsync();
            if (await _storageBucketRepository.GetCountAsync() > 0)
            {
                await _storageBucketRepository.HardDeleteAsync(existingAllStorageBucket, true);
            }
            var existingAll = await _storageProviderRepository.GetListAsync();
            if (await _storageProviderRepository.GetCountAsync() > 0)
            {
                await _storageProviderRepository.HardDeleteAsync(existingAll, true);
            }
        }
    }
}