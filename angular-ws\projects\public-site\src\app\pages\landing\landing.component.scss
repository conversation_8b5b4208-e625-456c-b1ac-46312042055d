.hero-section {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  text-align: center;
  color: white;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.section-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 2rem;
  text-align: center;
  color: var(--primary-color);
}

.collection-header {
  margin-bottom: 1.5rem;
}

.collection-title {
  position: relative;
  font-size: 1.5rem;
  font-weight: 600;
  padding: 0.75rem 1rem 0.75rem 1.5rem;
  border-radius: 4px;
}

.collection-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--p-blue-600);
  border-radius: 2px 0 0 2px;
}

.miracle-card {
  height: 200px;
}

.card-description {
  margin-bottom: 1rem;
  color: var(--p-text-color-secondary);
}

.creation-time-tag {
  margin-top: auto;
}

.galleria-item {
  width: 100%;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.galleria-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
}

.galleria-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  color: white;
  padding: 1rem;
  text-align: center;
}

.tianmenkai-item {
  text-align: center;
  margin-bottom: 1rem;
}

.tianmenkai-cover {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.tianmenkai-title {
  font-size: 0.9rem;
  font-weight: 500;
  display: block;
}

/* 等高卡片容器 */
.equal-height-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  align-items: stretch;
}

@media (max-width: 768px) {
  .equal-height-cards {
    grid-template-columns: 1fr;
  }
}

.card-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-wrapper ::ng-deep p-card {
  height: 100%;
}

/* 强制 PrimeNG Card 组件等高 */
.card-wrapper ::ng-deep .p-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: box-shadow 0.3s ease;
}

.card-wrapper ::ng-deep .p-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-wrapper ::ng-deep .p-card .p-card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--surface-border);
  background-color: var(--surface-ground);
  border-radius: 8px 8px 0 0;
}

.card-wrapper ::ng-deep .p-card .p-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.card-wrapper ::ng-deep .p-card .p-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
}

/* 榜单内容区域 */
.ranking-list, .notice-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--surface-border);
}

.rank-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 1rem;
}

.rank-title {
  flex: 1;
}

.rank-score {
  font-weight: 500;
}

.notice-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--surface-border);
}

.notice-tag {
  margin-right: 1rem;
}

.notice-title {
  flex: 1;
}

.notice-date {
  font-size: 0.8rem;
}

.fixed-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.content-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
}

.content-text {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  color: var(--text-color-secondary);
}

.footer-section {
  background-color: var(--p-form-field-background);
  margin-top: 3rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.footer-column {
  margin-bottom: 1.5rem;
}

.footer-title {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: var(--text-color-secondary);
  text-decoration: none;
  transition: color 0.2s;
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  border-top: 1px solid var(--surface-border);
  padding-top: 1rem;
  margin-top: 2rem;
  text-align: center;
  color: var(--text-color-secondary);
}
