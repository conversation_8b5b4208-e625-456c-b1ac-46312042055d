import type { VirtualFolderTreeDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyVirtualFolderService {
  apiName = 'Default';
  

  getFolderTreeWithFiles = (rootFolderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VirtualFolderTreeDto>({
      method: 'GET',
      url: `/api/app/read-only-virtual-folder/folder-tree-with-files/${rootFolderId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
