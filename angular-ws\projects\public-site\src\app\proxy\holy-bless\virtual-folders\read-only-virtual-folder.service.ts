import type { VirtualFolderDto, VirtualFolderFileDto, VirtualFolderFileSearchDto, VirtualFolderSearchDto, VirtualFolderTreeDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyVirtualFolderService {
  apiName = 'Default';
  

  getFolderFiles = (input: VirtualFolderFileSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<VirtualFolderFileDto>>({
      method: 'GET',
      url: '/api/app/read-only-virtual-folder/folder-files',
      params: { folderId: input.folderId, contentCode: input.contentCode, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: VirtualFolderSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<VirtualFolderDto>>({
      method: 'GET',
      url: '/api/app/read-only-virtual-folder',
      params: { channelId: input.channelId, contentCode: input.contentCode, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getVirtualFolderTree = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VirtualFolderTreeDto[]>({
      method: 'GET',
      url: '/api/app/read-only-virtual-folder/virtual-folder-tree',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getVirtualFolderTree = (folderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VirtualFolderTreeDto[]>({
      method: 'GET',
      url: `/api/app/read-only-virtual-folder/virtual-folder-tree/${folderId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
