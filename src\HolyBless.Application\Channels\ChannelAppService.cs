using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Channels.Dtos;
using HolyBless.Entities.Channels;
using HolyBless.Permissions;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Channels
{
    [Authorize(HolyBlessPermissions.Channels.Default)]
    public class ChannelAppService : ReadOnlyChannelAppService, IChannelAppService
    {
        public ChannelAppService(IRepository<Channel, int> repository,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService
            )
            : base(repository, requestContextService, cachedFileUrlAppService)
        {
        }

        [Authorize(HolyBlessPermissions.Channels.Create)]
        public async Task<ChannelDto> CreateAsync(CreateUpdateChannelDto input)
        {
            var channel = ObjectMapper.Map<CreateUpdateChannelDto, Channel>(input);
            channel = await _repository.InsertAsync(channel, autoSave: true);
            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        [Authorize(HolyBlessPermissions.Channels.Edit)]
        public async Task<ChannelDto> UpdateAsync(int id, CreateUpdateChannelDto input)
        {
            var channel = await _repository.GetAsync(id);
            ObjectMapper.Map(input, channel);
            channel = await _repository.UpdateAsync(channel, true);
            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        [Authorize(HolyBlessPermissions.Channels.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id, true);
        }
    }
}