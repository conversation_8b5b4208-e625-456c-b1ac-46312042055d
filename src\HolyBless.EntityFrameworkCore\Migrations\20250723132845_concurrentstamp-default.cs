﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class concurrentstampdefault : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "VirtualDiskFolderTrees",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "VirtualDiskFolders",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Tags",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "StorageProviders",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "StorageBuckets",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "ProviderSecrets",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "EBooks",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Countries",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Collections",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Chapters",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Channels",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "BucketFiles",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Articles",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "ArticleFiles",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Albums",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                defaultValueSql: "gen_random_uuid()::text",
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "VirtualDiskFolderTrees",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "VirtualDiskFolders",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Tags",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "StorageProviders",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "StorageBuckets",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "ProviderSecrets",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "EBooks",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Countries",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Collections",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Chapters",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Channels",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "BucketFiles",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Articles",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "ArticleFiles",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");

            migrationBuilder.AlterColumn<string>(
                name: "ConcurrencyStamp",
                table: "Albums",
                type: "character varying(40)",
                maxLength: 40,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(40)",
                oldMaxLength: 40,
                oldDefaultValueSql: "gen_random_uuid()::text");
        }
    }
}
