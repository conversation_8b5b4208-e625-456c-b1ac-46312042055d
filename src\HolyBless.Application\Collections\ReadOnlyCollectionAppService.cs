using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Buckets;
using HolyBless.Collections.Dtos;
using HolyBless.Configs;
using HolyBless.Entities.Collections;
using HolyBless.Interfaces;
using HolyBless.Results;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Collections
{
    [AllowAnonymous]
    public class ReadOnlyCollectionAppService : HolyBlessAppService, IReadOnlyCollectionAppService
    {
        protected readonly ICollectionRepository _repository;
        protected readonly AppConfig _settings;
        protected readonly IRepository<CollectionToArticle> _collectionToArticleRepository;
        protected readonly IRepository<CollectionToFile> _collectionToFileRepository;

        public ReadOnlyCollectionAppService(
            ICollectionRepository repository,
            AppConfig settings,
            IRepository<CollectionToArticle> collectionToArticleRepository,
            IRepository<CollectionToFile> collectionToFileRepository,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService
            ) : base(cachedFileUrlAppService, requestContextService)
        {
            _repository = repository;
            _settings = settings;
            _collectionToArticleRepository = collectionToArticleRepository;
            _collectionToFileRepository = collectionToFileRepository;
        }

        public async Task<CollectionDto> GetAsync(int id)
        {
            var collection = await _repository
                .FirstOrDefaultAsync(x => x.Id == id);

            Check.NotNull(collection, nameof(collection));
            var collectionDto = ObjectMapper.Map<Collection, CollectionDto>(collection);
            await FillThumbnailUrl(collectionDto);
            return collectionDto;
        }

        [RemoteService(false)]
        public async Task<PagedResultDto<CollectionDto>> GetListAsync(CollectionSearchDto input)
        {
            var queryable = await _repository.GetQueryableAsync();

            var query = queryable
                .WhereIf(input.Status.HasValue, x => x.Status == input.Status.Value)
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == input.ChannelId.Value)
                .Include(x => x.ContentCode)
                .OrderBy(input.Sorting ?? "CollectionName");

            var collections = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount).Take(input.MaxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);

            return new PagedResultDto<CollectionDto>(
                totalCount,
                ObjectMapper.Map<List<Collection>, List<CollectionDto>>(collections)
            );
        }

        [RemoteService(false)]
        public async Task<PagedResultDto<CollectionToArticleDto>> GetCollectionArticlesAsync(CollectionArticleSearchDto input)
        {
            var queryable = await _collectionToArticleRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.CollectionId == input.CollectionId)
                .WhereIf(input.Status.HasValue, x => x.Article.Status == input.Status.Value)
                .OrderBy(input.Sorting ?? nameof(CollectionToArticle.Weight));

            var items = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount).Take(input.MaxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);

            var dtos = ObjectMapper.Map<List<CollectionToArticle>, List<CollectionToArticleDto>>(items);
            return new PagedResultDto<CollectionToArticleDto>(totalCount, dtos);
        }

        [RemoteService(false)]
        public async Task<PagedResultDto<CollectionToFileDto>> GetCollectionFilesAsync(CollectionFileSearchDto input)
        {
            var queryable = await _collectionToFileRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.CollectionId == input.CollectionId)
                .OrderBy(input.Sorting ?? nameof(CollectionToFile.Weight));

            var items = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount).Take(input.MaxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);

            var dtos = ObjectMapper.Map<List<CollectionToFile>, List<CollectionToFileDto>>(items);
            return new PagedResultDto<CollectionToFileDto>(totalCount, dtos);
        }

        /// <summary>
        /// Return data for list view for ImageCard and SummaryCard style
        /// </summary>
        /// <param name="collectionId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<CollectionSummaryResult> GetCollectionSummaryAsync(int collectionId, CollectionSummaryRequest request)
        {
            var result = await _repository.GetCollectionSummaryAsync(collectionId, request);
            if (result != null)
            {
                await FillThumbnailUrls(result.Articles);
            }
            return result ?? new();
        }

        /// <summary>
        /// This overload method get collection summary by collection content code
        /// and Language Code (in request header)
        /// Usage: List Page for ImageCard and SummaryCard
        /// </summary>
        /// <param name="contentCode">Collection Content Code</param>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<CollectionSummaryResult> GetCollectionSummaryAsync(string contentCode, CollectionSummaryRequest request)
        {
            var collectionId = await GetMatchedId(contentCode);
            if (collectionId == 0) return new CollectionSummaryResult();
            return await GetCollectionSummaryAsync(collectionId, request);
        }

        private async Task<int> GetMatchedId(string contentCode)
        {
            var lang = _requestContextService!.GetLanguageCode();
            var collection = await _repository.FirstOrDefaultAsync(x => x.ContentCode == contentCode && x.LanguageCode == lang);
            if (collection == null) return 0;
            return collection.Id;
        }

        /// <summary>
        /// Return Article titles for a given collection.
        /// Usage: Collection detail page left side menu for ArticleTree style.
        /// </summary>
        /// <param name="collectionId">The ID of the collection.</param>
        /// <returns>A list of article titles.</returns>
        public async Task<List<ArticleTitleDto>> GetCollectionArticleTitlesAsync(int collectionId)
        {
            var queryable = await _collectionToArticleRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.CollectionId == collectionId)
                .OrderByDescending(x => x.Article.DeliveryDate)
                .Select(x => new ArticleTitleDto
                {
                    Id = x.Article.Id,
                    Title = x.Article.Title,
                    DeliveryDate = x.Article.DeliveryDate,
                });
            return await AsyncExecuter.ToListAsync(query);
        }

        /// <summary>
        /// This method can replace existing GetCollectionArticleTitlesAsync
        /// Get collection article titles by content code and matched language code (In request header).
        /// </summary>
        /// <param name="contentCode"></param>
        /// <returns></returns>
        public async Task<List<ArticleTitleDto>> GetCollectionArticleTitlesAsync(string contentCode)
        {
            var collectionId = await GetMatchedId(contentCode);
            if (collectionId == 0) return new List<ArticleTitleDto>();
            return await GetCollectionArticleTitlesAsync(collectionId);
        }

        /// <summary>
        /// Get the first collection by channel ID.
        /// </summary>
        /// <param name="channelId"></param>
        /// <returns></returns>
        public async Task<CollectionDto?> GetFirstByChannelIdAsync(int channelId)
        {
            var collection = await _repository
                .FirstOrDefaultAsync(x => x.ChannelId == channelId);
            if (collection == null)
            {
                return null;
            }

            var collectionDto = ObjectMapper.Map<Collection, CollectionDto>(collection);
            await FillThumbnailUrl(collectionDto);
            return collectionDto;
        }

        /// <summary>
        /// When switch language on ImageCard or SummaryCard list page
        /// Call this method to get matched CollectionDto for new language by provider current collection's content code
        /// </summary>
        /// <param name="contentCode"></param>
        /// <param name="languageCode"></param>
        /// <returns></returns>
        public async Task<CollectionDto?> GetLanguageMatchingCollectionAsync(string contentCode, string languageCode)
        {
            var queryable = await _repository.GetQueryableAsync();

            var collection = await queryable
                .Where(x => (x.LanguageCode == null || x.LanguageCode == languageCode) && x.ContentCode == contentCode)
                .FirstOrDefaultAsync();
            if (collection == null)
            {
                return null;
            }
            var rt = ObjectMapper.Map<Collection, CollectionDto>(collection);
            await FillThumbnailUrl(rt);
            return rt;
        }

        /// <summary>
        /// Get the collection tree structure for a specific collection.
        /// Usage: For List Style: CollectionTree, Collection detail page left side menu
        /// </summary>
        /// <param name="collectionId"></param>
        /// <returns></returns>
        public async Task<List<CollectionTreeDto>> GetCollectionTreeAsync(int collectionId)
        {
            // Use the optimized repository method that uses CTE for single DB call
            var allCollections = await _repository.GetAllDescendantsAsync(collectionId);

            // Convert all collections to CollectionTreeDto
            var collectionDtos = ObjectMapper.Map<List<Collection>, List<CollectionTreeDto>>(allCollections);

            // Create a dictionary for quick lookup
            var collectionDict = collectionDtos.ToDictionary(x => x.Id);

            // Build the tree structure
            var rootCollections = new List<CollectionTreeDto>();

            foreach (var collection in collectionDtos)
            {
                if (collection.ParentCollectionId.HasValue && collectionDict.TryGetValue(collection.ParentCollectionId.Value, out var parentDto))
                {
                    // Add as child to parent
                    parentDto.Children.Add(collection);
                }
                else if (collection.ParentCollectionId == collectionId)
                {
                    // Add as root collection (direct child of the specified collection)
                    collection.IsRoot = true;
                    rootCollections.Add(collection);
                }
            }

            return rootCollections;
        }

        /// <summary>
        /// Get the collection tree structure by collection content code and language code (in request header).
        /// Usage: For List Style: CollectionTree, Collection detail page left side menu
        /// </summary>
        /// <param name="contentCode"></param>
        /// <returns></returns>
        public async Task<List<CollectionTreeDto>> GetCollectionTreeAsync(string contentCode)
        {
            var collectionId = await GetMatchedId(contentCode);
            if (collectionId == 0) return new List<CollectionTreeDto>();
            return await GetCollectionTreeAsync(collectionId);
        }

        /// <summary>
        /// Usage: For List Style: CollectionArticleTree, Collection detail page left side menu
        /// </summary>
        /// <param name="collectionId"></param>
        /// <returns></returns>
        public async Task<List<CollectionArticleTreeDto>> GetCollectionTreeAndArticleTitlesAsync(int collectionId)
        {
            // Get the collection tree structure
            var allCollections = await _repository.GetAllDescendantsAsync(collectionId);

            // Convert all collections to CollectionArticleTreeDto
            var collectionDtos = ObjectMapper.Map<List<Collection>, List<CollectionArticleTreeDto>>(allCollections);

            // Create a dictionary for quick lookup
            var collectionDict = collectionDtos.ToDictionary(x => x.Id);

            // Build the tree structure
            var rootCollections = new List<CollectionArticleTreeDto>();

            foreach (var collection in collectionDtos)
            {
                if (collection.ParentCollectionId.HasValue && collectionDict.TryGetValue(collection.ParentCollectionId.Value, out var parentDto))
                {
                    // Add as child to parent
                    parentDto.Children.Add(collection);
                }
                else if (collection.ParentCollectionId == collectionId)
                {
                    // Add as root collection (direct child of the specified collection)
                    collection.IsRoot = true;
                    rootCollections.Add(collection);
                }
            }

            // Now populate articles for leaf collections (collections with no children)
            var leafCollections = collectionDtos.Where(c => c.Children.Count == 0).ToList();

            if (leafCollections.Any())
            {
                // Get all articles for leaf collections
                var collectionToArticleQueryable = await _collectionToArticleRepository.GetQueryableAsync();
                var articlesWithCollectionId = await AsyncExecuter.ToListAsync(
                    collectionToArticleQueryable
                        .Where(x => leafCollections.Select(lc => lc.Id).Contains(x.CollectionId))
                        .OrderByDescending(x => x.Article.DeliveryDate)
                        .Select(x => new
                        {
                            CollectionId = x.CollectionId,
                            Article = new ArticleTitleDto
                            {
                                Id = x.Article.Id,
                                Title = x.Article.Title,
                                DeliveryDate = x.Article.DeliveryDate,
                            }
                        })
                );

                // Group articles by collection and assign to respective collections
                var articlesByCollection = articlesWithCollectionId
                    .GroupBy(x => x.CollectionId)
                    .ToDictionary(g => g.Key, g => g.Select(x => x.Article).ToList());

                foreach (var leafCollection in leafCollections)
                {
                    if (articlesByCollection.TryGetValue(leafCollection.Id, out var articles))
                    {
                        leafCollection.Articles = articles;
                    }
                }
            }

            return rootCollections;
        }

        /// <summary>
        /// Get the collection tree structure and article titles by collection content code and language code (in request header).
        /// Usage: For List Style: CollectionArticleTree, Collection detail page left side menu
        /// </summary>
        /// <param name="contentCode"></param>
        /// <returns></returns>
        public async Task<List<CollectionArticleTreeDto>> GetCollectionTreeAndArticleTitlesAsync(string contentCode)
        {
            var collectionId = await GetMatchedId(contentCode);
            if (collectionId == 0) return new List<CollectionArticleTreeDto>();
            return await GetCollectionTreeAndArticleTitlesAsync(collectionId);
        }
    }
}