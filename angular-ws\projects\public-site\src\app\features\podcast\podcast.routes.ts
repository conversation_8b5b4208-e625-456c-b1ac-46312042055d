import { Routes } from '@angular/router';

export const PODCAST_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./podcast.component').then(m => m.PodcastComponent),
    title: 'HolyBless - 播客',
    children: [
      {
        path: 'album-card',
        loadComponent: () => import('./album-card/album-card').then(m => m.AlbumCardComponent),
        title: 'HolyBless - 播客专辑'
      },
      {
        path: 'album-detail',
        loadComponent: () => import('./album-detail/album-detail').then(m => m.AlbumDetailComponent),
        title: 'HolyBless - 播客专辑详情'
      },
    ]
  },
  // {
  //   path: 'subscriptions',
  //   loadComponent: () => import('./subscriptions/subscriptions.component').then(m => m.SubscriptionsComponent),
  //   title: 'HolyBless - 我的订阅'
  // }
];
