import { Component, HostListener, inject } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';
import { CalendarModule } from 'primeng/calendar';
import { ButtonModule } from 'primeng/button';
import { FormsModule } from '@angular/forms';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ActivatedRoute, Router } from '@angular/router';
import { ArticleSummaryResult } from '@/proxy/holy-bless/results';
import { TranslatePipe } from '@/pipes/translate.pipe';
import { I18nService } from '@/services/i18n.service';

@Component({
  selector: 'app-image-cards',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    PaginatorModule,
    CalendarModule,
    ButtonModule,
    FormsModule,
  ],
  providers: [DatePipe],
  templateUrl: './image-cards.component.html',
  styleUrls: ['./image-cards.component.scss'],
})
export class ImageCardsComponent {
  i18nService = inject(I18nService);
  // 分页相关属性
  totalRecords = 0;
  rows = 10;
  first = 0;
  isMobile = false;

  selectedDate: Date | null = null;
  contentCode: string | null = null;

  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #route = inject(ActivatedRoute);
  router = inject(Router);

  cardItems: ArticleSummaryResult[] = [];

  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      this.contentCode = params['contentCode'];
      this.loadCollectionSummary();
    });
  }
  private loadCollectionSummary() {
    if (!this.contentCode) return;

    this.#ReadOnlyCollectionService
      .getCollectionSummary(this.contentCode, {
        skip: this.first,
        maxResultCount: this.rows,
        year: this.selectedDate?.getFullYear(),
        month: (this.selectedDate?.getMonth() || 0) + 1,
      })
      .subscribe({
        next: (data) => {
          this.cardItems = data.articles;
          this.totalRecords = data.totalRecords;
        },
        error: (error) => {
          console.error('获取摘要数据失败:', error);
        },
      });
  }

  navigateToArticle(articleId: number) {
    this.router.navigateByUrl(`/home/<USER>
  }

  onPageChange(event: any) {
    this.first = event.first;
    this.rows = event.rows;
    this.loadCollectionSummary();
  }

  onDateChange(event: Date | null) {
    this.selectedDate = event;
    this.loadCollectionSummary();
  }

  constructor() {
    this.checkMobile();
  }

  // 根据设备类型返回每页条数选项
  get rowsPerPageOptions(): number[] | undefined {
    return this.isMobile ? undefined : [10];
  }

  // 监听窗口大小变化
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkMobile();
  }

  private checkMobile() {
    this.isMobile = window.innerWidth <= 768;
  }

  // 播放本页
  playCurrentPage() {}
}
