<div class="flex flex-col p-6">
  <!-- 卡片网格容器 -->
  <div class="collection-header">
    <h3 class="collection-title">Audio</h3>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    @for (item of cardItems; track item.id) {
    <p-card styleClass="card-item" [id]="'card-' + item.id">
      <ng-template pTemplate="header">
        <img
          class="rounded-t-xl w-56"
          [src]="item.thumbnailUrl"
          [alt]="item.title + ' 封面图片'"
        />
      </ng-template>
      <p class="p-card-subtitle text-sm">
        {{ "2023-01-01" | date: 'yyyy-MM-dd' }}
      </p>
      <p class="p-card-title mt-2 cursor-pointer" (click)="openBook(item)">{{ item.title }}</p>
    </p-card>
    }
  </div>

  <!-- 卡片网格容器 -->
  <div class="collection-header">
    <h3 class="collection-title">Audio</h3>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    @for (item of cardItems; track item.id) {
    <p-card styleClass="card-item" [id]="'card-' + item.id">
      <ng-template pTemplate="header">
        <img
          class="rounded-t-xl w-56"
          [src]="item.thumbnailUrl"
          [alt]="item.title + ' 封面图片'"
        />
      </ng-template>
      <p class="p-card-subtitle text-sm">
        {{ "2023-01-01" | date: 'yyyy-MM-dd' }}
      </p>
      <p class="p-card-title mt-2">{{ item.title }}</p>
    </p-card>
    }
  </div>

  <!-- 分页组件 -->
  <div class="pagination-container">
    <p-paginator
      [first]="first"
      [rows]="rows"
      [totalRecords]="totalRecords"
      [rowsPerPageOptions]="rowsPerPageOptions"
      [showPageLinks]="!isMobile"
      [showCurrentPageReport]="isMobile"
      (onPageChange)="onPageChange($event)"
      styleClass="custom-paginator"
    >
    </p-paginator>
  </div>
</div>
