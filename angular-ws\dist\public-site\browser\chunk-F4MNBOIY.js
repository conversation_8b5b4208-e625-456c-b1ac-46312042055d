import{a as re}from"./chunk-2WHDSBNT.js";import{d as R}from"./chunk-5G3J65ZF.js";import{S as ae,X as ve,Z as ye,_ as V,ca as Ce,ea as E,ga as ce,ha as Ae,q as D,r as oe,s as M}from"./chunk-LS3LVTXN.js";import{c as ie,d as $,f as O,g as L,h as K}from"./chunk-BMA7WWEI.js";import{j as ee,l as te,m as be,n as ne,q as H}from"./chunk-D6WDCTDG.js";import{$b as h,Ab as S,Bb as X,Hb as U,Ib as d,Jb as F,Kb as T,Nb as _,Pb as m,Q as v,Qb as b,R as se,Ra as s,S as le,Sb as ge,Ub as _e,X as u,Za as J,Zb as Y,ab as x,bb as he,eb as I,fb as ue,ga as y,gb as l,ma as k,mb as p,nb as c,ob as fe,pa as Q,pb as C,qb as W,ra as pe,rb as A,sb as w,uc as Z,vc as me,wb as N,xb as j,xc as g,yb as f,zb as P}from"./chunk-BL4EGCPV.js";var G=["*"],we=["toggleicon"],Pe=t=>({active:t});function Se(t,r){}function Fe(t,r){t&1&&l(0,Se,0,0,"ng-template")}function He(t,r){if(t&1&&l(0,Fe,1,0,null,0),t&2){let e=d();c("ngTemplateOutlet",e.toggleicon)("ngTemplateOutletContext",h(2,Pe,e.active()))}}function De(t,r){if(t&1&&f(0,"span",4),t&2){let e=d(3);A(e.pcAccordion.collapseIcon),c("ngClass",e.pcAccordion.iconClass),p("aria-hidden",!0)}}function Ee(t,r){if(t&1&&f(0,"ChevronDownIcon",4),t&2){let e=d(3);c("ngClass",e.pcAccordion.iconClass),p("aria-hidden",!0)}}function ke(t,r){if(t&1&&(P(0),l(1,De,1,4,"span",2)(2,Ee,1,2,"ChevronDownIcon",3),S()),t&2){let e=d(2);s(),c("ngIf",e.pcAccordion.collapseIcon),s(),c("ngIf",!e.pcAccordion.collapseIcon)}}function $e(t,r){if(t&1&&f(0,"span",4),t&2){let e=d(3);A(e.pcAccordion.expandIcon),c("ngClass",e.pcAccordion.iconClass),p("aria-hidden",!0)}}function Oe(t,r){if(t&1&&f(0,"ChevronUpIcon",4),t&2){let e=d(3);c("ngClass",e.pcAccordion.iconClass),p("aria-hidden",!0)}}function Le(t,r){if(t&1&&(P(0),l(1,$e,1,4,"span",2)(2,Oe,1,2,"ChevronUpIcon",3),S()),t&2){let e=d(2);s(),c("ngIf",e.pcAccordion.expandIcon),s(),c("ngIf",!e.pcAccordion.expandIcon)}}function Ke(t,r){if(t&1&&l(0,ke,3,2,"ng-container",1)(1,Le,3,2,"ng-container",1),t&2){let e=d();c("ngIf",e.active()),s(),c("ngIf",!e.active())}}var q=t=>({transitionParams:t}),Te=t=>({value:"visible",params:t}),xe=t=>({value:"hidden",params:t}),Me=["header"],Be=["icon"],Qe=["content"],Ne=["*",[["p-header"]]],je=["*","p-header"],Ue=t=>({$implicit:t});function Ve(t,r){if(t&1&&ge(0),t&2){let e=d();_e(" ",e.header," ")}}function Re(t,r){t&1&&X(0)}function qe(t,r){if(t&1&&l(0,Re,1,0,"ng-container",4),t&2){let e=d(2);c("ngTemplateOutlet",e.headerTemplate||e._headerTemplate)}}function ze(t,r){t&1&&T(0,1)}function Ge(t,r){if(t&1&&l(0,qe,1,1,"ng-container")(1,ze,1,0),t&2){let e=d();w(e.headerTemplate||e._headerTemplate?0:-1),s(),w(e.headerFacet?1:-1)}}function Je(t,r){}function We(t,r){t&1&&l(0,Je,0,0,"ng-template")}function Xe(t,r){if(t&1&&l(0,We,1,0,null,5),t&2){let e=d();c("ngTemplateOutlet",e.iconTemplate||e._iconTemplate)("ngTemplateOutletContext",h(2,Ue,e.selected))}}function Ye(t,r){if(t&1&&f(0,"span",8),t&2){let e=d(3);A(e.accordion.collapseIcon),c("ngClass",e.iconClass),p("aria-hidden",!0)}}function Ze(t,r){if(t&1&&f(0,"ChevronDownIcon",8),t&2){let e=d(3);c("ngClass",e.iconClass),p("aria-hidden",!0)}}function et(t,r){if(t&1&&(P(0),l(1,Ye,1,4,"span",6)(2,Ze,1,2,"ChevronDownIcon",7),S()),t&2){let e=d(2);s(),c("ngIf",e.accordion.collapseIcon),s(),c("ngIf",!e.accordion.collapseIcon)}}function tt(t,r){if(t&1&&f(0,"span",8),t&2){let e=d(3);A(e.accordion.expandIcon),c("ngClass",e.iconClass),p("aria-hidden",!0)}}function nt(t,r){if(t&1&&f(0,"ChevronUpIcon",8),t&2){let e=d(3);c("ngClass",e.iconClass),p("aria-hidden",!0)}}function it(t,r){if(t&1&&(P(0),l(1,tt,1,4,"span",6)(2,nt,1,2,"ChevronUpIcon",7),S()),t&2){let e=d(2);s(),c("ngIf",e.accordion.expandIcon),s(),c("ngIf",!e.accordion.expandIcon)}}function ot(t,r){if(t&1&&l(0,et,3,2,"ng-container",3)(1,it,3,2,"ng-container",3),t&2){let e=d();c("ngIf",e.selected),s(),c("ngIf",!e.selected)}}function at(t,r){t&1&&X(0)}function ct(t,r){if(t&1&&(P(0),l(1,at,1,0,"ng-container",4),S()),t&2){let e=d();s(),c("ngTemplateOutlet",e.contentTemplate||e._contentTemplate)}}var rt=({dt:t})=>`
.p-accordionpanel {
    display: flex;
    flex-direction: column;
    border-style: solid;
    border-width: ${t("accordion.panel.border.width")};
    border-color: ${t("accordion.panel.border.color")};
}

.p-accordionheader {
    all: unset;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: ${t("accordion.header.padding")};
    color: ${t("accordion.header.color")};
    background: ${t("accordion.header.background")};
    border-style: solid;
    border-width: ${t("accordion.header.border.width")};
    border-color: ${t("accordion.header.border.color")};
    font-weight: ${t("accordion.header.font.weight")};
    border-radius: ${t("accordion.header.border.radius")};
    transition: background ${t("accordion.transition.duration")}; color ${t("accordion.transition.duration")}color ${t("accordion.transition.duration")}, outline-color ${t("accordion.transition.duration")}, box-shadow ${t("accordion.transition.duration")};
    outline-color: transparent;
    position: relative;
    overflow: hidden;
}

.p-accordionpanel:first-child > .p-accordionheader {
    border-width: ${t("accordion.header.first.border.width")};
    border-start-start-radius: ${t("accordion.header.first.top.border.radius")};
    border-start-end-radius: ${t("accordion.header.first.top.border.radius")};
}

.p-accordionpanel:last-child > .p-accordionheader {
    border-end-start-radius: ${t("accordion.header.last.bottom.border.radius")};
    border-end-end-radius: ${t("accordion.header.last.bottom.border.radius")};
}

.p-accordionpanel:last-child.p-accordionpanel-active > .p-accordionheader {
    border-end-start-radius: ${t("accordion.header.last.active.bottom.border.radius")};
    border-end-end-radius:${t("accordion.header.last.active.bottom.border.radius")};
}

.p-accordionheader-toggle-icon {
    color: ${t("accordion.header.toggle.icon.color")};
}

.p-accordionpanel:not(.p-disabled) .p-accordionheader:focus-visible {
    box-shadow: ${t("accordion.header.focus.ring.shadow")};
    outline: ${t("accordion.header.focus.ring.width")} ${t("accordion.header.focus.ring.style")} ${t("accordion.header.focus.ring.color")};
    outline-offset: ${t("accordion.header.focus.ring.offset")};
}

.p-accordionpanel:not(.p-accordionpanel-active):not(.p-disabled) > .p-accordionheader:hover {
    background: ${t("accordion.header.hover.background")};
    color: ${t("accordion.header.hover.color")}
}

.p-accordionpanel:not(.p-accordionpanel-active):not(.p-disabled) .p-accordionheader:hover .p-accordionheader-toggle-icon {
    color: ${t("accordion.header.toggle.icon.hover.color")};
}

.p-accordionpanel:not(.p-disabled).p-accordionpanel-active > .p-accordionheader {
    background: ${t("accordion.header.active.background")};
    color: ${t("accordion.header.active.color")}
}

.p-accordionpanel:not(.p-disabled).p-accordionpanel-active > .p-accordionheader .p-accordionheader-toggle-icon {
    color: ${t("accordion.header.toggle.icon.active.color")};
}

.p-accordionpanel:not(.p-disabled).p-accordionpanel-active > .p-accordionheader:hover  {
    background: ${t("accordion.header.active.hover.background")};
    color: ${t("accordion.header.active.hover.color")}
}

.p-accordionpanel:not(.p-disabled).p-accordionpanel-active > .p-accordionheader:hover  .p-accordionheader-toggle-icon {
    color: ${t("accordion.header.toggle.icon.active.hover.color")};
}

.p-accordioncontent-content {
    border-style: solid;
    border-width: ${t("accordion.content.border.width")};
    border-color: ${t("accordion.content.border.color")};
    background-color: ${t("accordion.content.background")};
    color: ${t("accordion.content.color")};
    padding: ${t("accordion.content.padding")}
}

/*For PrimeNG*/

.p-accordion .p-accordioncontent {
    overflow: hidden;
}

.p-accordionpanel.p-accordioncontent:not(.ng-animating) {
    overflow: inherit;
}

.p-accordionheader-toggle-icon.icon-start {
    order: -1;
}

.p-accordionheader:has(.p-accordionheader-toggle-icon.icon-start) {
    justify-content: flex-start;
    gap: ${t("accordion.header.padding")};
}
`,dt={root:"p-accordion p-component"},z=(()=>{class t extends Ce{name="accordion";theme=rt;classes=dt;static \u0275fac=(()=>{let e;return function(n){return(e||(e=y(t)))(n||t)}})();static \u0275prov=se({token:t,factory:t.\u0275fac})}return t})();var de=(()=>{class t extends E{pcAccordion=u(v(()=>B));value=J(void 0);disabled=Q(!1,{transform:e=>R(e)});active=g(()=>this.pcAccordion.multiple()?this.valueEquals(this.pcAccordion.value(),this.value()):this.pcAccordion.value()===this.value());valueEquals(e,i){return Array.isArray(e)?e.includes(i):e===i}static \u0275fac=(()=>{let e;return function(n){return(e||(e=y(t)))(n||t)}})();static \u0275cmp=x({type:t,selectors:[["p-accordion-panel"],["p-accordionpanel"]],hostVars:9,hostBindings:function(i,n){i&2&&(p("data-pc-name","accordionpanel")("data-p-disabled",n.disabled())("data-p-active",n.active()),C("p-accordionpanel",!0)("p-accordionpanel-active",n.active())("p-disabled",n.disabled()))},inputs:{value:[1,"value"],disabled:[1,"disabled"]},outputs:{value:"valueChange"},features:[I],ngContentSelectors:G,decls:1,vars:0,template:function(i,n){i&1&&(F(),T(0))},dependencies:[H],encapsulation:2,changeDetection:0})}return t})(),st=(()=>{class t extends E{pcAccordion=u(v(()=>B));pcAccordionPanel=u(v(()=>de));id=g(()=>`${this.pcAccordion.id()}_accordionheader_${this.pcAccordionPanel.value()}`);active=g(()=>this.pcAccordionPanel.active());disabled=g(()=>this.pcAccordionPanel.disabled());ariaControls=g(()=>`${this.pcAccordion.id()}_accordioncontent_${this.pcAccordionPanel.value()}`);toggleicon;onClick(e){let i=this.active();this.changeActiveValue();let n=this.active(),o=this.pcAccordionPanel.value();!i&&n?this.pcAccordion.onOpen.emit({originalEvent:e,index:o}):i&&!n&&this.pcAccordion.onClose.emit({originalEvent:e,index:o})}onFocus(){this.pcAccordion.selectOnFocus()&&this.changeActiveValue()}onKeydown(e){switch(e.code){case"ArrowDown":this.arrowDownKey(e);break;case"ArrowUp":this.arrowUpKey(e);break;case"Home":this.onHomeKey(e);break;case"End":this.onEndKey(e);break;case"Enter":case"Space":case"NumpadEnter":this.onEnterKey(e);break;default:break}}changeActiveValue(){this.pcAccordion.updateValue(this.pcAccordionPanel.value())}findPanel(e){return e?.closest('[data-pc-name="accordionpanel"]')}findHeader(e){return D(e,'[data-pc-name="accordionheader"]')}findNextPanel(e,i=!1){let n=i?e:e.nextElementSibling;return n?M(n,"data-p-disabled")?this.findNextPanel(n):this.findHeader(n):null}findPrevPanel(e,i=!1){let n=i?e:e.previousElementSibling;return n?M(n,"data-p-disabled")?this.findPrevPanel(n):this.findHeader(n):null}findFirstPanel(){return this.findNextPanel(this.pcAccordion.el.nativeElement.firstElementChild,!0)}findLastPanel(){return this.findPrevPanel(this.pcAccordion.el.nativeElement.lastElementChild,!0)}changeFocusedPanel(e,i){oe(i)}arrowDownKey(e){let i=this.findNextPanel(this.findPanel(e.currentTarget));i?this.changeFocusedPanel(e,i):this.onHomeKey(e),e.preventDefault()}arrowUpKey(e){let i=this.findPrevPanel(this.findPanel(e.currentTarget));i?this.changeFocusedPanel(e,i):this.onEndKey(e),e.preventDefault()}onHomeKey(e){let i=this.findFirstPanel();this.changeFocusedPanel(e,i),e.preventDefault()}onEndKey(e){let i=this.findLastPanel();this.changeFocusedPanel(e,i),e.preventDefault()}onEnterKey(e){this.changeActiveValue(),e.preventDefault()}static \u0275fac=(()=>{let e;return function(n){return(e||(e=y(t)))(n||t)}})();static \u0275cmp=x({type:t,selectors:[["p-accordion-header"],["p-accordionheader"]],contentQueries:function(i,n,o){if(i&1&&_(o,we,5),i&2){let a;m(a=b())&&(n.toggleicon=a.first)}},hostVars:13,hostBindings:function(i,n){i&1&&U("click",function(a){return n.onClick(a)})("focus",function(a){return n.onFocus(a)})("keydown",function(a){return n.onKeydown(a)}),i&2&&(p("id",n.id())("aria-expanded",n.active())("aria-controls",n.ariaControls())("aria-disabled",n.disabled())("role","button")("tabindex",n.disabled()?"-1":"0")("data-p-active",n.active())("data-p-disabled",n.disabled())("data-pc-name","accordionheader"),fe("user-select","none"),C("p-accordionheader",!0))},features:[ue([Ae]),I],ngContentSelectors:G,decls:3,vars:1,consts:[[4,"ngTemplateOutlet","ngTemplateOutletContext"],[4,"ngIf"],[3,"class","ngClass",4,"ngIf"],[3,"ngClass",4,"ngIf"],[3,"ngClass"]],template:function(i,n){i&1&&(F(),T(0),l(1,He,1,4)(2,Ke,2,2)),i&2&&(s(),w(n.toggleicon?1:2))},dependencies:[H,ee,te,ne,ce,re],encapsulation:2,changeDetection:0})}return t})(),lt=(()=>{class t extends E{pcAccordion=u(v(()=>B));pcAccordionPanel=u(v(()=>de));active=g(()=>this.pcAccordionPanel.active());ariaLabelledby=g(()=>`${this.pcAccordion.id()}_accordionheader_${this.pcAccordionPanel.value()}`);id=g(()=>`${this.pcAccordion.id()}_accordioncontent_${this.pcAccordionPanel.value()}`);static \u0275fac=(()=>{let e;return function(n){return(e||(e=y(t)))(n||t)}})();static \u0275cmp=x({type:t,selectors:[["p-accordion-content"],["p-accordioncontent"]],hostVars:7,hostBindings:function(i,n){i&2&&(p("id",n.id())("role","region")("data-pc-name","accordioncontent")("data-p-active",n.active())("aria-labelledby",n.ariaLabelledby()),C("p-accordioncontent",!0))},features:[I],ngContentSelectors:G,decls:2,vars:9,consts:[[1,"p-accordioncontent-content"]],template:function(i,n){i&1&&(F(),N(0,"div",0),T(1),j()),i&2&&c("@content",n.active()?h(3,Te,h(1,q,n.pcAccordion.transitionOptions)):h(7,xe,h(5,q,n.pcAccordion.transitionOptions)))},dependencies:[H],encapsulation:2,data:{animation:[ie("content",[L("hidden",O({height:"0",paddingBottom:"0",visibility:"hidden"})),L("visible",O({height:"*",visibility:"visible"})),K("visible <=> hidden",[$("{{transitionParams}}")]),K("void => *",$(0))])]},changeDetection:0})}return t})(),Ie=(()=>{class t extends E{get hostClass(){return this.tabStyleClass}get hostStyle(){return this.tabStyle}id=ae("pn_id_");header;headerStyle;tabStyle;contentStyle;tabStyleClass;headerStyleClass;contentStyleClass;disabled;cache=!0;transitionOptions="400ms cubic-bezier(0.86, 0, 0.07, 1)";iconPos="start";get selected(){return this._selected}set selected(e){this._selected=e,this.loaded||(this._selected&&this.cache&&(this.loaded=!0),this.cd.detectChanges())}headerAriaLevel=2;selectedChange=new k;headerFacet;_selected=!1;get iconClass(){return this.iconPos==="end"?"p-accordionheader-toggle-icon icon-end":"p-accordionheader-toggle-icon icon-start"}headerTemplate;iconTemplate;contentTemplate;templates;_headerTemplate;_iconTemplate;_contentTemplate;loaded=!1;accordion=u(v(()=>B));_componentStyle=u(z);ngOnInit(){super.ngOnInit(),console.log("AccordionTab is deprecated as of v18, please use the new structure instead.")}ngAfterContentInit(){this.templates.forEach(e=>{switch(e.getType()){case"content":this._contentTemplate=e.template;break;case"header":this._headerTemplate=e.template;break;case"icon":this._iconTemplate=e.template;break;default:this._contentTemplate=e.template;break}})}toggle(e){if(this.disabled)return!1;let i=this.findTabIndex();if(this.selected)this.selected=!1,this.accordion.onClose.emit({originalEvent:e,index:i});else{if(!this.accordion.multiple())for(var n=0;n<this.accordion.tabs.length;n++)this.accordion.tabs[n].selected&&(this.accordion.tabs[n].selected=!1,this.accordion.tabs[n].selectedChange.emit(!1),this.accordion.tabs[n].cd.markForCheck());this.selected=!0,this.loaded=!0,this.accordion.onOpen.emit({originalEvent:e,index:i})}this.selectedChange.emit(this.selected),this.accordion.updateActiveIndex(),this.cd.markForCheck(),e?.preventDefault()}findTabIndex(){let e=-1;for(var i=0;i<this.accordion.tabs.length;i++)if(this.accordion.tabs[i]==this){e=i;break}return e}onKeydown(e){switch(e.code){case"Enter":case"Space":this.toggle(e),e.preventDefault();break;default:break}}getTabHeaderActionId(e){return`${e}_header_action`}getTabContentId(e){return`${e}_content`}ngOnDestroy(){this.accordion.tabs.splice(this.findTabIndex(),1),super.ngOnDestroy()}static \u0275fac=(()=>{let e;return function(n){return(e||(e=y(t)))(n||t)}})();static \u0275cmp=x({type:t,selectors:[["p-accordionTab"],["p-accordion-tab"],["p-accordiontab"]],contentQueries:function(i,n,o){if(i&1&&(_(o,Me,4),_(o,Be,4),_(o,Qe,4),_(o,ve,4),_(o,ye,4)),i&2){let a;m(a=b())&&(n.headerTemplate=a.first),m(a=b())&&(n.iconTemplate=a.first),m(a=b())&&(n.contentTemplate=a.first),m(a=b())&&(n.headerFacet=a),m(a=b())&&(n.templates=a)}},hostVars:9,hostBindings:function(i,n){i&2&&(p("data-pc-name","accordiontab"),W(n.hostStyle),A(n.hostClass),C("p-accordionpanel",!0)("p-accordionpanel-active",n.selected))},inputs:{id:"id",header:"header",headerStyle:"headerStyle",tabStyle:"tabStyle",contentStyle:"contentStyle",tabStyleClass:"tabStyleClass",headerStyleClass:"headerStyleClass",contentStyleClass:"contentStyleClass",disabled:[2,"disabled","disabled",Z],cache:[2,"cache","cache",Z],transitionOptions:"transitionOptions",iconPos:"iconPos",selected:"selected",headerAriaLevel:[2,"headerAriaLevel","headerAriaLevel",me]},outputs:{selectedChange:"selectedChange"},features:[Y([z]),I],ngContentSelectors:je,decls:9,vars:30,consts:[["type","button",1,"p-accordionheader",3,"click","keydown","disabled","ngClass","ngStyle"],["role","region",1,"p-accordioncontent"],[1,"p-accordioncontent-content",3,"ngClass","ngStyle"],[4,"ngIf"],[4,"ngTemplateOutlet"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"class","ngClass",4,"ngIf"],[3,"ngClass",4,"ngIf"],[3,"ngClass"]],template:function(i,n){i&1&&(F(Ne),N(0,"button",0),U("click",function(a){return n.toggle(a)})("keydown",function(a){return n.onKeydown(a)}),l(1,Ve,1,1)(2,Ge,2,2)(3,Xe,1,4)(4,ot,2,2),j(),N(5,"div",1)(6,"div",2),T(7),l(8,ct,2,1,"ng-container",3),j()()),i&2&&(C("p-disabled",n.disabled),c("disabled",n.disabled)("ngClass",n.headerStyleClass)("ngStyle",n.headerStyle),p("aria-expanded",n.selected)("aria-level",n.headerAriaLevel)("data-p-disabled",n.disabled)("data-pc-section","accordionheader")("tabindex",n.disabled?null:0)("id",n.getTabHeaderActionId(n.id))("aria-controls",n.getTabContentId(n.id)),s(),w(!n.headerTemplate&&!n._headerTemplate?1:2),s(2),w(n.iconTemplate||n._iconTemplate?3:4),s(2),c("@tabContent",n.selected?h(24,Te,h(22,q,n.transitionOptions)):h(28,xe,h(26,q,n.transitionOptions))),p("id",n.getTabContentId(n.id))("aria-hidden",!n.selected)("aria-labelledby",n.getTabHeaderActionId(n.id))("data-pc-section","toggleablecontent"),s(),c("ngClass",n.contentStyleClass)("ngStyle",n.contentStyle),s(2),c("ngIf",(n.contentTemplate||n._contentTemplate)&&(n.cache?n.loaded:n.selected)))},dependencies:[H,ee,te,ne,be,ce,re],encapsulation:2,data:{animation:[ie("tabContent",[L("hidden",O({height:"0",visibility:"hidden"})),L("visible",O({height:"*",visibility:"visible"})),K("visible <=> hidden",[$("{{transitionParams}}")]),K("void => *",$(0))])]},changeDetection:0})}return t})(),B=(()=>{class t extends E{get hostClass(){return this.styleClass}get hostStyle(){return this.style}value=J(void 0);multiple=Q(!1,{transform:e=>R(e)});style;styleClass;expandIcon;collapseIcon;selectOnFocus=Q(!1,{transform:e=>R(e)});set activeIndex(e){if(this._activeIndex=e,this.preventActiveIndexPropagation){this.preventActiveIndexPropagation=!1;return}this.updateSelectionState()}transitionOptions="400ms cubic-bezier(0.86, 0, 0.07, 1)";activeIndexChange=new k;set headerAriaLevel(e){typeof e=="number"&&e>0?this._headerAriaLevel=e:this._headerAriaLevel!==2&&(this._headerAriaLevel=2)}onClose=new k;onOpen=new k;id=pe(ae("pn_id_"));tabList;tabListSubscription=null;_activeIndex;_headerAriaLevel=2;preventActiveIndexPropagation=!1;tabs=[];_componentStyle=u(z);get activeIndex(){return this._activeIndex}get headerAriaLevel(){return this._headerAriaLevel}onKeydown(e){switch(e.code){case"ArrowDown":this.onTabArrowDownKey(e);break;case"ArrowUp":this.onTabArrowUpKey(e);break;case"Home":e.shiftKey||this.onTabHomeKey(e);break;case"End":e.shiftKey||this.onTabEndKey(e);break}}onTabArrowDownKey(e){let i=this.findNextHeaderAction(e.target.parentElement);i?this.changeFocusedTab(i):this.onTabHomeKey(e),e.preventDefault()}onTabArrowUpKey(e){let i=this.findPrevHeaderAction(e.target.parentElement);i?this.changeFocusedTab(i):this.onTabEndKey(e),e.preventDefault()}onTabHomeKey(e){let i=this.findFirstHeaderAction();this.changeFocusedTab(i),e.preventDefault()}changeFocusedTab(e){e&&(oe(e),this.selectOnFocus()&&this.tabs.forEach((i,n)=>{let o=this.multiple()?this._activeIndex.includes(n):n===this._activeIndex;this.multiple()?(this._activeIndex||(this._activeIndex=[]),i.id==e.id&&(i.selected=!i.selected,this._activeIndex.includes(n)?this._activeIndex=this._activeIndex.filter(a=>a!==n):this._activeIndex.push(n))):i.id==e.id?(i.selected=!i.selected,this._activeIndex=n):i.selected=!1,i.selectedChange.emit(o),this.activeIndexChange.emit(this._activeIndex),i.cd.markForCheck()}))}findNextHeaderAction(e,i=!1){let n=i?e:e.nextElementSibling,o=D(n,'[data-pc-section="accordionheader"]');return o?M(o,"data-p-disabled")?this.findNextHeaderAction(o.parentElement):D(o.parentElement,'[data-pc-section="accordionheader"]'):null}findPrevHeaderAction(e,i=!1){let n=i?e:e.previousElementSibling,o=D(n,'[data-pc-section="accordionheader"]');return o?M(o,"data-p-disabled")?this.findPrevHeaderAction(o.parentElement):D(o.parentElement,'[data-pc-section="accordionheader"]'):null}findFirstHeaderAction(){let e=this.el.nativeElement.firstElementChild;return this.findNextHeaderAction(e,!0)}findLastHeaderAction(){let e=this.el.nativeElement.lastElementChild;return this.findPrevHeaderAction(e,!0)}onTabEndKey(e){let i=this.findLastHeaderAction();this.changeFocusedTab(i),e.preventDefault()}ngAfterContentInit(){this.initTabs(),this.tabListSubscription=this.tabList.changes.subscribe(e=>{this.initTabs()})}initTabs(){this.tabs=this.tabList.toArray(),this.tabs.forEach(e=>{e.headerAriaLevel=this._headerAriaLevel}),this.updateSelectionState(),this.cd.markForCheck()}getBlockableElement(){return this.el.nativeElement.children[0]}updateSelectionState(){if(this.tabs&&this.tabs.length&&this._activeIndex!=null)for(let e=0;e<this.tabs.length;e++){let i=this.multiple()?this._activeIndex.includes(e):e===this._activeIndex;i!==this.tabs[e].selected&&(this.tabs[e].selected=i,this.tabs[e].selectedChange.emit(i),this.tabs[e].cd.markForCheck())}}isTabActive(e){return this.multiple()?this._activeIndex&&this._activeIndex.includes(e):this._activeIndex===e}getTabProp(e,i){return e.props?e.props[i]:void 0}updateActiveIndex(){let e=this.multiple()?[]:null;this.tabs.forEach((i,n)=>{if(i.selected)if(this.multiple())e.push(n);else{e=n;return}}),this.preventActiveIndexPropagation=!0,this._activeIndex=e,this.activeIndexChange.emit(e)}updateValue(e){let i=this.value();if(this.multiple()){let n=Array.isArray(i)?[...i]:[],o=n.indexOf(e);o!==-1?n.splice(o,1):n.push(e),this.value.set(n)}else i===e?this.value.set(void 0):this.value.set(e)}ngOnDestroy(){this.tabListSubscription&&this.tabListSubscription.unsubscribe(),super.ngOnDestroy()}static \u0275fac=(()=>{let e;return function(n){return(e||(e=y(t)))(n||t)}})();static \u0275cmp=x({type:t,selectors:[["p-accordion"]],contentQueries:function(i,n,o){if(i&1&&_(o,Ie,5),i&2){let a;m(a=b())&&(n.tabList=a)}},hostVars:8,hostBindings:function(i,n){i&1&&U("keydown",function(a){return n.onKeydown(a)}),i&2&&(W(n.hostStyle),A(n.hostClass),C("p-accordion",!0)("p-component",!0))},inputs:{value:[1,"value"],multiple:[1,"multiple"],style:"style",styleClass:"styleClass",expandIcon:"expandIcon",collapseIcon:"collapseIcon",selectOnFocus:[1,"selectOnFocus"],transitionOptions:"transitionOptions",activeIndex:"activeIndex",headerAriaLevel:"headerAriaLevel"},outputs:{value:"valueChange",activeIndexChange:"activeIndexChange",onClose:"onClose",onOpen:"onOpen"},features:[Y([z]),I],ngContentSelectors:G,decls:1,vars:0,template:function(i,n){i&1&&(F(),T(0))},dependencies:[H,V],encapsulation:2,changeDetection:0})}return t})(),$t=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=he({type:t});static \u0275inj=le({imports:[B,Ie,V,de,st,lt,V]})}return t})();export{de as a,st as b,lt as c,B as d,$t as e};
