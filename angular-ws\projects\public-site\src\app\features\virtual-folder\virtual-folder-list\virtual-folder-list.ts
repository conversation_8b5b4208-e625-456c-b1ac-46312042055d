import {
  ReadOnlyVirtualFolderService,
  VirtualFolderDto,
} from '@/proxy/holy-bless/virtual-folders';
import { MobileService } from '@/services/mobile.service';
import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';

@Component({
  selector: 'app-virtual-folder-list',
  standalone: true,
  imports: [CommonModule, RouterModule, CardModule, PaginatorModule],
  templateUrl: './virtual-folder-list.html',
  styleUrls: ['./virtual-folder-list.scss'],
})
export class VirtualFolderListComponent {
  #ReadOnlyVirtualFolderService = inject(ReadOnlyVirtualFolderService);
  mobileService = inject(MobileService);
  router = inject(Router);
  route = inject(ActivatedRoute);

  first = signal(0);
  rows = signal(10);
  totalRecords = signal(0);

  items = signal<VirtualFolderDto[]>([]);
  channelId: number | null = null;

  ngOnInit() {
    this.route.params.subscribe((params) => {
      const channelId = params['channelId'];
      if (channelId) {
        this.channelId = +channelId;
        this.loadVirtualFolders();
      }
    });
  }

  loadVirtualFolders() {
    this.#ReadOnlyVirtualFolderService
      .getList({
        channelId: this.channelId!,
        skipCount: this.first(),
        maxResultCount: this.rows(),
      })
      .subscribe({
        next: (res) => {
          this.items.set(res.items || []);
          this.totalRecords.set(res.totalCount || 0);
        },
        error: (error) => {
          console.error('获取虚拟文件夹列表失败:', error);
        },
      });
  }

  onPageChange(event: any) {
    this.first.set(event.first);
    this.rows.set(event.rows);
    this.loadVirtualFolders();
  }

  navigateToFolderDetail(id: number | undefined) {
    if (!id) return;
    this.router.navigateByUrl(`/virtual-folder/folder-detail/${id}`);
  }
}
