using HolyBless.Enums;
using HolyBless.Interfaces;
using System;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Albums.Dtos
{
    public class AlbumFileDto : EntityDto, IHaveFileUrl, IHaveThumbnail
    {
        public int AlbumId { get; set; }
        public string? AlbumTitle { get; set; }
        //Album Thumbnail
        public string? ThumbnailUrl { get; set; }
        public int? ThumbnailFileId { get; set; }
        public int FileId { get; set; }
        public string? Title { get; set; }
        public int Weight { get; set; }

        // Additional properties for display
        public string? FileName { get; set; }

        public string? FileUrl { get; set; }

        public MediaType MediaType { get; set; }
        public ContentCategory ContentCategory { get; set; }
        public DateTime? DeliveryDate { get; set; }
    }
}