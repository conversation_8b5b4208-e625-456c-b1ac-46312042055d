using Volo.Abp.Application.Dtos;

namespace HolyBless.VirtualFolders
{
    public class VirtualFolderDto : AuditedEntityDto<int>
    {
        public int? ParentFolderId { get; set; }
        public string FolderName { get; set; } = default!;
        public string ContentCode { get; set; } = "";
        public string? LanguageCode { get; set; }
        public int Views { get; set; } = 0;
        public int Weight { get; set; } = 0;
        public int? ChannelId { get; set; }
    }
}
