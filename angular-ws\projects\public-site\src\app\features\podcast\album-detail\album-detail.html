<div class="flex flex-1">
  <div class="articaldetail-container prose max-w-none p-6 flex-1">
    <p-table
      [value]="products"
      stripedRows
      [tableStyle]="{ 'min-width': '60rem' }"
    >
      <ng-template #caption>
        <div class="flex items-center gap-2">
          <p-button icon="pi pi-play" label="播放" [outlined]="true" />
        </div>
      </ng-template>
      <ng-template #header>
        <tr>
          <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
          <th>名称</th>
          <th></th>
        </tr>
      </ng-template>
      <ng-template #body let-product>
        <tr>
          <td>
            <p-tableCheckbox [value]="product" />
          </td>
          <td>{{ product.name }}</td>
          <td>
            <div class="flex items-center gap-2">
              <p-button icon="pi pi-download" label="下载" [outlined]="true" />
              <p-button icon="pi pi-play" label="播放" [outlined]="true" />
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template #footer> </ng-template>
    </p-table>
  </div>
  <div class="p-6">
    <img src="assets/images/灵音.jpg" class="w-72 h-96" alt="Album Cover" />
    <div>
      <div class="mt-6">
        <h3>Album Title</h3>
      </div>
    </div>
  </div>
</div>
