﻿using HolyBless.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Albums.Dtos
{
    /// <summary>
    /// Used to get the list of albums with optional filters for channel and album type (audio/video)
    /// </summary>
    public class AlbumSearchDto : PagedAndSortedResultRequestDto
    {
        public int? ChannelId { get; set; }
        public AlbumType? AlbumType { get; set; }
        public string? ChannelContentCode { get; set; }
    }
}