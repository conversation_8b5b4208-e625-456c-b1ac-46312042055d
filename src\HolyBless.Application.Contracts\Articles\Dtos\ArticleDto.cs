using System;
using System.Collections.Generic;
using HolyBless.Enums;
using HolyBless.Interfaces;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Articles.Dtos
{
    public class ArticleDto : AuditedEntityDto<int>, IHaveThumbnail
    {
        public DateTime DeliveryDate { get; set; }

        public string? LanguageCode { get; set; }
        public string Title { get; set; } = "";
        public int? ThumbnailFileId { get; set; }
        public string? ThumbnailUrl { get; set; }
        public string? Description { get; set; }
        public string? Keywords { get; set; }
        public int Views { get; set; }
        public int Likes { get; set; }
        public ArticleContentCategory ArticleContentCategory { get; set; }
        public PublishStatus Status { get; set; }
        public string? Content { get; set; }
        public string? Memo { get; set; }

        //public List<ArticleToTagDto> ArticleToTags { get; set; } = new List<ArticleToTagDto>();
        //public List<ArticleFileDto> ArticleFiles { get; set; } = new List<ArticleFileDto>();
    }
}