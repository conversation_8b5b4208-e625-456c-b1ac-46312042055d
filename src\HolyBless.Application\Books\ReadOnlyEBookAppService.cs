using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using HolyBless.Entities.Books;
using HolyBless.Books.Dtos;
using HolyBless.Buckets;
using HolyBless.Services;
using Volo.Abp;

namespace HolyBless.Books;

public class ReadOnlyEBookAppService : HolyBlessAppService, IReadOnlyEBookAppService
{
    protected readonly IRepository<EBook, int> _eBookRepository;
    protected readonly IRepository<Chapter, int> _chapterRepository;
    protected readonly IRepository<ChapterToArticle> _chapterToArticleRepository;

    public ReadOnlyEBookAppService(
        IRepository<EBook, int> eBookRepository,
        IRepository<Chapter, int> chapterRepository,
        IRepository<ChapterToArticle> chapterToArticleRepository,
        IRequestContextService requestContextService,
        ICachedFileUrlAppService cachedFileUrlAppService
        )
        : base(cachedFileUrlAppService, requestContextService)
    {
        _eBookRepository = eBookRepository;
        _chapterRepository = chapterRepository;
        _chapterToArticleRepository = chapterToArticleRepository;
    }

    [RemoteService(false)]
    public async Task<EBookDto> GetAsync(int id)
    {
        var eBook = await _eBookRepository.GetAsync(id);
        var rt = ObjectMapper.Map<EBook, EBookDto>(eBook);
        await FillThumbnailUrl(rt);
        return rt;
    }

    [RemoteService(false)]
    public async Task<PagedResultDto<EBookDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await _eBookRepository.GetQueryableAsync();
        var query = queryable
            .OrderBy(e => e.Weight)
            .ThenBy(e => e.Title)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var eBooks = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var rtList = ObjectMapper.Map<List<EBook>, List<EBookDto>>(eBooks);
        await FillThumbnailUrls(rtList);
        return new PagedResultDto<EBookDto>(
            totalCount,
            rtList
        );
    }

    /// <summary>
    /// Get a list of eBooks by channel ID.
    /// Usage: When a channel source is EBook, UI should call this method to render EBook listing page.
    /// </summary>
    /// <param name="channelId"></param>
    /// <returns></returns>
    public async Task<List<EBookDto>> GetEBooksByChannelIdAsync(int channelId)
    {
        var queryable = await _eBookRepository.GetQueryableAsync();
        var eBooks = await queryable
            .Where(e => e.ChannelId == channelId)
            .OrderBy(e => e.Weight)
            .ThenBy(e => e.Title)
            .ToListAsync();

        var rtList = ObjectMapper.Map<List<EBook>, List<EBookDto>>(eBooks);
        await FillThumbnailUrls(rtList);
        return rtList;
    }

    /// <summary>
    /// Get the chapter tree structure for a specific eBook.
    /// Usage: Ebook Detail Page, render left side chapter tree
    /// </summary>
    /// <param name="eBookId"></param>
    /// <returns></returns>
    public async Task<List<ChapterTreeDto>> GetChapterTreeByEBookIdAsync(int eBookId)
    {
        // Get all chapters for the eBook
        var chapterQueryable = await _chapterRepository.GetQueryableAsync();
        var chapters = await chapterQueryable
            .Where(c => c.EBookId == eBookId)
            .OrderBy(c => c.Weight)
            .ThenBy(c => c.Title)
            .ToListAsync();

        // Get article counts for each chapter
        var chapterToArticleQueryable = await _chapterToArticleRepository.GetQueryableAsync();
        var articleCounts = await chapterToArticleQueryable
            .Where(ca => chapters.Select(c => c.Id).Contains(ca.ChapterId))
            .GroupBy(ca => ca.ChapterId)
            .Select(g => new { ChapterId = g.Key, Count = g.Count() })
            .ToListAsync();

        var articleCountDict = articleCounts.ToDictionary(ac => ac.ChapterId, ac => ac.Count);

        // Map to DTOs
        var chapterDtos = chapters.Select(c => new ChapterTreeDto
        {
            Id = c.Id,
            EBookId = c.EBookId,
            ParentChapterId = c.ParentChapterId,
            Title = c.Title,
            Description = c.Description,
            Weight = c.Weight,
            Views = c.Views,
            Likes = c.Likes,
            IsRoot = c.ParentChapterId == null,
            ArticleCount = articleCountDict.GetValueOrDefault(c.Id, 0),
            Children = new List<ChapterTreeDto>()
        }).ToList();

        // Build the tree structure
        var rootChapters = chapterDtos.Where(c => c.ParentChapterId == null).ToList();
        var childChapters = chapterDtos.Where(c => c.ParentChapterId != null).ToList();

        BuildChapterTree(rootChapters, childChapters);

        return rootChapters;
    }

    private void BuildChapterTree(List<ChapterTreeDto> parentChapters, List<ChapterTreeDto> allChildChapters)
    {
        foreach (var parent in parentChapters)
        {
            var children = allChildChapters.Where(c => c.ParentChapterId == parent.Id).ToList();
            parent.Children = children;

            if (children.Any())
            {
                BuildChapterTree(children, allChildChapters);
            }
        }
    }
}