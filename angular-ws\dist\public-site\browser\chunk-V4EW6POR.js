import{p,x as i}from"./chunk-CNIH62FZ.js";import{q as s}from"./chunk-D6WDCTDG.js";import{ab as n,yb as r}from"./chunk-BL4EGCPV.js";import"./chunk-4CLCTAJ7.js";var a=class t{openFolder(e){console.log("Opening folder:",e)}static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275cmp=n({type:t,selectors:[["app-storage"]],decls:1,vars:0,template:function(o,m){o&1&&r(0,"router-outlet")},dependencies:[s,i,p],styles:["[_nghost-%COMP%]{flex:1;display:flex}"]})}};export{a as StorageComponent};
