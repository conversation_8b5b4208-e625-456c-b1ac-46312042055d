using Amazon.S3.Model.Internal.MarshallTransformations;
using HolyBless.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.DependencyInjection;
using static Volo.Abp.Http.MimeTypes;

namespace HolyBless.Services
{
    /// <summary>
    /// Service for accessing request context information like language codes from headers
    /// </summary>
    [RemoteService(false)]
    public class RequestContextService : ApplicationService, IRequestContextService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        // Header names that <PERSON><PERSON> will send
        private const string LanguageCodeHeader = "x-user-language-reading";

        private const string SpokenLangCodeHeader = "x-user-language-audio";

        public RequestContextService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Gets the LanguageCode from the request header
        /// </summary>
        /// <returns>The language code or null if not present</returns>
        public string? GetLanguageCode()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Request?.Headers == null)
            {
                return LangCode.SimplifiedChinese;
            }

            var lang = httpContext.Request.Headers.TryGetValue(LanguageCodeHeader, out var languageCode)
                ? languageCode.ToString()
                : LangCode.SimplifiedChinese;
            if (lang.Equals("zh", System.StringComparison.InvariantCultureIgnoreCase))
                return LangCode.SimplifiedChinese;
            return lang;
        }

        public string GetPreferProvider()
        {
            //Todo: Calculate based on Country
            return ProviderCodeConstants.CloudFlare;
        }

        /// <summary>
        /// Gets the SpokenLangCode from the request header
        /// </summary>
        /// <returns>The spoken language code or null if not present</returns>
        public string? GetSpokenLangCode()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Request?.Headers == null)
            {
                return SpokenLangCode.Mandarin;
            }

            return httpContext.Request.Headers.TryGetValue(SpokenLangCodeHeader, out var spokenLangCode)
                ? spokenLangCode.ToString()
                : SpokenLangCode.Mandarin;
        }

        /// <summary>
        /// Gets both language codes as a tuple
        /// </summary>
        /// <returns>Tuple containing (LanguageCode, SpokenLangCode)</returns>
        public (string? LanguageCode, string? SpokenLangCode) GetLanguageCodes()
        {
            return (GetLanguageCode(), GetSpokenLangCode());
        }
    }
}