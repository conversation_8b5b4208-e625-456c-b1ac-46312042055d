<div class="flex flex-1">
  <p-tree
    [value]="files"
    styleClass="w-[20rem] h-full"
    selectionMode="single"
    [(selection)]="selectedFile"
    (onNodeSelect)="loadArticleDetail($event.node)"
    [virtualScroll]="true"
    virtualScrollItemSize="36"
    [filter]="true"
  />
  <div
    class="articaldetail-container prose max-w-none p-6 flex-1"
    style="overflow-y: auto; max-height: calc(100vh - 60px)"
  >
    <h1 class="text-3xl font-bold mb-4">{{ articleDetail()?.title }}</h1>
    <p class="text-sm mb-4 flex items-center gap-2">
      @if (articleDetail()) {
      <i class="pi pi-clock"></i>
      {{ articleDetail()?.deliveryDate | date: 'yy-MM-dd HH:mm:ss' }} }
    </p>
    <div
      class="mb-4 articaldetail-container"
      [innerHTML]="articleContent()"
    ></div>
  </div>
  <div class="p-6 w-[20rem] border-l-2">
    <div>
      <div class="flex flex-col max-h-[60vh] overflow-y-auto">
        <h3 class="text-lg font-semibold mb-4">目录</h3>

        <!-- 如果没有目录项，显示提示 -->
        <div *ngIf="tocItems().length === 0" class="text-sm italic">
          暂无目录
        </div>

        <!-- 动态生成的目录项 -->
        <a
          *ngFor="let item of tocItems(); trackBy: trackByTocItem"
          class="mt-2 cursor-pointer hover:text-primary-600 underline"
          (click)="onTocItemClick(item)"
        >
          {{ item.text }}
        </a>
      </div>
      <div class="mt-6">
        @if (notImageArticleFiles().length > 0) {
        <h3>附件下载</h3>
        @for (item of notImageArticleFiles(); track $index) {
        <p class="flex justify-between items-center mt-3">
          <span>{{ item.fileName }}</span> <i class="pi pi-download"></i>
        </p>
        } }
      </div>
    </div>
  </div>
</div>
