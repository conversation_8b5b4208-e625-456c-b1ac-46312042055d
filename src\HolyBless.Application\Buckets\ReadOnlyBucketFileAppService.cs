using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using HolyBless.Entities.Buckets;
using Microsoft.EntityFrameworkCore;
using HolyBless.Buckets.Dtos;
using System.Linq;
using System.Linq.Dynamic.Core;
using Volo.Abp;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using HolyBless.Enums;
using static HolyBless.Permissions.HolyBlessPermissions;

namespace HolyBless.Buckets
{
    [AllowAnonymous]
    public class ReadOnlyBucketFileAppService : HolyBlessAppService, IReadOnlyBucketFileAppService
    {
        private readonly IRepository<BucketFile, int> _repository;

        public ReadOnlyBucketFileAppService(
            IRepository<BucketFile, int> repository,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService) : base(cachedFileUrlAppService, requestContextService)
        {
            _repository = repository;
        }

        public async Task<PagedResultDto<FileSearchResultDto>> SearchAsync(BucketFileSearchDto input)
        {
            var queryable = await _repository.GetQueryableAsync();

            queryable = queryable.AsQueryable()
                .WhereIf(!string.IsNullOrWhiteSpace(input.FileName), x => x.FileName.Contains(input.FileName!))
                .WhereIf(input.DeliveryDateStart.HasValue, x => x.DeliveryDate >= input.DeliveryDateStart!.Value)
                .WhereIf(input.DeliveryDateEnd.HasValue, x => x.DeliveryDate <= input.DeliveryDateEnd!.Value)
                .WhereIf(input.ContentCategories != null && input.ContentCategories.Count > 0,
                    x => input.ContentCategories!.Select(c => (int)c).Contains((int)x.ContentCategory));

            var query = queryable.OrderBy(input.Sorting ?? "FileName")
                         .Skip(input.SkipCount)
                         .Take(input.MaxResultCount);

            var totalCount = await AsyncExecuter.CountAsync(queryable);
            var files = await query.Select(x => new FileSearchResultDto
            {
                FileName = x.FileName,
                MediaType = x.MediaType.ToString(),
                ContentCategory = x.ContentCategory,
                LastModificationTime = x.LastModificationTime,
                FileId = x.Id,
            }).ToListAsync();
            await FillFileUrls(files);
            return new PagedResultDto<FileSearchResultDto>(
            totalCount,
                files
            );
        }

        [RemoteService(false)]
        public async Task<BucketFileDto> GetAsync(int id)
        {
            var queryable = await _repository.GetQueryableAsync();
            var bucketFile = await queryable.FirstOrDefaultAsync(x => x.Id == id);
            Check.NotNull(bucketFile, nameof(BucketFile));
            return ObjectMapper.Map<BucketFile, BucketFileDto>(bucketFile);
        }

        [RemoteService(false)]
        public async Task<PagedResultDto<BucketFileDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .OrderBy(input.Sorting ?? "FileName")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var bucketFiles = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<BucketFileDto>(
                totalCount,
                ObjectMapper.Map<List<BucketFile>, List<BucketFileDto>>(bucketFiles)
            );
        }
    }
}