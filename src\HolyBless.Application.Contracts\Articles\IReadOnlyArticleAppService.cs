using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Results;
using HolyBless.Tags.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Articles
{
    public interface IReadOnlyArticleAppService : IApplicationService
    {
        Task<ArticleDto> GetAsync(int id);

        Task<PagedResultDto<ArticleDto>> GetListAsync(PagedAndSortedResultRequestDto input);

        Task<List<TagDto>> GetTagsAsync(int articleId);

        Task<PagedResultDto<TeacherArticleLinkDto>> GetTeacherArticleLinksAsync(int studentArticleId, int skipCount, int maxResultCount, string? sorting = null);

        Task<ArticleAggregateResult?> GetArticleAggregateAsync(int articleId);

        Task<ArticleAggregateResult?> GetArticleAggregateAsync(DateTime deliveryDate);

        Task<List<ArticleAggregateResult>> GetArticleAggregatesByCollectionIdAsync(int collectionId);

        Task<List<ArticleAggregateResult>> GetArticleAggregatesByCollectionContentCodeAsync(string collectionContentCode);

        Task<List<ArticleDto>> GetRelatedArticlesByFileId(int fileId);

        Task<List<ArticleAggregateResult>> GetArticleAggregatesByChapterIdAsync(int chapterId, int skipCount = 0, int maxResultCount = 20);

        Task<PagedResultDto<ArticleSearchResultDto>> SearchAsync(ArticleSearchDto input);
    }
}