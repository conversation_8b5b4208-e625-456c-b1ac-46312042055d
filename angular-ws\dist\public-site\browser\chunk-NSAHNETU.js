import{b as A}from"./chunk-XN4TN4BS.js";import"./chunk-PNGXS4EP.js";import{a as L}from"./chunk-RMOO4PXW.js";import"./chunk-T2K2OPF3.js";import"./chunk-CX2NCE6L.js";import"./chunk-2WHDSBNT.js";import"./chunk-XDERG77Q.js";import{a as D,b as H}from"./chunk-TPWS2CUE.js";import{c as E,d as F}from"./chunk-O7DPC57I.js";import"./chunk-KRRRQ7A6.js";import"./chunk-EFRKI2JO.js";import{d as z}from"./chunk-7JNW5ZNC.js";import"./chunk-5G3J65ZF.js";import"./chunk-7X7MFIN2.js";import{Z as k}from"./chunk-LS3LVTXN.js";import{g as B}from"./chunk-YFEKHFVJ.js";import"./chunk-BMA7WWEI.js";import{o as T,r as O}from"./chunk-CNIH62FZ.js";import{o as d,q as I}from"./chunk-D6WDCTDG.js";import{Cb as w,Hb as c,Ib as u,Ma as _,Pa as h,Ra as n,Sb as g,Ub as f,X as m,Zb as S,ab as P,da as C,ea as y,gb as M,hc as R,jc as x,nb as l,ub as b,vb as v,wb as a,xb as o}from"./chunk-BL4EGCPV.js";import"./chunk-4CLCTAJ7.js";var j=(r,e)=>e.id;function V(r,e){if(r&1&&(a(0,"div",8),g(1),R(2,"date"),o()),r&2){let t=u().$implicit;n(),f(" ",x(2,1,t.creationTime,"yyyy-MM-dd HH:mm:ss")," ")}}function N(r,e){if(r&1){let t=w();a(0,"p-card",2)(1,"p",5),c("click",function(){let p=C(t).$implicit,s=u();return y(s.navigateToArticle(p.id))}),o(),a(2,"p",6),g(3),o(),M(4,V,3,4,"ng-template",7),o()}if(r&2){let t=e.$implicit;l("id",t.id),n(),l("innerHTML",t.title,_),n(2),f(" ",t.description," ")}}var $=class r{constructor(){this.totalRecords=50;this.rows=10;this.first=0;this._isMobile=!1;this.selectedDate=new Date;this.collectionId=null;this.cardItems=[];this.#e=m(L);this.#t=m(T);this.router=m(O);this.checkMobile()}#e;#t;ngOnInit(){this.#t.queryParams.subscribe(e=>{this.collectionId=e.collectionId,this.loadCollectionSummary()})}loadCollectionSummary(){this.collectionId&&this.#e.getCollectionSummary(this.collectionId,{skip:0,maxResultCount:10}).subscribe({next:e=>{this.cardItems=e.articles,this.totalRecords=e.totalRecords},error:e=>{console.error("\u83B7\u53D6\u6458\u8981\u6570\u636E\u5931\u8D25:",e)}})}navigateToArticle(e){this.router.navigateByUrl(`/home/<USER>"\u9875\u9762\u53D8\u5316:",e)}onDateChange(e){console.log("\u9009\u62E9\u7684\u65E5\u671F:",e)}static{this.\u0275fac=function(t){return new(t||r)}}static{this.\u0275cmp=P({type:r,selectors:[["app-summary-cards"]],hostBindings:function(t,i){t&1&&c("resize",function(s){return i.onResize(s)},!1,h)},features:[S([d])],decls:6,vars:6,consts:[[1,"summary-cards-container","p-6"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-3","gap-6","mb-8"],["styleClass","card-item",3,"id"],[1,"pagination-container"],["styleClass","custom-paginator",3,"onPageChange","first","rows","totalRecords","rowsPerPageOptions","showPageLinks","showCurrentPageReport"],[1,"p-card-title","cursor-pointer",3,"click","innerHTML"],[1,"mt-2","text-gray-500","text-sm"],["pTemplate","footer"],[1,"text-gray-500","text-sm","flex","items-center"]],template:function(t,i){t&1&&(a(0,"div",0)(1,"div",1),b(2,N,5,3,"p-card",2,j),o(),a(4,"div",3)(5,"p-paginator",4),c("onPageChange",function(s){return i.onPageChange(s)}),o()()()),t&2&&(n(2),v(i.cardItems),n(3),l("first",i.first)("rows",i.rows)("totalRecords",i.totalRecords)("rowsPerPageOptions",i.rowsPerPageOptions)("showPageLinks",!i.isMobile)("showCurrentPageReport",i.isMobile))},dependencies:[I,d,H,D,k,F,E,A,z,B],styles:["[_nghost-%COMP%]{flex:1}[_nghost-%COMP%]     .p-card{height:100%}.pagination-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:2rem}.pagination-container[_ngcontent-%COMP%]   .custom-paginator[_ngcontent-%COMP%]{border:none;background:transparent}"]})}};export{$ as SummaryCardsComponent};
