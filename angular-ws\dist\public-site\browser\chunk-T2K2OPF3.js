import{g as ge,h as me}from"./chunk-YFEKHFVJ.js";import{c as ue,d as ze,e as Pe,f as _e,g as N,h as V,j as le,k as de,m as Ue,q as he,r as pe,x as fe}from"./chunk-CNIH62FZ.js";import{a as q,q as ae,w as ce}from"./chunk-D6WDCTDG.js";import{D as G,E as X,L as w,N as R,O as ee,R as f,S as M,U as g,W as d,Wa as je,X as v,Y as te,ba as Le,bb as O,db as Fe,f as D,g as b,ga as ne,i as Ne,ia as C,j as P,ja as ie,jb as re,k as _,n as ke,o as J,oc as xe,p as h,pc as $e,q as Q,ra as B,xc as se,y as E,yc as oe,z as U}from"./chunk-BL4EGCPV.js";import{a as c,b as y,c as z,d as W}from"./chunk-4CLCTAJ7.js";var ve=ye;function ye(t,r){return t===r||t!==t&&r!==r?!0:typeof t!=typeof r||{}.toString.call(t)!={}.toString.call(r)||t!==Object(t)||!t?!1:Array.isArray(t)?Ge(t,r):{}.toString.call(t)=="[object Set]"?Ge(Array.from(t),Array.from(r)):{}.toString.call(t)=="[object Object]"?ht(t,r):dt(t,r)}function dt(t,r){return t.toString()===r.toString()}function Ge(t,r){var e=t.length;if(e!=r.length)return!1;for(var n=0;n<e;n++)if(!ye(t[n],r[n]))return!1;return!0}function ht(t,r){var e=Object.keys(t),n=e.length;if(n!=Object.keys(r).length)return!1;for(var i=0;i<n;i++){var s=e[i];if(!(r.hasOwnProperty(s)&&ye(t[s],r[s])))return!1}return!0}var Be=k;function k(t){let r=t;var e={}.toString.call(t).slice(8,-1);if(e=="Set")return new Set([...t].map(i=>k(i)));if(e=="Map")return new Map([...t].map(i=>[k(i[0]),k(i[1])]));if(e=="Date")return new Date(t.getTime());if(e=="RegExp")return RegExp(t.source,pt(t));if(e=="Array"||e=="Object"){r=Array.isArray(t)?[]:{};for(var n in t)r[n]=k(t[n])}return r}function pt(t){if(typeof t.source.flags=="string")return t.source.flags;var r=[];return t.global&&r.push("g"),t.ignoreCase&&r.push("i"),t.multiline&&r.push("m"),t.sticky&&r.push("y"),t.unicode&&r.push("u"),r.join("")}function be(t,r){let e=!r?.manualCleanup;e&&!r?.injector&&Le(be);let n=e?r?.injector?.get(ie)??v(ie):null,i=ft(r?.equal),s;r?.requireSync?s=B({kind:0},{equal:i}):s=B({kind:1,value:r?.initialValue},{equal:i});let o=t.subscribe({next:a=>s.set({kind:1,value:a}),error:a=>{if(r?.rejectErrors)throw a;s.set({kind:2,error:a})}});if(r?.requireSync&&s().kind===0)throw new ee(601,!1);return n?.onDestroy(o.unsubscribe.bind(o)),se(()=>{let a=s();switch(a.kind){case 1:return a.value;case 2:throw a.error;case 0:throw new ee(601,!1)}},{equal:r?.equal})}function ft(t=Object.is){return(r,e)=>r.kind===1&&e.kind===1&&t(r.value,e.value)}var yt=(()=>{class t{warningMessage(){console.error("You should add @abp/ng-oauth packages or create your own auth packages.")}get oidc(){return this.warningMessage(),!1}set oidc(e){this.warningMessage()}init(){return this.warningMessage(),Promise.resolve(void 0)}login(e){return this.warningMessage(),P(void 0)}logout(e){return this.warningMessage(),P(void 0)}navigateToLogin(e){}get isInternalAuth(){throw new Error("not implemented")}get isAuthenticated(){return this.warningMessage(),!1}loginUsingGrant(e,n,i){return console.log({grantType:e,parameters:n,headers:i}),Promise.reject(new Error("not implemented"))}getAccessTokenExpiration(){return this.warningMessage(),0}getRefreshToken(){return this.warningMessage(),""}getAccessToken(){return this.warningMessage(),""}refreshToken(){return this.warningMessage(),Promise.resolve(void 0)}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),Se=class{},we=class extends Se{warningMessage(){console.error("You should add @abp/ng-oauth packages or create your own auth packages.")}get(r){throw this.warningMessage(),new Error("not implemented")}add(r){this.warningMessage()}patch(r){this.warningMessage()}remove(r){this.warningMessage()}run(r){throw this.warningMessage(),new Error("not implemented")}},Qe=new g("LOCALIZATIONS");function Xe(t){t&&Ce.next([...Ce.value,...t])}var Ce=new b([]),F=new g("CORE_OPTIONS");function bt(r){var t=z(r,[]);return c({},t)}function Ee(t){return(r,e,n)=>{if(r==="_")return e;let i=t?.values?.[r];return i&&i[e]||n}}function qe(t){let r=St(t);return(e,n,i)=>{let{localized:s}=r(e,n);return s||i}}function St(t){let r=Ee(t);return(e,n)=>{e=e.concat(t.defaultResourceName||"").filter(Boolean);let i=e.length,s=n.length;for(let o=0;o<i;o++){let a=e[o];for(let u=0;u<s;u++){let l=n[u],p=r(a,l,null);if(p)return{resourceName:a,key:l,localized:p}}}return{resourceName:void 0,key:void 0,localized:void 0}}}function wt(t){return r=>{let e=[],n=t.replace(/\./g,"\\.").replace(/\{\s?([0-9a-zA-Z]+)\s?\}/g,(s,o)=>(e.push(o),"(.+)"));return(r.match(n)||[]).slice(1).reduce((s,o,a)=>{let u=e[a];return s[u]=[...s[u]||[],o].filter(Boolean),s},{})}}function Ct(t,r){return t.replace(/(['"])?\{\s*(\d+)\s*\}\1/g,(e,n,i)=>(n||"")+(r[i]??`{${i}}`)+(n||"")).replace(/\s+/g," ")}function Et(t){return r=>(t.push(r),t)}function Tt(){return function(){}}function It(t){return t===void 0||t===""}function L(t){return t==null}function et(t){return!L(t)}function Te(t){return t instanceof Object}function H(t){return Array.isArray(t)}function Rt(t){return Te(t)&&!H(t)}function Ie(t){return t instanceof Node}function Ve(t){return Rt(t)&&!Ie(t)}function At(t,r){return Object.prototype.hasOwnProperty.call(t,r)}function tt(t,r){return Ve(t)&&Ve(r)?nt(t,r):L(t)&&L(r)?{}:et(r)?r:t}function nt(t,r){if(L(t)||L(r)||H(t)||H(r)||!Te(t)||!Te(r)||Ie(t)||Ie(r))return et(r)?r:t;let n=Object.keys(t),i=Object.keys(r);return[...new Set(n.concat(i))].reduce((o,a)=>(o[a]=nt(t[a],r[a]),o),{})}var j=class{get state(){return this.state$.value}constructor(r){this.initialState=r,this.state$=new b(this.initialState),this.update$=new D,this.sliceState=(e,n=ve)=>this.state$.pipe(h(e),X(n)),this.sliceUpdate=(e,n=i=>i!==void 0)=>this.update$.pipe(h(e),E(n))}patch(r){let e=r;typeof r=="object"&&!Array.isArray(r)&&(e=c(c({},this.state),r)),this.state$.next(e),this.update$.next(e)}deepPatch(r){this.state$.next(tt(this.state,r)),this.update$.next(r)}set(r){this.state$.next(r),this.update$.next(r)}reset(){this.set(this.initialState)}},He=t=>r=>(t&&r[t]||r.default).url||r.default.url,Ke=t=>t&&(t.endsWith("/")?t:t+"/"),x=(()=>{class t{constructor(){this.store=new j({})}get createOnUpdateStream(){return this.store.sliceUpdate}getEnvironment$(){return this.store.sliceState(e=>e)}getEnvironment(){return this.store.state}getApiUrl(e){return He(e)(this.store.state?.apis)}getApiUrl$(e){return this.store.sliceState(n=>n.apis).pipe(h(He(e)))}setState(e){this.store.set(e)}getIssuer(){let e=this.store.state?.oAuthConfig?.issuer;return Ke(e)}getIssuer$(){return this.store.sliceState(e=>e?.oAuthConfig?.issuer).pipe(h(Ke))}getImpersonation(){return this.store.state?.oAuthConfig?.impersonation||{}}getImpersonation$(){return this.store.sliceState(e=>e?.oAuthConfig?.impersonation||{})}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),it=(()=>{class t{constructor(){this._reporter$=new D,this._errors$=new b([])}get reporter$(){return this._reporter$.asObservable()}get errors$(){return this._errors$.asObservable()}get errors(){return this._errors$.value}reportError(e){this._reporter$.next(e),this._errors$.next([...this.errors,e])}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function Dt(t,r){let e=t.get(x),{remoteEnv:n}=r,{headers:i={},method:s="GET",url:o}=n||{};if(!o)return Promise.resolve();let a=t.get(N),u=t.get(it);return a.request(s,o,{headers:i}).pipe(U(l=>(u.reportError(l),P(null))),R(l=>e.setState(Mt(r,l||{},n)))).toPromise()}function Mt(t,r,e){switch(e.mergeStrategy){case"deepmerge":return tt(t,r);case"overwrite":case null:case void 0:return r;default:return e.mergeStrategy(t,r)}}function Ot(t){return t==Number(t)}function Ji(t){let r=[];for(let e in t)Ot(e)||r.push({key:e,value:t[e]});return r}var Nt=(()=>{class t{constructor(){}get length(){return localStorage.length}clear(){localStorage.clear()}getItem(e){return localStorage.getItem(e)}key(e){return localStorage.key(e)}removeItem(e){localStorage.removeItem(e)}setItem(e,n){localStorage.setItem(e,n)}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),$=(()=>{class t{constructor(e,n){this.configState=e,this.localStorageService=n,this.store=new j({}),this.document=v(q),this.updateLocalStorage=()=>{this.localStorageService.setItem("abpSession",JSON.stringify(this.store.state))},this.init(),this.setInitialLanguage()}init(){let e=this.localStorageService.getItem("abpSession");e&&this.store.set(JSON.parse(e)),this.store.sliceUpdate(n=>n).subscribe(this.updateLocalStorage)}setInitialLanguage(){let e=this.getLanguage();this.configState.getDeep$("localization.currentCulture.cultureName").pipe(E(n=>!!n),G(1)).subscribe(n=>{n.includes(";")&&(n=n.split(";")[0]),this.setLanguage(n)})}onLanguageChange$(){return this.store.sliceUpdate(e=>e.language)}onTenantChange$(){return this.store.sliceUpdate(e=>e.tenant)}getLanguage(){return this.store.state.language}getLanguage$(){return this.store.sliceState(e=>e.language)}getTenant(){return this.store.state.tenant}getTenant$(){return this.store.sliceState(e=>e.tenant)}setTenant(e){ve(e,this.store.state.tenant)||this.store.set(y(c({},this.store.state),{tenant:e}))}setLanguage(e){let n=this.store.state.language;e!==n&&this.store.patch({language:e});let i=this.document.documentElement.getAttribute("lang");e!==i&&this.document.documentElement.setAttribute("lang",e)}static{this.\u0275fac=function(n){return new(n||t)(d(A),d(Nt))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),kt=new g("APP_INIT_ERROR_HANDLERS"),Lt=(()=>{class t{constructor(e){this.restService=e,this.apiName="abp",this.findTenantById=(n,i)=>this.restService.request({method:"GET",url:`/api/abp/multi-tenancy/tenants/by-id/${n}`},c({apiName:this.apiName},i)),this.findTenantByName=(n,i)=>this.restService.request({method:"GET",url:`/api/abp/multi-tenancy/tenants/by-name/${n}`},c({apiName:this.apiName},i))}static{this.\u0275fac=function(n){return new(n||t)(d(Y))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),rt=new g("TENANT_KEY"),jt=new ze(()=>!1),Ft=(()=>{class t extends N{request(e,n,i={}){return typeof e=="string"?(this.#e(i),super.request(e,n||"",i)):(this.#e(e),super.request(e))}#e(e){e.context??=new Pe,e.context.set(jt,!0)}static{this.\u0275fac=(()=>{let e;return function(i){return(e||(e=ne(t)))(i||t)}})()}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),Y=(()=>{class t{constructor(e,n,i,s,o){this.options=e,this.http=n,this.externalHttp=i,this.environment=s,this.httpErrorReporter=o}getApiFromStore(e){return this.environment.getApiUrl(e)}handleError(e){return this.httpErrorReporter.reportError(e),_(()=>e)}request(e,n,i){n=n||{},i=i||this.getApiFromStore(n.apiName);let S=e,{method:s,params:o}=S,a=z(S,["method","params"]),{observe:u="body",skipHandleError:l}=n,p=this.removeDuplicateSlashes(i+e.url);return this.getHttpClient(n.skipAddingHeader).request(s,p,c(c({observe:u},o&&{params:this.getParams(o,n.httpParamEncoder)}),a)).pipe(U(I=>l?_(()=>I):this.handleError(I)))}getHttpClient(e){return e?this.externalHttp:this.http}getParams(e,n){let i=Object.entries(e).reduce((s,[o,a])=>(It(a)||a===null&&!this.options.sendNullsAsQueryParam||(s[o]=a),s),{});return n?new ue({encoder:n,fromObject:i}):new ue({fromObject:i})}removeDuplicateSlashes(e){return e.replace(/([^:]\/)\/+/g,"$1")}static{this.\u0275fac=function(n){return new(n||t)(d(F),d(N),d(Ft),d(x),d(it))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),xt=(()=>{class t{constructor(e,n,i,s,o){this.restService=e,this.sessionState=n,this.tenantService=i,this.configStateService=s,this.tenantKey=o,this.domainTenant=null,this.isTenantBoxVisible=!0,this.apiName="abp",this.setTenantToState=a=>(this.sessionState.setTenant({id:a.tenantId,name:a.name,isAvailable:!0}),this.configStateService.refreshAppState().pipe(h(u=>a)))}setTenantByName(e){return this.tenantService.findTenantByName(e).pipe(w(this.setTenantToState))}setTenantById(e){return this.tenantService.findTenantById(e).pipe(w(this.setTenantToState))}static{this.\u0275fac=function(n){return new(n||t)(d(Y),d($),d(Lt),d(A),d(rt))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),$t=new g("TENANT_NOT_FOUND_BY_NAME"),Oe="{0}";function zt(t){t.charAt(t.length-1)!=="/"&&(t+="/");let r=wt(t),e=Oe.replace(/[}{]/g,"");return r(window.location.href)[e]?.[0]}function Pt(t){return new URLSearchParams(window.location.search).get(t)}function _t(t){return W(this,null,function*(){let r=t.get(x),e=t.get(xt),n=t.get($t,null),i=r.getEnvironment()?.application?.baseUrl||"",s=zt(i),o=()=>{e.isTenantBoxVisible=!1},a=p=>{e.domainTenant={id:p.tenantId,name:p.name,isAvailable:!0}},u=p=>{o(),a(p)};if(s){Ze(t,s);let p=e.setTenantByName(s);try{let m=yield J(p);return u(m),Promise.resolve(m)}catch(m){return m instanceof _e&&m.status===404&&n&&n(m),Promise.reject()}}Ze(t,"",Oe+".");let l=Pt(e.tenantKey);if(l){let p=e.setTenantById(l);return J(p)}return Promise.resolve()})}function Ze(t,r,e=Oe){let n=t.get(x),i=Be(n.getEnvironment());return i.application.baseUrl&&(i.application.baseUrl=i.application.baseUrl.replace(e,r)),i.oAuthConfig?.redirectUri&&(i.oAuthConfig.redirectUri=i.oAuthConfig.redirectUri.replace(e,r)),i.oAuthConfig||(i.oAuthConfig={}),i.oAuthConfig.issuer=(i.oAuthConfig.issuer||"").replace(e,r),Object.keys(i.apis).forEach(s=>{Object.keys(i.apis[s]).forEach(o=>{i.apis[s][o]=(i.apis[s][o]||"").replace(e,r)})}),n.setState(i)}var Ut=new g("CHECK_AUTHENTICATION_STATE_FN_KEY");function Gt(){return W(this,null,function*(){let t=v(C),r=t.get(x),e=t.get(A),n=t.get(F);r.setState(n.environment),yield Dt(t,n.environment),yield _t(t);let i=t.get(yt,void 0,{optional:!0}),s=t.get(Ut,Tt,{optional:!0});if(!n.skipInitAuthService&&i&&(yield i.init()),n.skipGetAppConfiguration)return;let o=e.refreshAppState().pipe(R(()=>s(t)),R(()=>{let a=e.getOne("currentTenant");t.get($).setTenant(a)}),U(a=>{let u=t.get(kt,null);return u&&u.length&&u.forEach(l=>l(a)),_(()=>a)}));yield ke(o)})}function Bt(){let t=v(C),r=t.get($),{registerLocaleFn:e}=t.get(F),n=r.getLanguage()||"en";return new Promise((i,s)=>{e(n).then(o=>(o?.default&&ce(o.default),i("resolved")),s)})}var Re=class{constructor(){this.queue=[],this.isRunning=!1,this.stack=0,this.interval=0,this.stackSize=100}init(r,e){this.interval=r,this.stackSize=e}add(r){this.queue.push(r),this.run()}run(){if(this.isRunning)return;this.stack++,this.isRunning=!0;let r=this.queue.shift();if(!r){this.isRunning=!1;return}r(),this.stack>this.stackSize?setTimeout(()=>{this.isRunning=!1,this.run(),this.stack=0},this.interval):(this.isRunning=!1,this.run())}};var Ae=class t{constructor(r){this.children=[],this.isLeaf=!0,Object.assign(this,r)}static create(r){return new t(r)}};function qt(t,r,e,n){let i=Vt(t,r,n),s=[];return t.forEach(o=>{let a=r(o),u=e(o),l=i.get(a);if(l)if(u){let p=i.get(u);if(!p)return;p.children.push(l),p.isLeaf=!1,l.parent=p}else s.push(l)}),s}function Vt(t,r,e){let n=new Map;return t.forEach(i=>n.set(r(i),e(i))),n}function Ht(t,r){if(!H(t)||!t.some(n=>!!n.group))return;let e=new Map;for(let n of t){let i=n?.group||r;if(typeof i!="string")throw new Error(`Invalid group: ${i}`);let s=e.get(i)||[];s.push(n),e.set(i,s)}return e}var Qi=new g("LOADER_DELAY");var Xi=new g("LIST_QUERY_DEBOUNCE_TIME");var Kt=(()=>{class t{constructor(e){this.configState=e}getGrantedPolicy$(e){return this.getStream().pipe(h(n=>this.isPolicyGranted(e,n)))}getGrantedPolicy(e){let n=this.getSnapshot();return this.isPolicyGranted(e,n)}filterItemsByPolicy(e){let n=this.getSnapshot();return e.filter(i=>!i.requiredPolicy||this.isPolicyGranted(i.requiredPolicy,n))}filterItemsByPolicy$(e){return this.getStream().pipe(h(n=>e.filter(i=>!i.requiredPolicy||this.isPolicyGranted(i.requiredPolicy,n))))}isPolicyGranted(e,n){if(!e)return!0;let i=/\|\|/g,s=/&&/g;if(i.test(e)){let o=e.split("||").filter(Boolean);return o.length<2?!1:o.some(a=>this.getPolicy(a.trim(),n))}else if(s.test(e)){let o=e.split("&&").filter(Boolean);return o.length<2?!1:o.every(a=>this.getPolicy(a.trim(),n))}return this.getPolicy(e,n)}getStream(){return this.configState.getAll$().pipe(h(this.mapToPolicies))}getSnapshot(){return this.mapToPolicies(this.configState.getAll())}mapToPolicies(e){return e?.auth?.grantedPolicies||{}}getPolicy(e,n){return n[e]||!1}static{this.\u0275fac=function(n){return new(n||t)(d(A))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var Zt=new g("COOKIE_LANGUAGE_KEY",{factory:()=>".AspNetCore.Culture"}),er=new g("NAVIGATE_TO_MANAGE_PROFILE"),Yt=new g("QUEUE_MANAGER"),st=new g("INCUDE_LOCALIZATION_RESOURCES_TOKEN"),tr=new g("PIPE_TO_LOGIN_FN_KEY"),nr=new g("SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY"),ot=new g("OTHERS_GROUP"),at=new g("SORT_COMPARE_FUNC");function Wt(){let t=v(T);return(e,n)=>{let i=e.order,s=n.order;if(i>s)return 1;if(i<s)return-1;if(e.id>n.id)return 1;if(e.id<n.id)return-1;if(!Number.isInteger(i))return 1;if(!Number.isInteger(s))return-1;let o=t.instant(e.name),a=t.instant(n.name);return o>a?1:o<a?-1:0}}var Jt=new g("DYNAMIC_LAYOUTS"),Qt=new g("DISABLE_APP_NAME"),De=class{constructor(){this._flat$=new b([]),this._tree$=new b([]),this._visible$=new b([]),this.shouldSingularizeRoutes=!0}get flat(){return this._flat$.value}get flat$(){return this._flat$.asObservable()}get tree(){return this._tree$.value}get tree$(){return this._tree$.asObservable()}get visible(){return this._visible$.value}get visible$(){return this._visible$.asObservable()}filterWith(r){return this._flat$.value.filter(e=>!r.has(e[this.id]))}findItemsToRemove(r){return this._flat$.value.reduce((e,n)=>{if(!e.has(n[this.parentId]))return e;let i=new Set([n[this.id]]),s=this.findItemsToRemove(i);return new Set([...e,...s])},r)}publish(r){return this._flat$.next(r),this._tree$.next(this.createTree(r)),this._visible$.next(this.createTree(r.filter(e=>!this.hide(e)))),r}createTree(r){return qt(r,e=>e[this.id],e=>e[this.parentId],e=>Ae.create(e))}createGroupedTree(r){let e=Ht(r,this.othersGroup);if(e)return Array.from(e,([n,i])=>({group:n,items:i}))}add(r){let e=[];if(this.shouldSingularizeRoutes||(e=[...this.flat,...r]),this.shouldSingularizeRoutes){let n=new Map;r.forEach(i=>n.set(i[this.id],i)),e=this.filterWith(n),n.forEach(Et(e))}return e.sort(this.sort),this.publish(e)}find(r,e=this.tree){return e.reduce((n,i)=>n||(r(i)?i:this.find(r,i.children)),null)}patch(r,e){let n=this._flat$.value,i=n.findIndex(s=>s[this.id]===r);return i<0?!1:(n[i]=c(c({},n[i]),e),n.sort(this.sort),this.publish(n))}refresh(){return this.add([])}remove(r){let e=new Set;r.forEach(s=>e.add(s));let n=this.findItemsToRemove(e),i=this.filterWith(n);return this.publish(i)}removeByParam(r){if(!r)return null;let e=Object.keys(r);if(e.length===0)return null;let n=this.flat.filter(s=>e.every(o=>s[o]===r[o]));if(!n?.length)return null;for(let s of n)this.removeByParam({[this.parentId]:s[this.id]});let i=this.flat.filter(s=>!n.includes(s));return this.publish(i)}search(r,e=this.tree){let n=Object.keys(r);return e.reduce((i,s)=>i||(n.every(o=>s[o]===r[o])?s:this.search(r,s.children)),null)}setSingularizeStatus(r=!0){this.shouldSingularizeRoutes=r}},Xt=(()=>{class t extends De{constructor(e){super(),this.injector=e,this.id="name",this.parentId="parentName",this.hide=i=>i.invisible||!this.isGranted(i),this.sort=(i,s)=>this.compareFunc(i,s);let n=this.injector.get(A);this.subscription=n.createOnUpdateStream(i=>i).subscribe(()=>this.refresh()),this.permissionService=e.get(Kt),this.othersGroup=e.get(ot),this.compareFunc=e.get(at)}isGranted({requiredPolicy:e}){return this.permissionService.getGrantedPolicy(e)}hasChildren(e){return!!this.find(i=>i[this.id]===e)?.children?.length}hasInvisibleChild(e){return this.find(i=>i[this.id]===e)?.children?.some(i=>i.invisible)||!1}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(n){return new(n||t)(d(C))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac})}}return t})(),en=(()=>{class t extends Xt{hasPathOrChild(e){return!!e.path||this.hasChildren(e.name)}get groupedVisible(){return this.createGroupedTree(this.visible.filter(e=>this.hasPathOrChild(e)))}get groupedVisible$(){return this.visible$.pipe(h(e=>e.filter(n=>this.hasPathOrChild(n))),h(e=>this.createGroupedTree(e)))}static{this.\u0275fac=(()=>{let e;return function(i){return(e||(e=ne(t)))(i||t)}})()}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var tn=(()=>{class t{constructor(){this.window=v(q).defaultView,this.window.addEventListener("storage",e=>{if(e.key==="access_token"){let n=e.newValue===null,i=e.oldValue===null&&e.newValue!==null;(n||i)&&this.window.location.assign("/")}})}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),nn=(()=>{class t extends he{constructor(){super(),this.title=v(Ue),this.localizationService=v(T),this.disableProjectName=v(Qt,{optional:!0}),this.langugageChange=be(this.localizationService.languageChange$),oe(()=>{this.langugageChange()&&this.updateTitle(this.routerState)})}updateTitle(e){this.routerState=e;let n=this.buildTitle(e),i=this.localizationService.instant({key:"::AppName",defaultValue:"MyProjectName"});if(!n)return this.title.setTitle(i);let s=this.localizationService.instant({key:n,defaultValue:n});this.disableProjectName||(s+=` | ${i}`),this.title.setTitle(s)}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),rn=(()=>{class t{constructor(e){this.restService=e,this.apiName="abp",this.get=(n,i)=>this.restService.request({method:"GET",url:"/api/abp/application-configuration",params:{includeLocalizationResources:n.includeLocalizationResources}},c({apiName:this.apiName},i))}static{this.\u0275fac=function(n){return new(n||t)(d(Y))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),sn=(()=>{class t{constructor(e){this.restService=e,this.apiName="abp",this.get=(n,i)=>this.restService.request({method:"GET",url:"/api/abp/application-localization",params:{cultureName:n.cultureName,onlyDynamics:n.onlyDynamics}},c({apiName:this.apiName},i))}static{this.\u0275fac=function(n){return new(n||t)(d(Y))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),A=(()=>{class t{setState(e){this.store.set(e)}get createOnUpdateStream(){return this.store.sliceUpdate}constructor(e,n,i){this.abpConfigService=e,this.abpApplicationLocalizationService=n,this.includeLocalizationResources=i,this.updateSubject=new D,this.store=new j({}),this.initUpdateStream()}initUpdateStream(){this.updateSubject.pipe(w(()=>this.abpConfigService.get({includeLocalizationResources:!!this.includeLocalizationResources}))).pipe(w(e=>this.getLocalizationAndCombineWithAppState(e))).subscribe(e=>this.store.set(e))}getLocalizationAndCombineWithAppState(e){if(!e.localization.currentCulture.cultureName)throw new Error("culture name should defined");let n=this.uiCultureFromAuthCodeFlow??e.localization.currentCulture.cultureName;return this.getlocalizationResource(n).pipe(h(i=>y(c({},e),{localization:c(c({},e.localization),i)})),R(()=>this.uiCultureFromAuthCodeFlow=void 0))}getlocalizationResource(e){return this.abpApplicationLocalizationService.get({cultureName:e,onlyDynamics:!1})}refreshAppState(){return this.updateSubject.next(),this.createOnUpdateStream(e=>e).pipe(G(1))}refreshLocalization(e){return this.includeLocalizationResources?this.refreshAppState().pipe(h(()=>null)):this.getlocalizationResource(e).pipe(R(n=>this.store.patch({localization:c(c({},this.store.state.localization),n)}))).pipe(h(()=>null))}getOne$(e){return this.store.sliceState(n=>n[e])}getOne(e){return this.store.state[e]}getAll$(){return this.store.sliceState(e=>e)}getAll(){return this.store.state}getDeep$(e){return e=Ye(e),this.store.sliceState(n=>n).pipe(h(n=>e.reduce((i,s)=>{if(i)return i[s]},n)))}getDeep(e){return e=Ye(e),e.reduce((n,i)=>{if(n)return n[i]},this.store.state)}getFeature(e){return this.store.state.features?.values?.[e]}getFeature$(e){return this.store.sliceState(n=>n.features?.values?.[e])}getFeatures(e){let{features:n}=this.store.state;if(n)return e.reduce((i,s)=>y(c({},i),{[s]:n.values[s]}),{})}getFeatures$(e){return this.store.sliceState(({features:n})=>{if(n?.values)return e.reduce((i,s)=>y(c({},i),{[s]:n.values[s]}),{})})}isFeatureEnabled(e,n){return n.values[e]==="true"}getFeatureIsEnabled(e){return this.isFeatureEnabled(e,this.store.state.features)}getFeatureIsEnabled$(e){return this.store.sliceState(n=>this.isFeatureEnabled(e,n.features))}getSetting(e){return this.store.state.setting?.values?.[e]}getSetting$(e){return this.store.sliceState(n=>n.setting?.values?.[e])}getSettings(e){let n=this.store.state.setting?.values||{};return e?Object.keys(n).filter(s=>s.indexOf(e)>-1).reduce((s,o)=>(s[o]=n[o],s),{}):n}getSettings$(e){return this.store.sliceState(n=>n.setting?.values).pipe(h((n={})=>e?Object.keys(n).filter(s=>s.indexOf(e)>-1).reduce((s,o)=>(s[o]=n[o],s),{}):n))}getGlobalFeatures(){return this.store.state.globalFeatures}getGlobalFeatures$(){return this.store.sliceState(e=>e.globalFeatures)}isGlobalFeatureEnabled(e,n){return(n.enabledFeatures||[]).some(s=>e===s)}getGlobalFeatureIsEnabled(e){return this.isGlobalFeatureEnabled(e,this.store.state.globalFeatures)}getGlobalFeatureIsEnabled$(e){return this.store.sliceState(n=>this.isGlobalFeatureEnabled(e,n.globalFeatures))}static{this.\u0275fac=function(n){return new(n||t)(d(rn),d(sn),d(st,8))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function Ye(t){if(typeof t=="string"&&(t=t.split(".")),!Array.isArray(t))throw new Error("The argument must be a dot string or an string array.");return t}var T=(()=>{class t{get currentLang(){return this.latestLang||this.sessionState.getLanguage()}get currentLang$(){return this.sessionState.getLanguage$()}get languageChange$(){return this._languageChange$.asObservable()}constructor(e,n,i,s){if(this.sessionState=e,this.injector=n,this.configState=s,this.latestLang=this.sessionState.getLanguage(),this._languageChange$=new D,this.uiLocalizations$=new b(new Map),this.localizations$=new b(new Map),i)throw new Error("LocalizationService should have only one instance.");this.listenToSetLanguage(),this.initLocalizationValues()}initLocalizationValues(){Ce.subscribe(o=>this.addLocalization(o));let e=this.configState.getDeep$("localization.values"),n=this.configState.getDeep$("localization.resources"),i=this.sessionState.getLanguage$(),s=Q([i,this.uiLocalizations$]).pipe(h(([o,a])=>a.get(o)));Q([e,n,s]).pipe(h(([o,a,u])=>{if(!a)return;let l=an(o||{},a);return l&&(u||(u=new Map),Object.entries(l).forEach(p=>{let m=p[0],S=p[1],I=u?.get(m)||{};I=c(c({},I),S),u?.set(m,I)})),u}),E(Boolean)).subscribe(o=>this.localizations$.next(o))}addLocalization(e){if(!e)return;let n=this.uiLocalizations$.value;e.forEach(i=>{let s=n.get(i.culture)||new Map;i.resources.forEach(o=>{let a=s.get(o.resourceName)||{};a=c(c({},a),o.texts),s.set(o.resourceName,a)}),n.set(i.culture,s)}),this.uiLocalizations$.next(n)}listenToSetLanguage(){this.sessionState.onLanguageChange$().pipe(E(e=>this.configState.getDeep("localization.currentCulture.cultureName")!==e),w(e=>this.configState.refreshLocalization(e).pipe(h(()=>e))),E(Boolean),w(e=>Ne(this.registerLocale(e).then(()=>e)))).subscribe(e=>this._languageChange$.next(e))}registerLocale(e){let{registerLocaleFn:n}=this.injector.get(F);return n(e).then(i=>{i?.default&&ce(i.default),this.latestLang=e})}get(e,...n){return this.configState.getAll$().pipe(h(i=>this.getLocalization(i,e,...n)))}getResource(e){return this.localizations$.value.get(e)}getResource$(e){return this.localizations$.pipe(h(n=>n.get(e)))}instant(e,...n){return this.getLocalization(this.configState.getAll(),e,...n)}localize(e,n,i){return this.configState.getOne$("localization").pipe(h(Ee),h(s=>s(e,n,i)))}localizeSync(e,n,i){let s=this.configState.getOne("localization");return Ee(s)(e,n,i)}localizeWithFallback(e,n,i){return this.configState.getOne$("localization").pipe(h(qe),h(s=>s(e,n,i)))}localizeWithFallbackSync(e,n,i){let s=this.configState.getOne("localization");return qe(s)(e,n,i)}getLocalization(e,n,...i){let s="";if(!n)return s;typeof n!="string"&&(s=n.defaultValue,n=n.key);let o=n.split("::"),a=S=>{$e()&&console.warn(S)};if(o.length<2)return a("The localization source separator (::) not found."),s||n;if(!e.localization)return s||o[1];let u=o[0]||e.localization.defaultResourceName,l=o[1];if(u==="_")return s||l;if(!u)return a("Localization source name is not specified and the defaultResourceName was not defined!"),s||l;let p=this.localizations$.value.get(u);if(!p)return a("Could not find localization source: "+u),s||l;let m=p[l];return typeof m>"u"?s||l:(i=i.filter(S=>S!=null),m&&(m=Ct(m,i)),typeof m!="string"&&(m=""),m||s||n)}static{this.\u0275fac=function(n){return new(n||t)(d($),d(C),d(t,12),d(A))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function ct(t,r){let e=r[t];return e.baseResources.length===0?e:e.baseResources.reduce((n,i)=>{let s=ct(i,r),o=c(c({},s.texts),e.texts);return y(c({},n),{texts:o})},e)}function on(t){return Object.keys(t).map(e=>{let n=ct(e,t);return[e,n]}).reduce((e,[n,i])=>y(c({},e),{[n]:i}),{})}function an(t,r){let e=on(r);return Object.entries(e).reduce((n,[i,s])=>y(c({},n),{[i]:s.texts}),t)}var We={aa:"en","aa-DJ":"en","aa-ER":"en","aa-ET":"en","af-ZA":"af","agq-CM":"agq","ak-GH":"ak","am-ET":"am","ar-001":"ar",arn:"en","arn-CL":"en","as-IN":"as","asa-TZ":"asa","ast-ES":"ast","az-Cyrl-AZ":"az-Cyrl","az-Latn-AZ":"az-Latn",ba:"ru","ba-RU":"ru","bas-CM":"bas","be-BY":"be","bem-ZM":"bem","bez-TZ":"bez","bg-BG":"bg",bin:"en","bin-NG":"en","bm-Latn":"bm","bm-Latn-ML":"bm","bn-BD":"bn","bo-CN":"bo","br-FR":"br","brx-IN":"brx","bs-Cyrl-BA":"bs-Cyrl","bs-Latn-BA":"bs-Latn",byn:"en","byn-ER":"en","ca-ES":"ca","ca-ES-valencia":"ca-ES-VALENCIA","ce-RU":"ce","cgg-UG":"cgg","chr-Cher":"chr","chr-Cher-US":"chr",co:"en","co-FR":"fr","cs-CZ":"cs","cu-RU":"cu","cy-GB":"cy","da-DK":"da","dav-KE":"dav","de-DE":"de","dje-NE":"dje","dsb-DE":"dsb","dua-CM":"dua",dv:"en","dv-MV":"en","dyo-SN":"dyo","dz-BT":"dz","ebu-KE":"ebu","ee-GH":"ee","el-GR":"el","en-029":"en","en-ID":"en","en-US":"en","eo-001":"en","es-ES":"es","et-EE":"et","eu-ES":"eu","ewo-CM":"ewo","fa-IR":"fa","ff-Latn-SN":"ff-Latn","ff-NG":"ff","fi-FI":"fi","fil-PH":"fil","fo-FO":"fo","fr-029":"fr","fr-FR":"fr","fur-IT":"fur","fy-NL":"fy","ga-IE":"ga","gd-GB":"gd","gl-ES":"gl",gn:"en","gn-PY":"en","gsw-CH":"gsw","gu-IN":"gu","guz-KE":"guz","gv-IM":"gv","ha-Latn":"ha","ha-Latn-GH":"ha-GH","ha-Latn-NE":"ha-NE","ha-Latn-NG":"ha","haw-US":"haw","he-IL":"he","hi-IN":"hi","hr-HR":"hr","hsb-DE":"hsb","hu-HU":"hu","hy-AM":"hy","ia-001":"ia","ia-FR":"ia",ibb:"en","ibb-NG":"en","id-ID":"id","ig-NG":"ig","ii-CN":"ii","is-IS":"is","it-IT":"it",iu:"en","iu-Cans":"en","iu-Cans-CA":"en","iu-Latn":"en","iu-Latn-CA":"en","ja-JP":"ja","jgo-CM":"jgo","jmc-TZ":"jmc","jv-Java":"jv","jv-Java-ID":"jv","jv-Latn":"jv","jv-Latn-ID":"jv","ka-GE":"ka","kab-DZ":"kab","kam-KE":"kam","kde-TZ":"kde","kea-CV":"kea","khq-ML":"khq","ki-KE":"ki","kk-KZ":"kk","kkj-CM":"kkj","kl-GL":"kl","kln-KE":"kln","km-KH":"km","kn-IN":"kn","ko-KR":"ko","kok-IN":"kok",kr:"en","kr-NG":"en","ks-Arab":"ks","ks-Arab-IN":"ks","ks-Deva":"ks","ks-Deva-IN":"ks","ksb-TZ":"ksb","ksf-CM":"ksf","ksh-DE":"ksh","ku-Arab":"ku","ku-Arab-IQ":"ku","ku-Arab-IR":"ku","kw-GB":"kw","ky-KG":"ky",la:"en","la-001":"en","lag-TZ":"lag","lb-LU":"lb","lg-UG":"lg","lkt-US":"lkt","ln-CD":"ln","lo-LA":"lo","lrc-IR":"lrc","lt-LT":"lt","lu-CD":"lu","luo-KE":"luo","luy-KE":"luy","lv-LV":"lv","mas-KE":"mas","mer-KE":"mer","mfe-MU":"mfe","mg-MG":"mg","mgh-MZ":"mgh","mgo-CM":"mgo","mi-NZ":"mi","mk-MK":"mk","ml-IN":"ml","mn-Cyrl":"mn","mn-MN":"mn","mn-Mong":"mn","mn-Mong-CN":"mn","mn-Mong-MN":"mn",mni:"en","mni-IN":"en",moh:"en","moh-CA":"en","mr-IN":"mr","ms-MY":"ms","mt-MT":"mt","mua-CM":"mua","my-MM":"my","mzn-IR":"mzn","naq-NA":"naq","nb-NO":"nb","nd-ZW":"nd","ne-NP":"ne","nl-NL":"nl","nmg-CM":"ngm","nn-NO":"nn","nnh-CM":"nnh",no:"en",nqo:"en","nqo-GN":"en",nr:"en","nr-ZA":"en",nso:"en","nso-ZA":"en","nus-SS":"nus","nyn-UG":"nyn",oc:"en","oc-FR":"fr","om-ET":"om","or-IN":"or","os-GE":"os","pa-Arab-PK":"pa-Arab","pa-IN":"pa",pap:"en","pap-029":"en","pl-PL":"pl","prg-001":"prg",prs:"en","prs-AF":"en","ps-AF":"ps","pt-BR":"pt",quc:"en","quc-Latn":"en","quc-Latn-GT":"en",quz:"en","quz-BO":"en","quz-EC":"en","quz-PE":"en","rm-CH":"rm","rn-BI":"rn","ro-RO":"ro","rof-TZ":"rof","ru-RU":"ru","rw-RW":"rw","rwk-TZ":"rwk",sa:"en","sa-IN":"en","sah-RU":"sah","saq-KE":"saq","sbp-TZ":"en","sd-Arab":"sd","sd-Arab-PK":"sd","sd-Deva":"sd","sd-Deva-IN":"sd","se-NO":"se","seh-MZ":"seh","ses-ML":"ses","sg-CF":"sg","shi-Latn-MA":"shi-Latn","shi-Tfng-MA":"shi-Tfng","si-LK":"si","sk-SK":"sk","sl-SI":"sl",sma:"en","sma-NO":"en","sma-SE":"en",smj:"en","smj-NO":"en","smj-SE":"en","smn-FI":"en",sms:"en","sms-FI":"en","sn-Latn":"sn","sn-Latn-ZW":"sn","so-SO":"so","sq-AL":"so","sr-Cyrl-RS":"sr-Cryl","sr-Latn-RS":"sr-Latn",ss:"en","ss-SZ":"en","ss-ZA":"en",ssy:"en","ssy-ER":"en",st:"en","st-LS":"en","st-ZA":"en","sv-SE":"sv","sw-TZ":"sw",syr:"en","syr-SY":"en","ta-IN":"ta","te-IN":"te","teo-UG":"teo","tg-Cyrl":"tg","tg-Cyrl-TJ":"tg","th-TH":"th","ti-ET":"ti",tig:"en","tig-ER":"en","tk-TM":"tk",tn:"en","tn-BW":"en","tn-ZA":"en","to-TO":"to","tr-TR":"tr",ts:"en","ts-ZA":"en","tt-RU":"tt","twq-NE":"twq","tzm-Arab":"tzm","tzm-Arab-MA":"tzm","tzm-Latn":"tzm","tzm-Latn-DZ":"tzm","tzm-Latn-MA":"tzm","tzm-Tfng":"tzm","tzm-Tfng-MA":"tzm","ug-CN":"ug","uk-UA":"uk","ur-PK":"ur","uz-Arab-AF":"uz-Arab","uz-Cyrl-UZ":"uz-Cyrl","uz-Latn-UZ":"uz-Latn","vai-Latn-LR":"vai-Latn","vai-Vaii-LR":"vai-Vaii",ve:"en","ve-ZA":"en","vi-VN":"vi","vo-001":"vo","vun-TZ":"vun","wae-CH":"wae",wal:"en","wal-ET":"en","wo-SN":"wo","xh-ZA":"xh","xog-UG":"xog","yav-CM":"yav","yi-001":"yi","yo-NG":"yo","zgh-Tfng":"zgh","zgh-Tfng-MA":"zgh","zh-CN":"zh","zh-HK":"zh","zh-MO":"zh","zh-SG":"zh","zh-TW":"zh","zu-ZA":"zu"},cn=new Map([["application","Theme.ApplicationLayoutComponent"],["account","Theme.AccountLayoutComponent"],["empty","Theme.EmptyLayoutComponent"]]);var un=(()=>{class t{constructor(e){this.localization=e}transform(e="",...n){let i=n.reduce((s,o)=>s?o?Array.isArray(o)?[...s,...o]:[...s,o]:s:o,[])||[];return this.localization.instant(e,...i)}static{this.\u0275fac=function(n){return new(n||t)(je(T,16))}}static{this.\u0275pipe=Fe({name:"abpLocalization",type:t,pure:!0,standalone:!1})}static{this.\u0275prov=f({token:t,factory:t.\u0275fac})}}return t})();var ir=new g("INJECTOR_PIPE_DATA_TOKEN");var K=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=O({type:t})}static{this.\u0275inj=M({})}}return t})();Date.prototype.toLocalISOString=function(){let t=this.getTimezoneOffset();return new Date(this.getTime()-t*6e4).toISOString()};function ln(){let t=v(C),r=t.get($),e=t.get(q),n=t.get(Zt);r.getLanguage$().subscribe(i=>{let s=encodeURIComponent(`c=${i}|uic=${i}`);e.cookie=`${n}=${s}`})}var dn=re(()=>{ln()}),Me=class extends String{constructor(r){super(),this.localizationService=r}toString(){let{currentLang:r}=this.localizationService;return At(We,r)?We[r]:r}valueOf(){return this.toString()}},hn={provide:xe,useClass:Me,deps:[T]},pn={provide:st,useValue:!1},fn=(()=>{class t{constructor(e,n){this.routes=e,this.router=n,this.addRoutes()}addRoutes(){this.router?.config?.forEach(({path:e="",data:n})=>{let i=n?.routes;if(i)if(Array.isArray(i))this.routes.add(i);else{let s=ut([c({path:e},i)],{path:""});this.routes.add(s)}})}static{this.\u0275fac=function(n){return new(n||t)(d(en),d(pe,8))}}static{this.\u0275prov=f({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function ut(t,r){return t?t.reduce((e,n)=>{let o=y(c({},n),{parentName:r.name,path:(r.path+"/"+n.path).replace(/\/\//g,"/")}),{children:i}=o,s=z(o,["children"]);return e.push(s,...ut(i,s)),e},[]):[]}var lt=function(t){return t[t.Options=0]="Options",t[t.CompareFunctionFactory=1]="CompareFunctionFactory",t[t.TitleStrategy=2]="TitleStrategy",t}(lt||{});function gn(t,r){return{\u0275kind:t,\u0275providers:r}}function mn(t={}){return gn(lt.Options,[{provide:"CORE_OPTIONS",useValue:t},{provide:F,useFactory:bt,deps:["CORE_OPTIONS"]},{provide:rt,useValue:t.tenantKey||"__tenant"},{provide:Qe,multi:!0,useValue:Xe(t.localizations),deps:[T]},{provide:ot,useValue:t.othersGroup||"AbpUi::OthersGroup"},{provide:Jt,useValue:t.dynamicLayouts||cn}])}function vn(...t){let r=[V(le(),de({cookieName:"XSRF-TOKEN",headerName:"RequestVerificationToken"})),re(()=>{Gt(),Bt(),v(T),v(tn),v(fn)}),hn,dn,{provide:at,useFactory:Wt},{provide:Yt,useClass:Re},we,pn,{provide:he,useExisting:nn}];for(let e of t)r.push(...e.\u0275providers);return te(r)}function yn(t={}){return te([{provide:Qe,multi:!0,useValue:Xe(t.localizations),deps:[T]}])}var Z=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=O({type:t})}static{this.\u0275inj=M({providers:[un,V(le())],imports:[ae,ge,me,fe,K,ae,ge,me,fe,K]})}}return t})(),Je=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=O({type:t})}static{this.\u0275inj=M({providers:[V(de({cookieName:"XSRF-TOKEN",headerName:"RequestVerificationToken"}))],imports:[Z,K,Z,K]})}}return t})(),rr=(()=>{class t{static forRoot(e={}){return{ngModule:Je,providers:[vn(mn(e))]}}static forChild(e={}){return{ngModule:Je,providers:[yn(e)]}}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=O({type:t})}static{this.\u0275inj=M({imports:[Z,Z]})}}return t})();export{Ji as a,Y as b,rr as c};
