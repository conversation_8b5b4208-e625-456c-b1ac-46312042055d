import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import {
  ArticleAggregateResult,
  ArticleFileAggregateResult,
} from '@/proxy/holy-bless/results';
import { GalleriaModule } from 'primeng/galleria';
import { MediaType } from '@/proxy/holy-bless/enums';

@Component({
  selector: 'app-artical-detail',
  standalone: true,
  imports: [CommonModule, GalleriaModule],
  templateUrl: './article-detail.html',
  styleUrls: ['./article-detail.scss'],
})
export class ArticleDetailComponent {
  #route = inject(ActivatedRoute);
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  articleId: number | null = null;

  articleDetail = signal<ArticleAggregateResult | null>(null);
  articleFiles = computed(() => this.articleDetail()?.articleFiles || []);
  primaryArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.isPrimary === true),
  );
  // imageArticleFiles = computed(() =>
  //   this.articleFiles().filter((file) => file.mediaType === MediaType.Image),
  // );
  imageArticleFiles = signal([
    {fileUrl: "https://picsum.photos/200/300"},
    {fileUrl: "https://picsum.photos/300/400"},
    {fileUrl: "https://picsum.photos/400/200"},
  ])
  notImageArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.mediaType !== MediaType.Image),
  );

  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      this.articleId = params['articleId'];
      this.loadArticleDetail();
    });
  }

  loadArticleDetail() {
    if (!this.articleId) return;
    this.#ReadOnlyArticleService.getArticleAggregate(this.articleId).subscribe({
      next: (data) => {
        this.articleDetail.set(data);
      },
      error: (error) => {
        console.error('获取文章详情失败:', error);
      },
    });
  }
}
