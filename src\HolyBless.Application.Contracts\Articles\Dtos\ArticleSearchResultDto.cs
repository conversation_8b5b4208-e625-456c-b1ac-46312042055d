using System;
using HolyBless.Enums;
using HolyBless.Interfaces;

namespace HolyBless.Articles.Dtos
{
    /// <summary>
    /// Result DTO for article search operations
    /// </summary>
    public class ArticleSearchResultDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public DateTime DeliveryDate { get; set; }
        public ArticleContentCategory ArticleContentCategory { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public string? Content { get; set; } = string.Empty;
    }
}