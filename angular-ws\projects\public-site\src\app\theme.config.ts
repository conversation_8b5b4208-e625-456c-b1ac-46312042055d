import { definePreset } from '@primeng/themes';
import Aura from '@primeng/themes/aura';

export const CustomAuraPreset = definePreset(Aura, {
  semantic: {
    primary: {
      50: '{orange.50}',
      100: '{orange.100}',
      200: '{orange.200}',
      300: '{orange.300}',
      400: '{orange.400}',
      500: '{orange.500}',
      600: '{orange.600}',
      700: '{orange.700}',
      800: '{orange.800}',
      900: '{orange.900}',
      950: '{orange.950}',
    },
  },
});

export const GreenModePreset = definePreset(Aura, {
  semantic: {
    primary: {
      50: '{green.50}',
      100: '{green.100}',
      200: '{green.200}',
      300: '{green.300}',
      400: '#6b7280',
      500: '#6b7280',
      600: '{green.600}',
      700: '{green.700}',
      800: '{green.800}',
      900: '{green.900}',
      950: '{green.950}',
    },
    colorScheme: {
      light: {
        surface: {
          0: '#DCE9DD',
          50: '{green.50}',
          100: '#EDFFE8',
          200: '#d1d5db',
          300: '{green.300}',
          400: '#6b7280',
          500: '#6b7280',
          600: '{green.600}',
          700: '#6b7280',
          800: '{green.800}',
          900: '{green.900}',
          950: '{green.950}',
        },
      },
    },
  },
});
