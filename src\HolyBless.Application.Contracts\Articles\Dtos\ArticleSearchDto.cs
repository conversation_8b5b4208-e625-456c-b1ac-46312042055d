using System;
using System.Collections.Generic;
using HolyBless.Enums;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Articles.Dtos
{
    /// <summary>
    /// Used for article advanced search with keyword, search fields, content categories, and delivery date range
    /// </summary>
    public class ArticleSearchDto : PagedAndSortedResultRequestDto
    {
        public string? Keyword { get; set; }
        public List<SearchFieldEnum>? SearchFields { get; set; }
        public List<ArticleContentCategory>? ContentCategories { get; set; }
        public DateTime? DeliveryDateStart { get; set; }
        public DateTime? DeliveryDateEnd { get; set; }
    }
}
