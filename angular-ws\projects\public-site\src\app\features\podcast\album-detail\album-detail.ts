import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { ArticleAggregateResult } from '@/proxy/holy-bless/results';
import { MediaType } from '@/proxy/holy-bless/enums';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-album-detail',
  standalone: true,
  imports: [CommonModule, TableModule, ButtonModule],
  templateUrl: './album-detail.html',
  styleUrls: ['./album-detail.scss'],
})
export class AlbumDetailComponent {
  products = [
    {
      id: '1',
      name: 'Product 1',
      type: 'Type A',
      lastModified: new Date(),
    },
    {
      id: '2',
      name: 'Product 2',
      type: 'Type B',
      lastModified: new Date(),
    },
    {
      id: '3',
      name: 'Product 3',
      type: 'Type C',
      lastModified: new Date(),
    },
    {
      id: '4',
      name: 'Product 4',
      type: 'Type D',
      lastModified: new Date(),
    },
  ];
}
