﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Services;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.VirtualFolders
{
    public class VirtualFolderAppService : ReadOnlyVirtualFolderAppService, IVirtualFolderAppService
    {
        public VirtualFolderAppService(
             IRepository<VirtualDiskFolder, int> virtualFolderRepository,
             IRepository<FolderToFile> folderToBucketFileRepository,
             IRepository<VirtualDiskFolderTree, int> folderTreeRepository,
             IRequestContextService requestContextService,
             ICachedFileUrlAppService cachedFileUrlAppService
            )
            : base(virtualFolderRepository, folderToBucketFileRepository, folderTreeRepository, requestContextService, cachedFileUrlAppService)
        {
        }
    }
}