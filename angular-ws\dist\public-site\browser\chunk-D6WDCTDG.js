import{Eb as me,Fb as D,Gb as l,O as x,R as E,S as he,Sa as De,Ta as te,U as w,Va as z,W as A,Wa as h,X as I,Ya as j,bb as pe,ca as ge,cb as L,db as ne,f as fe,oc as Ce,qa as U,rc as Fe,sc as ye}from"./chunk-BL4EGCPV.js";var T=new w("");var Ae=null;function V(){return Ae}function Ke(e){Ae??=e}var ie=class{},O=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(n){return new(n||e)};static \u0275prov=E({token:e,factory:()=>I(Se),providedIn:"platform"})}return e})(),Xe=new w(""),Se=(()=>{class e extends O{_location;_history;_doc=I(T);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return V().getBaseHref(this._doc)}onPopState(t){let n=V().getGlobalEventTarget(this._doc,"window");return n.addEventListener("popstate",t,!1),()=>n.removeEventListener("popstate",t)}onHashChange(t){let n=V().getGlobalEventTarget(this._doc,"window");return n.addEventListener("hashchange",t,!1),()=>n.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,n,r){this._history.pushState(t,n,r)}replaceState(t,n,r){this._history.replaceState(t,n,r)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static \u0275fac=function(n){return new(n||e)};static \u0275prov=E({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function G(e,i){return e?i?e.endsWith("/")?i.startsWith("/")?e+i.slice(1):e+i:i.startsWith("/")?e+i:`${e}/${i}`:e:i}function Ee(e){let i=e.search(/#|\?|$/);return e[i-1]==="/"?e.slice(0,i-1)+e.slice(i):e}function C(e){return e&&e[0]!=="?"?`?${e}`:e}var M=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(n){return new(n||e)};static \u0275prov=E({token:e,factory:()=>I(_e),providedIn:"root"})}return e})(),H=new w(""),_e=(()=>{class e extends M{_platformLocation;_baseHref;_removeListenerFns=[];constructor(t,n){super(),this._platformLocation=t,this._baseHref=n??this._platformLocation.getBaseHrefFromDOM()??I(T).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return G(this._baseHref,t)}path(t=!1){let n=this._platformLocation.pathname+C(this._platformLocation.search),r=this._platformLocation.hash;return r&&t?`${n}${r}`:n}pushState(t,n,r,s){let o=this.prepareExternalUrl(r+C(s));this._platformLocation.pushState(t,n,o)}replaceState(t,n,r,s){let o=this.prepareExternalUrl(r+C(s));this._platformLocation.replaceState(t,n,o)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(n){return new(n||e)(A(O),A(H,8))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),be=(()=>{class e{_subject=new fe;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(t){this._locationStrategy=t;let n=this._locationStrategy.getBaseHref();this._basePath=Qe(Ee(we(n))),this._locationStrategy.onPopState(r=>{this._subject.next({url:this.path(!0),pop:!0,state:r.state,type:r.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,n=""){return this.path()==this.normalize(t+C(n))}normalize(t){return e.stripTrailingSlash(Je(this._basePath,we(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,n="",r=null){this._locationStrategy.pushState(r,"",t,n),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+C(n)),r)}replaceState(t,n="",r=null){this._locationStrategy.replaceState(r,"",t,n),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+C(n)),r)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(n=>{this._notifyUrlChangeListeners(n.url,n.state)}),()=>{let n=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(n,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",n){this._urlChangeListeners.forEach(r=>r(t,n))}subscribe(t,n,r){return this._subject.subscribe({next:t,error:n??void 0,complete:r??void 0})}static normalizeQueryParams=C;static joinWithSlash=G;static stripTrailingSlash=Ee;static \u0275fac=function(n){return new(n||e)(A(M))};static \u0275prov=E({token:e,factory:()=>qe(),providedIn:"root"})}return e})();function qe(){return new be(A(M))}function Je(e,i){if(!e||!i.startsWith(e))return i;let t=i.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:i}function we(e){return e.replace(/\/index.html$/,"")}function Qe(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var tt=(()=>{class e extends M{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(t,n){super(),this._platformLocation=t,n!=null&&(this._baseHref=n)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}path(t=!1){let n=this._platformLocation.hash??"#";return n.length>0?n.substring(1):n}prepareExternalUrl(t){let n=G(this._baseHref,t);return n.length>0?"#"+n:n}pushState(t,n,r,s){let o=this.prepareExternalUrl(r+C(s))||this._platformLocation.pathname;this._platformLocation.pushState(t,n,o)}replaceState(t,n,r,s){let o=this.prepareExternalUrl(r+C(s))||this._platformLocation.pathname;this._platformLocation.replaceState(t,n,o)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(n){return new(n||e)(A(O),A(H,8))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();var f=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(f||{}),a=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(a||{}),g=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(g||{}),_={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function Te(e){return D(e)[l.LocaleId]}function Oe(e,i,t){let n=D(e),r=[n[l.DayPeriodsFormat],n[l.DayPeriodsStandalone]],s=p(r,i);return p(s,t)}function Re(e,i,t){let n=D(e),r=[n[l.DaysFormat],n[l.DaysStandalone]],s=p(r,i);return p(s,t)}function Be(e,i,t){let n=D(e),r=[n[l.MonthsFormat],n[l.MonthsStandalone]],s=p(r,i);return p(s,t)}function Pe(e,i){let n=D(e)[l.Eras];return p(n,i)}function R(e,i){let t=D(e);return p(t[l.DateFormat],i)}function B(e,i){let t=D(e);return p(t[l.TimeFormat],i)}function P(e,i){let n=D(e)[l.DateTimeFormat];return p(n,i)}function N(e,i){let t=D(e),n=t[l.NumberSymbols][i];if(typeof n>"u"){if(i===_.CurrencyDecimal)return t[l.NumberSymbols][_.Decimal];if(i===_.CurrencyGroup)return t[l.NumberSymbols][_.Group]}return n}function Ne(e){if(!e[l.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[l.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function ke(e){let i=D(e);return Ne(i),(i[l.ExtraData][2]||[]).map(n=>typeof n=="string"?re(n):[re(n[0]),re(n[1])])}function $e(e,i,t){let n=D(e);Ne(n);let r=[n[l.ExtraData][0],n[l.ExtraData][1]],s=p(r,i)||[];return p(s,t)||[]}function p(e,i){for(let t=i;t>-1;t--)if(typeof e[t]<"u")return e[t];throw new Error("Locale data API: locale data undefined")}function re(e){let[i,t]=e.split(":");return{hours:+i,minutes:+t}}var nt=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,W={},it=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function xe(e,i,t,n){let r=ft(e);i=S(t,i)||i;let o=[],u;for(;i;)if(u=it.exec(i),u){o=o.concat(u.slice(1));let y=o.pop();if(!y)break;i=y}else{o.push(i);break}let m=r.getTimezoneOffset();n&&(m=ze(n,m),r=dt(r,n));let b="";return o.forEach(y=>{let v=ct(y);b+=v?v(r,t,m):y==="''"?"'":y.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),b}function q(e,i,t){let n=new Date(0);return n.setFullYear(e,i,t),n.setHours(0,0,0),n}function S(e,i){let t=Te(e);if(W[t]??={},W[t][i])return W[t][i];let n="";switch(i){case"shortDate":n=R(e,g.Short);break;case"mediumDate":n=R(e,g.Medium);break;case"longDate":n=R(e,g.Long);break;case"fullDate":n=R(e,g.Full);break;case"shortTime":n=B(e,g.Short);break;case"mediumTime":n=B(e,g.Medium);break;case"longTime":n=B(e,g.Long);break;case"fullTime":n=B(e,g.Full);break;case"short":let r=S(e,"shortTime"),s=S(e,"shortDate");n=Z(P(e,g.Short),[r,s]);break;case"medium":let o=S(e,"mediumTime"),u=S(e,"mediumDate");n=Z(P(e,g.Medium),[o,u]);break;case"long":let m=S(e,"longTime"),b=S(e,"longDate");n=Z(P(e,g.Long),[m,b]);break;case"full":let y=S(e,"fullTime"),v=S(e,"fullDate");n=Z(P(e,g.Full),[y,v]);break}return n&&(W[t][i]=n),n}function Z(e,i){return i&&(e=e.replace(/\{([^}]+)}/g,function(t,n){return i!=null&&n in i?i[n]:t})),e}function F(e,i,t="-",n,r){let s="";(e<0||r&&e<=0)&&(r?e=-e+1:(e=-e,s=t));let o=String(e);for(;o.length<i;)o="0"+o;return n&&(o=o.slice(o.length-i)),s+o}function rt(e,i){return F(e,3).substring(0,i)}function d(e,i,t=0,n=!1,r=!1){return function(s,o){let u=st(e,s);if((t>0||u>-t)&&(u+=t),e===3)u===0&&t===-12&&(u=12);else if(e===6)return rt(u,i);let m=N(o,_.MinusSign);return F(u,i,m,n,r)}}function st(e,i){switch(e){case 0:return i.getFullYear();case 1:return i.getMonth();case 2:return i.getDate();case 3:return i.getHours();case 4:return i.getMinutes();case 5:return i.getSeconds();case 6:return i.getMilliseconds();case 7:return i.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function c(e,i,t=f.Format,n=!1){return function(r,s){return ot(r,s,e,i,t,n)}}function ot(e,i,t,n,r,s){switch(t){case 2:return Be(i,r,n)[e.getMonth()];case 1:return Re(i,r,n)[e.getDay()];case 0:let o=e.getHours(),u=e.getMinutes();if(s){let b=ke(i),y=$e(i,r,n),v=b.findIndex(k=>{if(Array.isArray(k)){let[ee,$]=k,le=o>=ee.hours&&u>=ee.minutes,de=o<$.hours||o===$.hours&&u<$.minutes;if(ee.hours<$.hours){if(le&&de)return!0}else if(le||de)return!0}else if(k.hours===o&&k.minutes===u)return!0;return!1});if(v!==-1)return y[v]}return Oe(i,r,n)[o<12?0:1];case 3:return Pe(i,n)[e.getFullYear()<=0?0:1];default:let m=t;throw new Error(`unexpected translation type ${m}`)}}function Y(e){return function(i,t,n){let r=-1*n,s=N(t,_.MinusSign),o=r>0?Math.floor(r/60):Math.ceil(r/60);switch(e){case 0:return(r>=0?"+":"")+F(o,2,s)+F(Math.abs(r%60),2,s);case 1:return"GMT"+(r>=0?"+":"")+F(o,1,s);case 2:return"GMT"+(r>=0?"+":"")+F(o,2,s)+":"+F(Math.abs(r%60),2,s);case 3:return n===0?"Z":(r>=0?"+":"")+F(o,2,s)+":"+F(Math.abs(r%60),2,s);default:throw new Error(`Unknown zone width "${e}"`)}}}var ut=0,X=4;function at(e){let i=q(e,ut,1).getDay();return q(e,0,1+(i<=X?X:X+7)-i)}function Ue(e){let i=e.getDay(),t=i===0?-3:X-i;return q(e.getFullYear(),e.getMonth(),e.getDate()+t)}function se(e,i=!1){return function(t,n){let r;if(i){let s=new Date(t.getFullYear(),t.getMonth(),1).getDay()-1,o=t.getDate();r=1+Math.floor((o+s)/7)}else{let s=Ue(t),o=at(s.getFullYear()),u=s.getTime()-o.getTime();r=1+Math.round(u/6048e5)}return F(r,e,N(n,_.MinusSign))}}function K(e,i=!1){return function(t,n){let s=Ue(t).getFullYear();return F(s,e,N(n,_.MinusSign),i)}}var oe={};function ct(e){if(oe[e])return oe[e];let i;switch(e){case"G":case"GG":case"GGG":i=c(3,a.Abbreviated);break;case"GGGG":i=c(3,a.Wide);break;case"GGGGG":i=c(3,a.Narrow);break;case"y":i=d(0,1,0,!1,!0);break;case"yy":i=d(0,2,0,!0,!0);break;case"yyy":i=d(0,3,0,!1,!0);break;case"yyyy":i=d(0,4,0,!1,!0);break;case"Y":i=K(1);break;case"YY":i=K(2,!0);break;case"YYY":i=K(3);break;case"YYYY":i=K(4);break;case"M":case"L":i=d(1,1,1);break;case"MM":case"LL":i=d(1,2,1);break;case"MMM":i=c(2,a.Abbreviated);break;case"MMMM":i=c(2,a.Wide);break;case"MMMMM":i=c(2,a.Narrow);break;case"LLL":i=c(2,a.Abbreviated,f.Standalone);break;case"LLLL":i=c(2,a.Wide,f.Standalone);break;case"LLLLL":i=c(2,a.Narrow,f.Standalone);break;case"w":i=se(1);break;case"ww":i=se(2);break;case"W":i=se(1,!0);break;case"d":i=d(2,1);break;case"dd":i=d(2,2);break;case"c":case"cc":i=d(7,1);break;case"ccc":i=c(1,a.Abbreviated,f.Standalone);break;case"cccc":i=c(1,a.Wide,f.Standalone);break;case"ccccc":i=c(1,a.Narrow,f.Standalone);break;case"cccccc":i=c(1,a.Short,f.Standalone);break;case"E":case"EE":case"EEE":i=c(1,a.Abbreviated);break;case"EEEE":i=c(1,a.Wide);break;case"EEEEE":i=c(1,a.Narrow);break;case"EEEEEE":i=c(1,a.Short);break;case"a":case"aa":case"aaa":i=c(0,a.Abbreviated);break;case"aaaa":i=c(0,a.Wide);break;case"aaaaa":i=c(0,a.Narrow);break;case"b":case"bb":case"bbb":i=c(0,a.Abbreviated,f.Standalone,!0);break;case"bbbb":i=c(0,a.Wide,f.Standalone,!0);break;case"bbbbb":i=c(0,a.Narrow,f.Standalone,!0);break;case"B":case"BB":case"BBB":i=c(0,a.Abbreviated,f.Format,!0);break;case"BBBB":i=c(0,a.Wide,f.Format,!0);break;case"BBBBB":i=c(0,a.Narrow,f.Format,!0);break;case"h":i=d(3,1,-12);break;case"hh":i=d(3,2,-12);break;case"H":i=d(3,1);break;case"HH":i=d(3,2);break;case"m":i=d(4,1);break;case"mm":i=d(4,2);break;case"s":i=d(5,1);break;case"ss":i=d(5,2);break;case"S":i=d(6,1);break;case"SS":i=d(6,2);break;case"SSS":i=d(6,3);break;case"Z":case"ZZ":case"ZZZ":i=Y(0);break;case"ZZZZZ":i=Y(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":i=Y(1);break;case"OOOO":case"ZZZZ":case"zzzz":i=Y(2);break;default:return null}return oe[e]=i,i}function ze(e,i){e=e.replace(/:/g,"");let t=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(t)?i:t}function lt(e,i){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+i),e}function dt(e,i,t){let r=e.getTimezoneOffset(),s=ze(i,r);return lt(e,-1*(s-r))}function ft(e){if(ve(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[r,s=1,o=1]=e.split("-").map(u=>+u);return q(r,s-1,o)}let t=parseFloat(e);if(!isNaN(e-t))return new Date(t);let n;if(n=e.match(nt))return ht(n)}let i=new Date(e);if(!ve(i))throw new Error(`Unable to convert "${e}" into a date`);return i}function ht(e){let i=new Date(0),t=0,n=0,r=e[8]?i.setUTCFullYear:i.setFullYear,s=e[8]?i.setUTCHours:i.setHours;e[9]&&(t=Number(e[9]+e[10]),n=Number(e[9]+e[11])),r.call(i,Number(e[1]),Number(e[2])-1,Number(e[3]));let o=Number(e[4]||0)-t,u=Number(e[5]||0)-n,m=Number(e[6]||0),b=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return s.call(i,o,u,m,b),i}function ve(e){return e instanceof Date&&!isNaN(e.valueOf())}var ue=/\s+/,Ie=[],gt=(()=>{class e{_ngEl;_renderer;initialClasses=Ie;rawClass;stateMap=new Map;constructor(t,n){this._ngEl=t,this._renderer=n}set klass(t){this.initialClasses=t!=null?t.trim().split(ue):Ie}set ngClass(t){this.rawClass=typeof t=="string"?t.trim().split(ue):t}ngDoCheck(){for(let n of this.initialClasses)this._updateState(n,!0);let t=this.rawClass;if(Array.isArray(t)||t instanceof Set)for(let n of t)this._updateState(n,!0);else if(t!=null)for(let n of Object.keys(t))this._updateState(n,!!t[n]);this._applyStateDiff()}_updateState(t,n){let r=this.stateMap.get(t);r!==void 0?(r.enabled!==n&&(r.changed=!0,r.enabled=n),r.touched=!0):this.stateMap.set(t,{enabled:n,changed:!0,touched:!0})}_applyStateDiff(){for(let t of this.stateMap){let n=t[0],r=t[1];r.changed?(this._toggleClass(n,r.enabled),r.changed=!1):r.touched||(r.enabled&&this._toggleClass(n,!1),this.stateMap.delete(n)),r.touched=!1}}_toggleClass(t,n){t=t.trim(),t.length>0&&t.split(ue).forEach(r=>{n?this._renderer.addClass(this._ngEl.nativeElement,r):this._renderer.removeClass(this._ngEl.nativeElement,r)})}static \u0275fac=function(n){return new(n||e)(h(U),h(z))};static \u0275dir=L({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var J=class{$implicit;ngForOf;index;count;constructor(i,t,n,r){this.$implicit=i,this.ngForOf=t,this.index=n,this.count=r}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},je=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(t,n,r){this._viewContainer=t,this._template=n,this._differs=r}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let n=this._viewContainer;t.forEachOperation((r,s,o)=>{if(r.previousIndex==null)n.createEmbeddedView(this._template,new J(r.item,this._ngForOf,-1,-1),o===null?void 0:o);else if(o==null)n.remove(s===null?void 0:s);else if(s!==null){let u=n.get(s);n.move(u,o),Le(u,r)}});for(let r=0,s=n.length;r<s;r++){let u=n.get(r).context;u.index=r,u.count=s,u.ngForOf=this._ngForOf}t.forEachIdentityChange(r=>{let s=n.get(r.currentIndex);Le(s,r)})}static ngTemplateContextGuard(t,n){return!0}static \u0275fac=function(n){return new(n||e)(h(j),h(te),h(Fe))};static \u0275dir=L({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Le(e,i){e.context.$implicit=i.item}var Dt=(()=>{class e{_viewContainer;_context=new Q;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,n){this._viewContainer=t,this._thenTemplateRef=n}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){Me(t,!1),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){Me(t,!1),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,n){return!0}static \u0275fac=function(n){return new(n||e)(h(j),h(te))};static \u0275dir=L({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Q=class{$implicit=null;ngIf=null};function Me(e,i){if(e&&!e.createEmbeddedView)throw new x(2020,!1)}var pt=(()=>{class e{_ngEl;_differs;_renderer;_ngStyle=null;_differ=null;constructor(t,n,r){this._ngEl=t,this._differs=n,this._renderer=r}set ngStyle(t){this._ngStyle=t,!this._differ&&t&&(this._differ=this._differs.find(t).create())}ngDoCheck(){if(this._differ){let t=this._differ.diff(this._ngStyle);t&&this._applyChanges(t)}}_setStyle(t,n){let[r,s]=t.split("."),o=r.indexOf("-")===-1?void 0:De.DashCase;n!=null?this._renderer.setStyle(this._ngEl.nativeElement,r,s?`${n}${s}`:n,o):this._renderer.removeStyle(this._ngEl.nativeElement,r,o)}_applyChanges(t){t.forEachRemovedItem(n=>this._setStyle(n.key,null)),t.forEachAddedItem(n=>this._setStyle(n.key,n.currentValue)),t.forEachChangedItem(n=>this._setStyle(n.key,n.currentValue))}static \u0275fac=function(n){return new(n||e)(h(U),h(ye),h(z))};static \u0275dir=L({type:e,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"}})}return e})(),mt=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(t){this._viewContainerRef=t}ngOnChanges(t){if(this._shouldRecreateView(t)){let n=this._viewContainerRef;if(this._viewRef&&n.remove(n.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let r=this._createContextForwardProxy();this._viewRef=n.createEmbeddedView(this.ngTemplateOutlet,r,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,n,r)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,n,r):!1,get:(t,n,r)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,n,r)}})}static \u0275fac=function(n){return new(n||e)(h(j))};static \u0275dir=L({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[ge]})}return e})();function Ve(e,i){return new x(2100,!1)}var Ct="mediumDate",Ge=new w(""),He=new w(""),Ft=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(t,n,r){this.locale=t,this.defaultTimezone=n,this.defaultOptions=r}transform(t,n,r,s){if(t==null||t===""||t!==t)return null;try{let o=n??this.defaultOptions?.dateFormat??Ct,u=r??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return xe(t,o,s||this.locale,u)}catch(o){throw Ve(e,o.message)}}static \u0275fac=function(n){return new(n||e)(h(Ce,16),h(Ge,24),h(He,24))};static \u0275pipe=ne({name:"date",type:e,pure:!0})}return e})();var yt=(()=>{class e{transform(t,n,r){if(t==null)return null;if(!(typeof t=="string"||Array.isArray(t)))throw Ve(e,t);return t.slice(n,r)}static \u0275fac=function(n){return new(n||e)};static \u0275pipe=ne({name:"slice",type:e,pure:!1})}return e})();var Et=(()=>{class e{static \u0275fac=function(n){return new(n||e)};static \u0275mod=pe({type:e});static \u0275inj=he({})}return e})();function wt(e,i){i=encodeURIComponent(i);for(let t of e.split(";")){let n=t.indexOf("="),[r,s]=n==-1?[t,""]:[t.slice(0,n),t.slice(n+1)];if(r.trim()===i)return decodeURIComponent(s)}return null}var We="browser",Ze="server";function At(e){return e===We}function St(e){return e===Ze}var ae=class{};function ai(e,i,t){return me(e,i,t)}var ci=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>new ce(I(T),window)})}return e})(),ce=class{document;window;offset=()=>[0,0];constructor(i,t){this.document=i,this.window=t}setOffset(i){Array.isArray(i)?this.offset=()=>i:this.offset=i}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(i){this.window.scrollTo(i[0],i[1])}scrollToAnchor(i){let t=_t(this.document,i);t&&(this.scrollToElement(t),t.focus())}setHistoryScrollRestoration(i){this.window.history.scrollRestoration=i}scrollToElement(i){let t=i.getBoundingClientRect(),n=t.left+this.window.pageXOffset,r=t.top+this.window.pageYOffset,s=this.offset();this.window.scrollTo(n-s[0],r-s[1])}};function _t(e,i){let t=e.getElementById(i)||e.getElementsByName(i)[0];if(t)return t;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let n=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),r=n.currentNode;for(;r;){let s=r.shadowRoot;if(s){let o=s.getElementById(i)||s.querySelector(`[name="${i}"]`);if(o)return o}r=n.nextNode()}}return null}export{T as a,V as b,Ke as c,ie as d,Xe as e,M as f,_e as g,be as h,tt as i,gt as j,je as k,Dt as l,pt as m,mt as n,Ft as o,yt as p,Et as q,wt as r,We as s,At as t,St as u,ae as v,ai as w,ci as x};
