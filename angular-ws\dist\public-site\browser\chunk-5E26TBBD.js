import{a as E,b as P}from"./chunk-TPWS2CUE.js";import{c as M,d as y}from"./chunk-O7DPC57I.js";import"./chunk-KRRRQ7A6.js";import"./chunk-EFRKI2JO.js";import{c as v,d as T}from"./chunk-7JNW5ZNC.js";import"./chunk-5G3J65ZF.js";import"./chunk-7X7MFIN2.js";import{Z as h}from"./chunk-LS3LVTXN.js";import{g as w}from"./chunk-YFEKHFVJ.js";import"./chunk-BMA7WWEI.js";import{x as _}from"./chunk-CNIH62FZ.js";import{q as k}from"./chunk-D6WDCTDG.js";import{Hb as m,Ib as C,Na as d,Pa as c,Ra as n,ab as u,gb as f,nb as r,ub as b,vb as g,wb as a,xb as s,yb as p}from"./chunk-BL4EGCPV.js";import"./chunk-4CLCTAJ7.js";var z=(o,e)=>e.id;function F(o,e){if(o&1&&p(0,"img",7),o&2){let i=C().$implicit;r("src",i.thumbnailUrl,d)("alt",i.title+" \u5C01\u9762\u56FE\u7247")}}function D(o,e){o&1&&(a(0,"div",8),p(1,"p-button",9)(2,"p-button",10),s()),o&2&&(n(),r("outlined",!0),n(),r("outlined",!0))}function j(o,e){if(o&1&&(a(0,"p-card",2),f(1,F,1,2,"ng-template",5)(2,D,3,2,"ng-template",6),s()),o&2){let i=e.$implicit;r("subheader",i.title)("id","card-"+i.id)}}var R=class o{constructor(){this.totalRecords=50;this.rows=10;this.first=0;this._isMobile=!1;this.cardItems=[{id:"1",name:"Ebook 1",description:"Description for Ebook 1",title:"Ebook 1 Title",thumbnailUrl:"assets/images/\u7075\u97F3.jpg"},{id:"2",name:"Ebook 2",description:"Description for Ebook 2",title:"Ebook 1 Title",thumbnailUrl:"assets/images/\u7075\u97F3.jpg"},{id:"3",name:"Ebook 3",description:"Description for Ebook 3",title:"Ebook 1 Title",thumbnailUrl:"assets/images/\u7075\u97F3.jpg"}];this.checkMobile()}get isMobile(){return this._isMobile}get rowsPerPageOptions(){return this.isMobile?void 0:[10,20,50]}checkMobile(){this._isMobile=window.innerWidth<=768}openBook(){}onPageChange(e){this.first=e.first,this.rows=e.rows,console.log("\u9875\u9762\u53D8\u5316:",e)}onResize(e){this.checkMobile()}static{this.\u0275fac=function(i){return new(i||o)}}static{this.\u0275cmp=u({type:o,selectors:[["app-ebook-card"]],hostBindings:function(i,t){i&1&&m("resize",function(l){return t.onResize(l)},!1,c)},decls:6,vars:6,consts:[[1,"flex","flex-col","p-6"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-4","gap-6","mb-8"],["styleClass","card-item",3,"subheader","id"],[1,"pagination-container"],["styleClass","custom-paginator",3,"onPageChange","first","rows","totalRecords","rowsPerPageOptions","showPageLinks","showCurrentPageReport"],["pTemplate","header"],["pTemplate","footer"],[1,"rounded-t-xl","w-56",3,"src","alt"],[1,"flex","justify-between","items-center"],["size","small","label","\u9605\u8BFB","icon","pi pi-book",3,"outlined"],["size","small","label","\u542C\u4E66","icon","pi pi-bookmark",3,"outlined"]],template:function(i,t){i&1&&(a(0,"div",0)(1,"div",1),b(2,j,3,2,"p-card",2,z),s(),a(4,"div",3)(5,"p-paginator",4),m("onPageChange",function(l){return t.onPageChange(l)}),s()()()),i&2&&(n(2),g(t.cardItems),n(3),r("first",t.first)("rows",t.rows)("totalRecords",t.totalRecords)("rowsPerPageOptions",t.rowsPerPageOptions)("showPageLinks",!t.isMobile)("showCurrentPageReport",t.isMobile))},dependencies:[k,_,P,E,h,w,y,M,T,v],styles:["[_nghost-%COMP%]     p-card{display:flex}"]})}};export{R as EbookCardComponent};
