import{a as p}from"./chunk-4CLCTAJ7.js";var y=class o{static isArray(t,e=!0){return Array.isArray(t)&&(e||t.length!==0)}static isObject(t,e=!0){return typeof t=="object"&&!Array.isArray(t)&&t!=null&&(e||Object.keys(t).length!==0)}static equals(t,e,n){return n?this.resolveFieldData(t,n)===this.resolveFieldData(e,n):this.equalsByValue(t,e)}static equalsByValue(t,e){if(t===e)return!0;if(t&&e&&typeof t=="object"&&typeof e=="object"){var n=Array.isArray(t),s=Array.isArray(e),r,i,a;if(n&&s){if(i=t.length,i!=e.length)return!1;for(r=i;r--!==0;)if(!this.equalsByValue(t[r],e[r]))return!1;return!0}if(n!=s)return!1;var u=this.isDate(t),f=this.isDate(e);if(u!=f)return!1;if(u&&f)return t.getTime()==e.getTime();var l=t instanceof RegExp,d=e instanceof RegExp;if(l!=d)return!1;if(l&&d)return t.toString()==e.toString();var c=Object.keys(t);if(i=c.length,i!==Object.keys(e).length)return!1;for(r=i;r--!==0;)if(!Object.prototype.hasOwnProperty.call(e,c[r]))return!1;for(r=i;r--!==0;)if(a=c[r],!this.equalsByValue(t[a],e[a]))return!1;return!0}return t!==t&&e!==e}static resolveFieldData(t,e){if(t&&e){if(this.isFunction(e))return e(t);if(e.indexOf(".")==-1)return t[e];{let n=e.split("."),s=t;for(let r=0,i=n.length;r<i;++r){if(s==null)return null;s=s[n[r]]}return s}}else return null}static isFunction(t){return!!(t&&t.constructor&&t.call&&t.apply)}static reorderArray(t,e,n){let s;t&&e!==n&&(n>=t.length&&(n%=t.length,e%=t.length),t.splice(n,0,t.splice(e,1)[0]))}static insertIntoOrderedArray(t,e,n,s){if(n.length>0){let r=!1;for(let i=0;i<n.length;i++)if(this.findIndexInList(n[i],s)>e){n.splice(i,0,t),r=!0;break}r||n.push(t)}else n.push(t)}static findIndexInList(t,e){let n=-1;if(e){for(let s=0;s<e.length;s++)if(e[s]==t){n=s;break}}return n}static contains(t,e){if(t!=null&&e&&e.length){for(let n of e)if(this.equals(t,n))return!0}return!1}static removeAccents(t){return t&&(t=t.normalize("NFKD").replace(new RegExp("\\p{Diacritic}","gu"),"")),t}static isDate(t){return Object.prototype.toString.call(t)==="[object Date]"}static isEmpty(t){return t==null||t===""||Array.isArray(t)&&t.length===0||!this.isDate(t)&&typeof t=="object"&&Object.keys(t).length===0}static isNotEmpty(t){return!this.isEmpty(t)}static compare(t,e,n,s=1){let r=-1,i=this.isEmpty(t),a=this.isEmpty(e);return i&&a?r=0:i?r=s:a?r=-s:typeof t=="string"&&typeof e=="string"?r=t.localeCompare(e,n,{numeric:!0}):r=t<e?-1:t>e?1:0,r}static sort(t,e,n=1,s,r=1){let i=o.compare(t,e,s,n),a=n;return(o.isEmpty(t)||o.isEmpty(e))&&(a=r===1?n:r),a*i}static merge(t,e){if(!(t==null&&e==null)){{if((t==null||typeof t=="object")&&(e==null||typeof e=="object"))return p(p({},t||{}),e||{});if((t==null||typeof t=="string")&&(e==null||typeof e=="string"))return[t||"",e||""].join(" ")}return e||t}}static isPrintableCharacter(t=""){return this.isNotEmpty(t)&&t.length===1&&t.match(/\S| /)}static getItemValue(t,...e){return this.isFunction(t)?t(...e):t}static findLastIndex(t,e){let n=-1;if(this.isNotEmpty(t))try{n=t.findLastIndex(e)}catch{n=t.lastIndexOf([...t].reverse().find(e))}return n}static findLast(t,e){let n;if(this.isNotEmpty(t))try{n=t.findLast(e)}catch{n=[...t].reverse().find(e)}return n}static deepEquals(t,e){if(t===e)return!0;if(t&&e&&typeof t=="object"&&typeof e=="object"){var n=Array.isArray(t),s=Array.isArray(e),r,i,a;if(n&&s){if(i=t.length,i!=e.length)return!1;for(r=i;r--!==0;)if(!this.deepEquals(t[r],e[r]))return!1;return!0}if(n!=s)return!1;var u=t instanceof Date,f=e instanceof Date;if(u!=f)return!1;if(u&&f)return t.getTime()==e.getTime();var l=t instanceof RegExp,d=e instanceof RegExp;if(l!=d)return!1;if(l&&d)return t.toString()==e.toString();var c=Object.keys(t);if(i=c.length,i!==Object.keys(e).length)return!1;for(r=i;r--!==0;)if(!Object.prototype.hasOwnProperty.call(e,c[r]))return!1;for(r=i;r--!==0;)if(a=c[r],!this.deepEquals(t[a],e[a]))return!1;return!0}return t!==t&&e!==e}static minifyCSS(t){return t&&t.replace(/\/\*(?:(?!\*\/)[\s\S])*\*\/|[\r\n\t]+/g,"").replace(/ {2,}/g," ").replace(/ ([{:}]) /g,"$1").replace(/([;,]) /g,"$1").replace(/ !/g,"!").replace(/: /g,":")}static toFlatCase(t){return this.isString(t)?t.replace(/(-|_)/g,"").toLowerCase():t}static isString(t,e=!0){return typeof t=="string"&&(e||t!=="")}},m=0;function v(o="pn_id_"){return m++,`${o}${m}`}function h(){let o=[],t=(r,i)=>{let a=o.length>0?o[o.length-1]:{key:r,value:i},u=a.value+(a.key===r?0:i)+2;return o.push({key:r,value:u}),u},e=r=>{o=o.filter(i=>i.value!==r)},n=()=>o.length>0?o[o.length-1].value:0,s=r=>r&&parseInt(r.style.zIndex,10)||0;return{get:s,set:(r,i,a)=>{i&&(i.style.zIndex=String(t(r,a)))},clear:r=>{r&&(e(s(r)),r.style.zIndex="")},getCurrent:()=>n(),generateZIndex:t,revertZIndex:e}}var V=h(),$=o=>!!o;export{y as a,v as b,V as c,$ as d};
