﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class Addbucketnametofiletable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "BucketFileUrls",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "BucketFileUrls",
                type: "timestamp without time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "BucketFileUrls",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "BucketName",
                table: "BucketFiles",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "BucketFileUrls");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "BucketFileUrls");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "BucketFileUrls");

            migrationBuilder.DropColumn(
                name: "BucketName",
                table: "BucketFiles");
        }
    }
}
