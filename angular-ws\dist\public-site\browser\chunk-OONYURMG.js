import{a as De}from"./chunk-XDERG77Q.js";import{a as Se,b as Me,c as pe,f as ze,g as Le,h as Ue,j as Ae,k as Re,l as Be}from"./chunk-7X7MFIN2.js";import{$ as Te,K as ye,L as Ne,M as ke,R as se,Z as te,_ as V,a as ee,aa as ve,ca as ie,ea as K,fa as Ie,ga as we,ha as Ee,p as de,q as be,r as Ce}from"./chunk-LS3LVTXN.js";import{a as Ve,c as Fe,d as $e,e as Oe,g as he}from"./chunk-YFEKHFVJ.js";import{j as P,k as ae,l as Q,m as ce,n as H,q}from"./chunk-D6WDCTDG.js";import{$b as D,Ab as v,Bb as L,Cb as M,Hb as I,Ib as c,Nb as k,Ob as U,Pb as m,Q as j,Qb as x,R as W,Ra as d,S as G,Sb as re,Tb as _e,Ub as me,Wa as fe,X as R,Zb as J,_b as xe,ab as $,ac as le,bb as Y,ca as X,da as h,ea as g,eb as O,fa as ge,ga as F,gb as s,lc as B,ma as C,mb as y,nb as a,ob as Z,qb as z,ra as ue,rb as w,sb as ne,uc as _,vc as E,wb as u,xb as f,yb as N,zb as T}from"./chunk-BL4EGCPV.js";import{a as A}from"./chunk-4CLCTAJ7.js";var Qe=(()=>{class t extends Ie{static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(t)))(o||t)}})();static \u0275cmp=$({type:t,selectors:[["MinusIcon"]],features:[O],decls:2,vars:5,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M13.2222 7.77778H0.777778C0.571498 7.77778 0.373667 7.69584 0.227806 7.54998C0.0819442 7.40412 0 7.20629 0 7.00001C0 6.79373 0.0819442 6.5959 0.227806 6.45003C0.373667 6.30417 0.571498 6.22223 0.777778 6.22223H13.2222C13.4285 6.22223 13.6263 6.30417 13.7722 6.45003C13.9181 6.5959 14 6.79373 14 7.00001C14 7.20629 13.9181 7.40412 13.7722 7.54998C13.6263 7.69584 13.4285 7.77778 13.2222 7.77778Z","fill","currentColor"]],template:function(i,o){i&1&&(ge(),u(0,"svg",0),N(1,"path",1),f()),i&2&&(w(o.getClassNames()),y("aria-label",o.ariaLabel)("aria-hidden",o.ariaHidden)("role",o.role))},encapsulation:2})}return t})();var Je=["checkboxicon"],et=["input"],tt=()=>({"p-checkbox-input":!0}),it=t=>({checked:t,class:"p-checkbox-icon"});function ot(t,l){if(t&1&&N(0,"span",8),t&2){let e=c(3);a("ngClass",e.checkboxIcon),y("data-pc-section","icon")}}function nt(t,l){t&1&&N(0,"CheckIcon",9),t&2&&(a("styleClass","p-checkbox-icon"),y("data-pc-section","icon"))}function rt(t,l){if(t&1&&(T(0),s(1,ot,1,2,"span",7)(2,nt,1,2,"CheckIcon",6),v()),t&2){let e=c(2);d(),a("ngIf",e.checkboxIcon),d(),a("ngIf",!e.checkboxIcon)}}function lt(t,l){t&1&&N(0,"MinusIcon",9),t&2&&(a("styleClass","p-checkbox-icon"),y("data-pc-section","icon"))}function at(t,l){if(t&1&&(T(0),s(1,rt,3,2,"ng-container",4)(2,lt,1,2,"MinusIcon",6),v()),t&2){let e=c();d(),a("ngIf",e.checked),d(),a("ngIf",e._indeterminate())}}function ct(t,l){}function dt(t,l){t&1&&s(0,ct,0,0,"ng-template")}var st=({dt:t})=>`
.p-checkbox {
    position: relative;
    display: inline-flex;
    user-select: none;
    vertical-align: bottom;
    width: ${t("checkbox.width")};
    height: ${t("checkbox.height")};
}

.p-checkbox-input {
    cursor: pointer;
    appearance: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    opacity: 0;
    z-index: 1;
    outline: 0 none;
    border: 1px solid transparent;
    border-radius: ${t("checkbox.border.radius")};
}

.p-checkbox-box {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: ${t("checkbox.border.radius")};
    border: 1px solid ${t("checkbox.border.color")};
    background: ${t("checkbox.background")};
    width: ${t("checkbox.width")};
    height: ${t("checkbox.height")};
    transition: background ${t("checkbox.transition.duration")}, color ${t("checkbox.transition.duration")}, border-color ${t("checkbox.transition.duration")}, box-shadow ${t("checkbox.transition.duration")}, outline-color ${t("checkbox.transition.duration")};
    outline-color: transparent;
    box-shadow: ${t("checkbox.shadow")};
}

.p-checkbox-icon {
    transition-duration: ${t("checkbox.transition.duration")};
    color: ${t("checkbox.icon.color")};
    font-size: ${t("checkbox.icon.size")};
    width: ${t("checkbox.icon.size")};
    height: ${t("checkbox.icon.size")};
}

.p-checkbox:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box {
    border-color: ${t("checkbox.hover.border.color")};
}

.p-checkbox-checked .p-checkbox-box {
    border-color: ${t("checkbox.checked.border.color")};
    background: ${t("checkbox.checked.background")};
}

.p-checkbox-checked .p-checkbox-icon {
    color: ${t("checkbox.icon.checked.color")};
}

.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box {
    background: ${t("checkbox.checked.hover.background")};
    border-color: ${t("checkbox.checked.hover.border.color")};
}

.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-icon {
    color: ${t("checkbox.icon.checked.hover.color")};
}

.p-checkbox:not(.p-disabled):has(.p-checkbox-input:focus-visible) .p-checkbox-box {
    border-color: ${t("checkbox.focus.border.color")};
    box-shadow: ${t("checkbox.focus.ring.shadow")};
    outline: ${t("checkbox.focus.ring.width")} ${t("checkbox.focus.ring.style")} ${t("checkbox.focus.ring.color")};
    outline-offset: ${t("checkbox.focus.ring.offset")};
}

.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:focus-visible) .p-checkbox-box {
    border-color: ${t("checkbox.checked.focus.border.color")};
}

p-checkBox.ng-invalid.ng-dirty .p-checkbox-box,
p-check-box.ng-invalid.ng-dirty .p-checkbox-box,
p-checkbox.ng-invalid.ng-dirty .p-checkbox-box {
    border-color: ${t("checkbox.invalid.border.color")};
}

.p-checkbox.p-variant-filled .p-checkbox-box {
    background: ${t("checkbox.filled.background")};
}

.p-checkbox-checked.p-variant-filled .p-checkbox-box {
    background: ${t("checkbox.checked.background")};
}

.p-checkbox-checked.p-variant-filled:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box {
    background: ${t("checkbox.checked.hover.background")};
}

.p-checkbox.p-disabled {
    opacity: 1;
}

.p-checkbox.p-disabled .p-checkbox-box {
    background: ${t("checkbox.disabled.background")};
    border-color: ${t("checkbox.checked.disabled.border.color")};
}

.p-checkbox.p-disabled .p-checkbox-box .p-checkbox-icon {
    color: ${t("checkbox.icon.disabled.color")};
}

.p-checkbox-sm,
.p-checkbox-sm .p-checkbox-box {
    width: ${t("checkbox.sm.width")};
    height: ${t("checkbox.sm.height")};
}

.p-checkbox-sm .p-checkbox-icon {
    font-size: ${t("checkbox.icon.sm.size")};
    width: ${t("checkbox.icon.sm.size")};
    height: ${t("checkbox.icon.sm.size")};
}

.p-checkbox-lg,
.p-checkbox-lg .p-checkbox-box {
    width: ${t("checkbox.lg.width")};
    height: ${t("checkbox.lg.height")};
}

.p-checkbox-lg .p-checkbox-icon {
    font-size: ${t("checkbox.icon.lg.size")};
    width: ${t("checkbox.icon.lg.size")};
    height: ${t("checkbox.icon.lg.size")};
}
`,pt={root:({instance:t,props:l})=>["p-checkbox p-component",{"p-checkbox-checked":t.checked,"p-disabled":l.disabled,"p-invalid":l.invalid,"p-variant-filled":l.variant?l.variant==="filled":t.config.inputStyle==="filled"||t.config.inputVariant==="filled"}],box:"p-checkbox-box",input:"p-checkbox-input",icon:"p-checkbox-icon"},He=(()=>{class t extends ie{name="checkbox";theme=st;classes=pt;static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(t)))(o||t)}})();static \u0275prov=W({token:t,factory:t.\u0275fac})}return t})();var ht={provide:Ve,useExisting:j(()=>oe),multi:!0},oe=(()=>{class t extends K{value;name;disabled;binary;ariaLabelledBy;ariaLabel;tabindex;inputId;style;inputStyle;styleClass;inputClass;indeterminate=!1;size;formControl;checkboxIcon;readonly;required;autofocus;trueValue=!0;falseValue=!1;variant;onChange=new C;onFocus=new C;onBlur=new C;inputViewChild;get checked(){return this._indeterminate()?!1:this.binary?this.model===this.trueValue:ke(this.value,this.model)}get containerClass(){return{"p-checkbox p-component":!0,"p-checkbox-checked p-highlight":this.checked,"p-disabled":this.disabled,"p-variant-filled":this.variant==="filled"||this.config.inputStyle()==="filled"||this.config.inputVariant()==="filled","p-checkbox-sm p-inputfield-sm":this.size==="small","p-checkbox-lg p-inputfield-lg":this.size==="large"}}_indeterminate=ue(void 0);checkboxIconTemplate;templates;_checkboxIconTemplate;model;onModelChange=()=>{};onModelTouched=()=>{};focused=!1;_componentStyle=R(He);ngAfterContentInit(){this.templates.forEach(e=>{switch(e.getType()){case"icon":this._checkboxIconTemplate=e.template;break;case"checkboxicon":this._checkboxIconTemplate=e.template;break}})}ngOnChanges(e){super.ngOnChanges(e),e.indeterminate&&this._indeterminate.set(e.indeterminate.currentValue)}updateModel(e){let i,o=this.injector.get(Fe,null,{optional:!0,self:!0}),n=o&&!this.formControl?o.value:this.model;this.binary?(i=this._indeterminate()?this.trueValue:this.checked?this.falseValue:this.trueValue,this.model=i,this.onModelChange(i)):(this.checked||this._indeterminate()?i=n.filter(r=>!Ne(r,this.value)):i=n?[...n,this.value]:[this.value],this.onModelChange(i),this.model=i,this.formControl&&this.formControl.setValue(i)),this._indeterminate()&&this._indeterminate.set(!1),this.onChange.emit({checked:i,originalEvent:e})}handleChange(e){this.readonly||this.updateModel(e)}onInputFocus(e){this.focused=!0,this.onFocus.emit(e)}onInputBlur(e){this.focused=!1,this.onBlur.emit(e),this.onModelTouched()}focus(){this.inputViewChild.nativeElement.focus()}writeValue(e){this.model=e,this.cd.markForCheck()}registerOnChange(e){this.onModelChange=e}registerOnTouched(e){this.onModelTouched=e}setDisabledState(e){setTimeout(()=>{this.disabled=e,this.cd.markForCheck()})}static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(t)))(o||t)}})();static \u0275cmp=$({type:t,selectors:[["p-checkbox"],["p-checkBox"],["p-check-box"]],contentQueries:function(i,o,n){if(i&1&&(k(n,Je,4),k(n,te,4)),i&2){let r;m(r=x())&&(o.checkboxIconTemplate=r.first),m(r=x())&&(o.templates=r)}},viewQuery:function(i,o){if(i&1&&U(et,5),i&2){let n;m(n=x())&&(o.inputViewChild=n.first)}},inputs:{value:"value",name:"name",disabled:[2,"disabled","disabled",_],binary:[2,"binary","binary",_],ariaLabelledBy:"ariaLabelledBy",ariaLabel:"ariaLabel",tabindex:[2,"tabindex","tabindex",E],inputId:"inputId",style:"style",inputStyle:"inputStyle",styleClass:"styleClass",inputClass:"inputClass",indeterminate:[2,"indeterminate","indeterminate",_],size:"size",formControl:"formControl",checkboxIcon:"checkboxIcon",readonly:[2,"readonly","readonly",_],required:[2,"required","required",_],autofocus:[2,"autofocus","autofocus",_],trueValue:"trueValue",falseValue:"falseValue",variant:"variant"},outputs:{onChange:"onChange",onFocus:"onFocus",onBlur:"onBlur"},features:[J([ht,He]),O,X],decls:6,vars:29,consts:[["input",""],[3,"ngClass"],["type","checkbox",3,"focus","blur","change","value","checked","disabled","readonly","ngClass"],[1,"p-checkbox-box"],[4,"ngIf"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"styleClass",4,"ngIf"],["class","p-checkbox-icon",3,"ngClass",4,"ngIf"],[1,"p-checkbox-icon",3,"ngClass"],[3,"styleClass"]],template:function(i,o){if(i&1){let n=M();u(0,"div",1)(1,"input",2,0),I("focus",function(p){return h(n),g(o.onInputFocus(p))})("blur",function(p){return h(n),g(o.onInputBlur(p))})("change",function(p){return h(n),g(o.handleChange(p))}),f(),u(3,"div",3),s(4,at,3,2,"ng-container",4)(5,dt,1,0,null,5),f()()}i&2&&(z(o.style),w(o.styleClass),a("ngClass",o.containerClass),y("data-p-highlight",o.checked)("data-p-checked",o.checked)("data-p-disabled",o.disabled),d(),z(o.inputStyle),w(o.inputClass),a("value",o.value)("checked",o.checked)("disabled",o.disabled)("readonly",o.readonly)("ngClass",xe(26,tt)),y("id",o.inputId)("name",o.name)("tabindex",o.tabindex)("required",o.required?!0:null)("aria-labelledby",o.ariaLabelledBy)("aria-label",o.ariaLabel),d(3),a("ngIf",!o.checkboxIconTemplate&&!o._checkboxIconTemplate),d(),a("ngTemplateOutlet",o.checkboxIconTemplate||o._checkboxIconTemplate)("ngTemplateOutletContext",D(27,it,o.checked)))},dependencies:[q,P,Q,H,Se,Qe,V],encapsulation:2,changeDetection:0})}return t})(),Xi=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=Y({type:t});static \u0275inj=G({imports:[oe,V,V]})}return t})();var je=t=>({height:t}),We=t=>({"p-tree-node-droppoint-active":t}),gt=(t,l)=>({$implicit:t,loading:l}),ut=(t,l)=>({$implicit:t,partialSelected:l,class:"p-tree-node-checkbox"}),Ge=t=>({$implicit:t});function ft(t,l){if(t&1){let e=M();u(0,"li",11),I("drop",function(o){h(e);let n=c(2);return g(n.onDropPoint(o,-1))})("dragover",function(o){h(e);let n=c(2);return g(n.onDropPointDragOver(o))})("dragenter",function(o){h(e);let n=c(2);return g(n.onDropPointDragEnter(o,-1))})("dragleave",function(o){h(e);let n=c(2);return g(n.onDropPointDragLeave(o))}),f()}if(t&2){let e=c(2);a("ngClass",D(2,We,e.draghoverPrev)),y("aria-hidden",!0)}}function _t(t,l){t&1&&N(0,"ChevronRightIcon",13),t&2&&a("styleClass","p-tree-node-toggle-icon")}function mt(t,l){t&1&&N(0,"ChevronDownIcon",13),t&2&&a("styleClass","p-tree-node-toggle-icon")}function xt(t,l){if(t&1&&(T(0),s(1,_t,1,1,"ChevronRightIcon",12)(2,mt,1,1,"ChevronDownIcon",12),v()),t&2){let e=c(3);d(),a("ngIf",!e.node.expanded),d(),a("ngIf",e.node.expanded)}}function bt(t,l){t&1&&(T(0),N(1,"SpinnerIcon",13),v()),t&2&&(d(),a("styleClass","pi-spin p-tree-node-toggle-icon"))}function Ct(t,l){if(t&1&&(T(0),s(1,xt,3,2,"ng-container",5)(2,bt,2,1,"ng-container",5),v()),t&2){let e=c(2);d(),a("ngIf",!e.node.loading),d(),a("ngIf",e.loadingMode==="icon"&&e.node.loading)}}function yt(t,l){}function Nt(t,l){t&1&&s(0,yt,0,0,"ng-template")}function kt(t,l){if(t&1&&(u(0,"span",14),s(1,Nt,1,0,null,15),f()),t&2){let e=c(2);d(),a("ngTemplateOutlet",e.tree.togglerIconTemplate||e.tree._togglerIconTemplate)("ngTemplateOutletContext",le(2,gt,e.node.expanded,e.node.loading))}}function Tt(t,l){}function vt(t,l){t&1&&s(0,Tt,0,0,"ng-template")}function It(t,l){if(t&1&&s(0,vt,1,0,null,15),t&2){let e=c(4);a("ngTemplateOutlet",e.tree.checkboxIconTemplate||e.tree._checkboxIconTemplate)("ngTemplateOutletContext",le(2,ut,e.isSelected(),e.node.partialSelected))}}function St(t,l){t&1&&(T(0),s(1,It,1,5,"ng-template",null,0,B),v())}function wt(t,l){if(t&1){let e=M();u(0,"p-checkbox",16),I("click",function(o){return h(e),g(o.preventDefault())}),s(1,St,3,0,"ng-container",5),f()}if(t&2){let e=c(2);a("ngModel",e.isSelected())("binary",!0)("indeterminate",e.node.partialSelected)("disabled",e.node.selectable===!1)("variant",(e.tree==null?null:e.tree.config.inputStyle())==="filled"||(e.tree==null?null:e.tree.config.inputVariant())==="filled"?"filled":"outlined")("tabindex",-1),y("data-p-partialchecked",e.node.partialSelected),d(),a("ngIf",e.tree.checkboxIconTemplate||e.tree._checkboxIconTemplate)}}function Dt(t,l){if(t&1&&N(0,"span"),t&2){let e=c(2);w(e.getIcon())}}function Mt(t,l){if(t&1&&(u(0,"span"),re(1),f()),t&2){let e=c(2);d(),_e(e.node.label)}}function Et(t,l){t&1&&L(0)}function Vt(t,l){if(t&1&&(u(0,"span"),s(1,Et,1,0,"ng-container",15),f()),t&2){let e=c(2);d(),a("ngTemplateOutlet",e.tree.getTemplateForNode(e.node))("ngTemplateOutletContext",D(2,Ge,e.node))}}function Ft(t,l){if(t&1&&N(0,"p-treeNode",19),t&2){let e=l.$implicit,i=l.first,o=l.last,n=l.index,r=c(3);a("node",e)("parentNode",r.node)("firstChild",i)("lastChild",o)("index",n)("itemSize",r.itemSize)("level",r.level+1)("loadingMode",r.loadingMode)}}function $t(t,l){if(t&1&&(u(0,"ul",17),s(1,Ft,1,8,"p-treeNode",18),f()),t&2){let e=c(2);Z("display",e.node.expanded?"flex":"none"),d(),a("ngForOf",e.node.children)("ngForTrackBy",e.tree.trackBy.bind(e))}}function Ot(t,l){if(t&1){let e=M();u(0,"li",11),I("drop",function(o){h(e);let n=c(2);return g(n.onDropPoint(o,1))})("dragover",function(o){h(e);let n=c(2);return g(n.onDropPointDragOver(o))})("dragenter",function(o){h(e);let n=c(2);return g(n.onDropPointDragEnter(o,1))})("dragleave",function(o){h(e);let n=c(2);return g(n.onDropPointDragLeave(o))}),f()}if(t&2){let e=c(2);a("ngClass",D(2,We,e.draghoverNext)),y("aria-hidden",!0)}}function zt(t,l){if(t&1){let e=M();s(0,ft,1,4,"li",1),u(1,"li",2),I("keydown",function(o){h(e);let n=c();return g(n.onKeyDown(o))}),u(2,"div",3),I("click",function(o){h(e);let n=c();return g(n.onNodeClick(o))})("contextmenu",function(o){h(e);let n=c();return g(n.onNodeRightClick(o))})("dblclick",function(o){h(e);let n=c();return g(n.onNodeDblClick(o))})("touchend",function(){h(e);let o=c();return g(o.onNodeTouchEnd())})("drop",function(o){h(e);let n=c();return g(n.onDropNode(o))})("dragover",function(o){h(e);let n=c();return g(n.onDropNodeDragOver(o))})("dragenter",function(o){h(e);let n=c();return g(n.onDropNodeDragEnter(o))})("dragleave",function(o){h(e);let n=c();return g(n.onDropNodeDragLeave(o))})("dragstart",function(o){h(e);let n=c();return g(n.onDragStart(o))})("dragend",function(o){h(e);let n=c();return g(n.onDragStop(o))}),u(3,"button",4),I("click",function(o){h(e);let n=c();return g(n.toggle(o))}),s(4,Ct,3,2,"ng-container",5)(5,kt,2,5,"span",6),f(),s(6,wt,2,8,"p-checkbox",7)(7,Dt,1,2,"span",8),u(8,"span",9),s(9,Mt,2,1,"span",5)(10,Vt,2,4,"span",5),f()(),s(11,$t,2,4,"ul",10),f(),s(12,Ot,1,4,"li",1)}if(t&2){let e=c();a("ngIf",e.tree.droppableNodes),d(),z(e.node.style),w(e.node.styleClass),a("ngClass",e.nodeClass)("ngStyle",D(29,je,e.itemSize+"px")),y("aria-label",e.node.label)("aria-checked",e.checked)("aria-setsize",e.node.children?e.node.children.length:0)("aria-selected",e.selected)("aria-expanded",e.node.expanded)("aria-posinset",e.index+1)("aria-level",e.level+1)("tabindex",e.index===0?0:-1)("data-id",e.node.key),d(),Z("padding-left",e.level*e.indentation+"rem"),a("ngClass",e.nodeContentClass)("draggable",e.tree.draggableNodes),d(),y("data-pc-section","toggler"),d(),a("ngIf",!e.tree.togglerIconTemplate&&!e.tree._togglerIconTemplate),d(),a("ngIf",e.tree.togglerIconTemplate||e.tree._togglerIconTemplate),d(),a("ngIf",e.tree.selectionMode=="checkbox"),d(),a("ngIf",e.node.icon||e.node.expandedIcon||e.node.collapsedIcon),d(2),a("ngIf",!e.tree.getTemplateForNode(e.node)),d(),a("ngIf",e.tree.getTemplateForNode(e.node)),d(),a("ngIf",!e.tree.virtualScroll&&e.node.children&&e.node.expanded),d(),a("ngIf",e.tree.droppableNodes&&e.lastChild)}}var qe=["filter"],Lt=["node"],Ut=["header"],At=["footer"],Rt=["loader"],Bt=["empty"],Pt=["togglericon"],Qt=["checkboxicon"],Ht=["loadingicon"],qt=["filtericon"],Kt=["scroller"],jt=["wrapper"],Wt=t=>({options:t});function Gt(t,l){if(t&1&&N(0,"i"),t&2){let e=c(2);w("p-tree-loading-icon pi-spin "+e.loadingIcon)}}function Xt(t,l){t&1&&N(0,"SpinnerIcon",16),t&2&&a("spin",!0)("styleClass","p-tree-loading-icon")}function Yt(t,l){}function Zt(t,l){t&1&&s(0,Yt,0,0,"ng-template")}function Jt(t,l){if(t&1&&(u(0,"span",17),s(1,Zt,1,0,null,9),f()),t&2){let e=c(3);d(),a("ngTemplateOutlet",e.loadingIconTemplate||e._loadingIconTemplate)}}function ei(t,l){if(t&1&&(T(0),s(1,Xt,1,2,"SpinnerIcon",14)(2,Jt,2,1,"span",15),v()),t&2){let e=c(2);d(),a("ngIf",!e.loadingIconTemplate&&!e._loadingIconTemplate),d(),a("ngIf",e.loadingIconTemplate||e._loadingIconTemplate)}}function ti(t,l){if(t&1&&(u(0,"div",12),s(1,Gt,1,2,"i",13)(2,ei,3,2,"ng-container",10),f()),t&2){let e=c();d(),a("ngIf",e.loadingIcon),d(),a("ngIf",!e.loadingIcon)}}function ii(t,l){t&1&&L(0)}function oi(t,l){t&1&&L(0)}function ni(t,l){if(t&1&&s(0,oi,1,0,"ng-container",18),t&2){let e=c();a("ngTemplateOutlet",e.filterTemplate||e._filterTemplate)("ngTemplateOutletContext",D(2,Ge,e.filterOptions))}}function ri(t,l){t&1&&N(0,"SearchIcon",21)}function li(t,l){}function ai(t,l){t&1&&s(0,li,0,0,"ng-template")}function ci(t,l){if(t&1&&(u(0,"span"),s(1,ai,1,0,null,9),f()),t&2){let e=c(3);d(),a("ngTemplateOutlet",e.filterIconTemplate||e._filterIconTemplate)}}function di(t,l){if(t&1){let e=M();u(0,"p-iconField")(1,"input",19,0),I("keydown.enter",function(o){return h(e),g(o.preventDefault())})("input",function(o){h(e);let n=c(2);return g(n._filter(o.target.value))}),f(),u(3,"p-inputIcon"),s(4,ri,1,0,"SearchIcon",20)(5,ci,2,1,"span",10),f()()}if(t&2){let e=c(2);d(),a("pAutoFocus",e.filterInputAutoFocus),y("placeholder",e.filterPlaceholder),d(3),a("ngIf",!e.filterIconTemplate&&!e._filterIconTemplate),d(),a("ngIf",e.filterIconTemplate||e._filterIconTemplate)}}function si(t,l){if(t&1&&s(0,di,6,4,"p-iconField",10),t&2){let e=c();a("ngIf",e.filter)}}function pi(t,l){if(t&1&&N(0,"p-treeNode",27,3),t&2){let e=l.$implicit,i=l.first,o=l.last,n=l.index,r=c(2).options,p=c(3);a("level",e.level)("rowNode",e)("node",e.node)("parentNode",e.parent)("firstChild",i)("lastChild",o)("index",p.getIndex(r,n))("itemSize",r.itemSize)("indentation",p.indentation)("loadingMode",p.loadingMode)}}function hi(t,l){if(t&1&&(u(0,"ul",25),s(1,pi,2,10,"p-treeNode",26),f()),t&2){let e=c(),i=e.$implicit,o=e.options,n=c(3);z(o.contentStyle),a("ngClass",o.contentStyleClass),y("aria-label",n.ariaLabel)("aria-labelledby",n.ariaLabelledBy),d(),a("ngForOf",i)("ngForTrackBy",n.trackBy)}}function gi(t,l){if(t&1&&s(0,hi,2,7,"ul",24),t&2){let e=l.$implicit;a("ngIf",e)}}function ui(t,l){t&1&&L(0)}function fi(t,l){if(t&1&&s(0,ui,1,0,"ng-container",18),t&2){let e=l.options,i=c(4);a("ngTemplateOutlet",i.loaderTemplate||i._loaderTemplate)("ngTemplateOutletContext",D(2,Wt,e))}}function _i(t,l){t&1&&(T(0),s(1,fi,1,4,"ng-template",null,4,B),v())}function mi(t,l){if(t&1){let e=M();u(0,"p-scroller",23,1),I("onScroll",function(o){h(e);let n=c(2);return g(n.onScroll.emit(o))})("onScrollIndexChange",function(o){h(e);let n=c(2);return g(n.onScrollIndexChange.emit(o))})("onLazyLoad",function(o){h(e);let n=c(2);return g(n.onLazyLoad.emit(o))}),s(2,gi,1,1,"ng-template",null,2,B)(4,_i,3,0,"ng-container",10),f()}if(t&2){let e=c(2);z(D(9,je,e.scrollHeight!=="flex"?e.scrollHeight:void 0)),a("items",e.serializedValue)("tabindex",-1)("scrollHeight",e.scrollHeight!=="flex"?void 0:"100%")("itemSize",e.virtualScrollItemSize||e._virtualNodeHeight)("lazy",e.lazy)("options",e.virtualScrollOptions),d(4),a("ngIf",e.loaderTemplate||e._loaderTemplate)}}function xi(t,l){if(t&1&&N(0,"p-treeNode",32),t&2){let e=l.$implicit,i=l.first,o=l.last,n=l.index,r=c(4);a("node",e)("firstChild",i)("lastChild",o)("index",n)("level",0)("loadingMode",r.loadingMode)}}function bi(t,l){if(t&1&&(u(0,"ul",30),s(1,xi,1,6,"p-treeNode",31),f()),t&2){let e=c(3);y("aria-label",e.ariaLabel)("aria-labelledby",e.ariaLabelledBy),d(),a("ngForOf",e.getRootNode())("ngForTrackBy",e.trackBy.bind(e))}}function Ci(t,l){if(t&1&&(T(0),u(1,"div",28,5),s(3,bi,2,4,"ul",29),f(),v()),t&2){let e=c(2);d(),Z("max-height",e.scrollHeight),d(2),a("ngIf",e.getRootNode())}}function yi(t,l){if(t&1&&(T(0),s(1,mi,5,11,"p-scroller",22)(2,Ci,4,3,"ng-container",10),v()),t&2){let e=c();d(),a("ngIf",e.virtualScroll),d(),a("ngIf",!e.virtualScroll)}}function Ni(t,l){if(t&1&&(T(0),re(1),v()),t&2){let e=c(2);d(),me(" ",e.emptyMessageLabel," ")}}function ki(t,l){}function Ti(t,l){t&1&&s(0,ki,0,0,"ng-template",null,6,B)}function vi(t,l){if(t&1&&(u(0,"div",33),s(1,Ni,2,1,"ng-container",34)(2,Ti,2,0,null,9),f()),t&2){let e=c();d(),a("ngIf",!e.emptyMessageTemplate&&!e._emptyMessageTemplate)("ngIfElse",e.emptyFilter),d(),a("ngTemplateOutlet",e.emptyMessageTemplate||e._emptyMessageTemplate)}}function Ii(t,l){t&1&&L(0)}var Si=({dt:t})=>`
.p-tree {
    background: ${t("tree.background")};
    color: ${t("tree.color")};
    padding: ${t("tree.padding")};
}

.p-tree-root-children,
.p-tree-node-children {
    display: flex;
    list-style-type: none;
    flex-direction: column;
    margin: 0;
    gap: ${t("tree.gap")};
}

.p-tree-root-children {
    padding: 0;
    padding-block-start: ${t("tree.gap")};
}

.p-tree-node-children {
    padding-block-start: ${t("tree.gap")};
    padding-inline-start: ${t("tree.indent")};
}

.p-tree-node {
    padding: 0;
    outline: 0 none;
}

.p-tree-node-content {
    border-radius: ${t("tree.node.border.radius")};
    padding: ${t("tree.node.padding")};
    display: flex;
    align-items: center;
    outline-color: transparent;
    color: ${t("tree.node.color")};
    gap: ${t("tree.node.gap")};
    transition: background ${t("tree.transition.duration")}, color ${t("tree.transition.duration")}, outline-color ${t("tree.transition.duration")}, box-shadow ${t("tree.transition.duration")};
}

.p-tree-node:focus-visible > .p-tree-node-content {
    box-shadow: ${t("tree.node.focus.ring.shadow")};
    outline: ${t("tree.node.focus.ring.width")} ${t("tree.node.focus.ring.style")} ${t("tree.node.focus.ring.color")};
    outline-offset: ${t("tree.node.focus.ring.offset")};
}

.p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover {
    background: ${t("tree.node.hover.background")};
    color: ${t("tree.node.hover.color")};
}

.p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover .p-tree-node-icon {
    color: ${t("tree.node.icon.hover.color")};
}

.p-tree-node-content.p-tree-node-selected {
    background: ${t("tree.node.selected.background")};
    color: ${t("tree.node.selected.color")};
}

.p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button {
    color: inherit;
}

.p-tree-node-toggle-button {
    cursor: pointer;
    user-select: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    flex-shrink: 0;
    width: ${t("tree.node.toggle.button.size")};
    height: ${t("tree.node.toggle.button.size")};
    color: ${t("tree.node.toggle.button.color")};
    border: 0 none;
    background: transparent;
    border-radius: ${t("tree.node.toggle.button.border.radius")};
    transition: background ${t("tree.transition.duration")}, color ${t("tree.transition.duration")}, border-color ${t("tree.transition.duration")}, outline-color ${t("tree.transition.duration")}, box-shadow ${t("tree.transition.duration")};
    outline-color: transparent;
    padding: 0;
}

.p-tree-node-toggle-button:enabled:hover {
    background: ${t("tree.node.toggle.button.hover.background")};
    color: ${t("tree.node.toggle.button.hover.color")};
}

.p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button:hover {
    background: ${t("tree.node.toggle.button.selected.hover.background")};
    color: ${t("tree.node.toggle.button.selected.hover.color")};
}

.p-tree-root {
    overflow: auto;
}

.p-tree-node-selectable {
    cursor: pointer;
    user-select: none;
}

.p-tree-node-leaf > .p-tree-node-content .p-tree-node-toggle-button {
    visibility: hidden;
}

.p-tree-node-icon {
    color: ${t("tree.node.icon.color")};
    transition: color ${t("tree.transition.duration")};
}

.p-tree-node-content.p-tree-node-selected .p-tree-node-icon {
    color: ${t("tree.node.icon.selected.color")};
}

.p-tree-filter-input {
    width: 100%;
}

.p-tree-loading {
    position: relative;
    height: 100%;
}

.p-tree-loading-icon {
    font-size: ${t("tree.loading.icon.size")};
    width: ${t("tree.loading.icon.size")};
    height: ${t("tree.loading.icon.size")};
}

.p-tree .p-tree-mask {
    position: absolute;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.p-tree-flex-scrollable {
    display: flex;
    flex: 1;
    height: 100%;
    flex-direction: column;
}

.p-tree-flex-scrollable .p-tree-root {
    flex: 1;
}

/* For PrimeNG */
.p-tree .p-tree-node-droppoint {
    height: 4px;
    list-style-type: none;
}

.p-tree .p-tree-node-droppoint-active {
    border: 0 none;
    background-color: ${t("primary.color")};
}

.p-tree-node-content.p-tree-node-dragover {
    background: ${t("tree.node.hover.background")};
    color: ${t("tree.node.hover.color")};
}

.p-tree-node-content.p-tree-node-dragover .p-tree-node-icon {
    color: ${t("tree.node.icon.hover.color")};
}

.p-tree-horizontal {
    width: auto;
    padding-inline-start: 0;
    padding-inline-end: 0;
    overflow: auto;
}

.p-tree.p-tree-horizontal table,
.p-tree.p-tree-horizontal tr,
.p-tree.p-tree-horizontal td {
    border-collapse: collapse;
    margin: 0;
    padding: 0;
    vertical-align: middle;
}

.p-tree-horizontal .p-tree-node-content {
    font-weight: normal;
    padding: 0.4em 1em 0.4em 0.2em;
    display: flex;
    align-items: center;
}

.p-tree-horizontal .p-tree-node-parent .p-tree-node-content {
    font-weight: normal;
    white-space: nowrap;
}

.p-tree.p-tree-horizontal .p-tree-node.p-tree-node-leaf,
.p-tree.p-tree-horizontal .p-tree-node.p-tree-node-collapsed {
    padding-inline-end: 0;
}

.p-tree.p-tree-horizontal .p-tree-node-children {
    padding: 0;
    margin: 0;
}

.p-tree.p-tree-horizontal .p-tree-node-connector {
    width: 1px;
}

.p-tree.p-tree-horizontal .p-tree-node-connector-table {
    height: 100%;
    width: 1px;
}

.p-tree.p-tree-horizontal table {
    height: 0;
}
`,wi={root:({instance:t})=>({"p-tree p-component":!0,"p-tree-selectable":t.selectionMode!=null,"p-tree-loading":t.loading,"p-tree-flex-scrollable":t.scrollHeight==="flex","p-tree-node-dragover":t.dragHover}),mask:"p-tree-mask p-overlay-mask",loadingIcon:"p-tree-loading-icon",pcFilterInput:"p-tree-filter-input",wrapper:"p-tree-root",rootChildren:"p-tree-root-children",node:({instance:t})=>({"p-tree-node":!0,"p-tree-node-leaf":t.isLeaf()}),nodeContent:({instance:t})=>({"p-tree-node-content":!0,[t.styleClass]:!!t.styleClass,"p-tree-node-selectable":t.selectable,"p-tree-node-dragover":t.draghoverNode,"p-tree-node-selected":t.selectionMode==="checkbox"&&t.tree.highlightOnSelect?t.checked:t.selected}),nodeToggleButton:"p-tree-node-toggle-button",nodeToggleIcon:"p-tree-node-toggle-icon",nodeCheckbox:"p-tree-node-checkbox",nodeIcon:"p-tree-node-icon",nodeLabel:"p-tree-node-label",nodeChildren:"p-tree-node-children"},Ke=(()=>{class t extends ie{name="tree";theme=Si;classes=wi;static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(t)))(o||t)}})();static \u0275prov=W({token:t,factory:t.\u0275fac})}return t})();var Di=(()=>{class t extends K{static ICON_CLASS="p-tree-node-icon ";rowNode;node;parentNode;root;index;firstChild;lastChild;level;indentation;itemSize;loadingMode;tree=R(j(()=>Xe));timeout;draghoverPrev;draghoverNext;draghoverNode;get selected(){return this.tree.selectionMode==="single"||this.tree.selectionMode==="multiple"?this.isSelected():void 0}get checked(){return this.tree.selectionMode==="checkbox"?this.isSelected():void 0}get nodeClass(){return this.tree._componentStyle.classes.node({instance:this})}get nodeContentClass(){return this.tree._componentStyle.classes.nodeContent({instance:this})}get selectable(){return this.node.selectable===!1?!1:this.tree.selectionMode!=null}ngOnInit(){super.ngOnInit(),this.node.parent=this.parentNode;let i=this.tree.el.nativeElement.closest("p-dialog");this.parentNode&&!i&&(this.setAllNodesTabIndexes(),this.tree.syncNodeOption(this.node,this.tree.value,"parent",this.tree.getNodeWithKey(this.parentNode.key,this.tree.value)))}getIcon(){let e;return this.node.icon?e=this.node.icon:e=this.node.expanded&&this.node.children&&this.node.children?.length?this.node.expandedIcon:this.node.collapsedIcon,t.ICON_CLASS+" "+e+" p-tree-node-icon"}isLeaf(){return this.tree.isNodeLeaf(this.node)}toggle(e){this.node.expanded?this.collapse(e):this.expand(e),e.stopPropagation()}expand(e){this.node.expanded=!0,this.tree.virtualScroll&&(this.tree.updateSerializedValue(),this.focusVirtualNode()),this.tree.onNodeExpand.emit({originalEvent:e,node:this.node})}collapse(e){this.node.expanded=!1,this.tree.virtualScroll&&(this.tree.updateSerializedValue(),this.focusVirtualNode()),this.tree.onNodeCollapse.emit({originalEvent:e,node:this.node})}onNodeClick(e){this.tree.onNodeClick(e,this.node)}onNodeKeydown(e){e.key==="Enter"&&this.tree.onNodeClick(e,this.node)}onNodeTouchEnd(){this.tree.onNodeTouchEnd()}onNodeRightClick(e){this.tree.onNodeRightClick(e,this.node)}onNodeDblClick(e){this.tree.onNodeDblClick(e,this.node)}isSelected(){return this.tree.isSelected(this.node)}isSameNode(e){return e.currentTarget&&(e.currentTarget.isSameNode(e.target)||e.currentTarget.isSameNode(e.target.closest('[role="treeitem"]')))}onDropPoint(e,i){e.preventDefault();let o=this.tree.dragNode,n=this.tree.dragNodeIndex,r=this.tree.dragNodeScope,p=this.tree.dragNodeTree===this.tree?i===1||n!==this.index-1:!0;if(this.tree.allowDrop(o,this.node,r)&&p){let b=A({},this.createDropPointEventMetadata(i));this.tree.validateDrop?this.tree.onNodeDrop.emit({originalEvent:e,dragNode:o,dropNode:this.node,index:this.index,accept:()=>{this.processPointDrop(b)}}):(this.processPointDrop(b),this.tree.onNodeDrop.emit({originalEvent:e,dragNode:o,dropNode:this.node,index:this.index}))}this.draghoverPrev=!1,this.draghoverNext=!1}processPointDrop(e){let i=e.dropNode.parent?e.dropNode.parent.children:this.tree.value;e.dragNodeSubNodes.splice(e.dragNodeIndex,1);let o=this.index;e.position<0?(o=e.dragNodeSubNodes===i?e.dragNodeIndex>e.index?e.index:e.index-1:e.index,i.splice(o,0,e.dragNode)):(o=i.length,i.push(e.dragNode)),this.tree.dragDropService.stopDrag({node:e.dragNode,subNodes:e.dropNode.parent?e.dropNode.parent.children:this.tree.value,index:e.dragNodeIndex})}createDropPointEventMetadata(e){return{dragNode:this.tree.dragNode,dragNodeIndex:this.tree.dragNodeIndex,dragNodeSubNodes:this.tree.dragNodeSubNodes,dropNode:this.node,index:this.index,position:e}}onDropPointDragOver(e){e.dataTransfer.dropEffect="move",e.preventDefault()}onDropPointDragEnter(e,i){this.tree.allowDrop(this.tree.dragNode,this.node,this.tree.dragNodeScope)&&(i<0?this.draghoverPrev=!0:this.draghoverNext=!0)}onDropPointDragLeave(e){this.draghoverPrev=!1,this.draghoverNext=!1}onDragStart(e){this.tree.draggableNodes&&this.node.draggable!==!1?(e.dataTransfer.setData("text","data"),this.tree.dragDropService.startDrag({tree:this,node:this.node,subNodes:this.node?.parent?this.node.parent.children:this.tree.value,index:this.index,scope:this.tree.draggableScope})):e.preventDefault()}onDragStop(e){this.tree.dragDropService.stopDrag({node:this.node,subNodes:this.node?.parent?this.node.parent.children:this.tree.value,index:this.index})}onDropNodeDragOver(e){e.dataTransfer.dropEffect="move",this.tree.droppableNodes&&(e.preventDefault(),e.stopPropagation())}onDropNode(e){if(this.tree.droppableNodes&&this.node?.droppable!==!1){let i=this.tree.dragNode;if(this.tree.allowDrop(i,this.node,this.tree.dragNodeScope)){let o=A({},this.createDropNodeEventMetadata());this.tree.validateDrop?this.tree.onNodeDrop.emit({originalEvent:e,dragNode:i,dropNode:this.node,index:this.index,accept:()=>{this.processNodeDrop(o)}}):(this.processNodeDrop(o),this.tree.onNodeDrop.emit({originalEvent:e,dragNode:i,dropNode:this.node,index:this.index}))}}e.preventDefault(),e.stopPropagation(),this.draghoverNode=!1}createDropNodeEventMetadata(){return{dragNode:this.tree.dragNode,dragNodeIndex:this.tree.dragNodeIndex,dragNodeSubNodes:this.tree.dragNodeSubNodes,dropNode:this.node}}processNodeDrop(e){let i=e.dragNodeIndex;e.dragNodeSubNodes.splice(i,1),e.dropNode.children?e.dropNode.children.push(e.dragNode):e.dropNode.children=[e.dragNode],this.tree.dragDropService.stopDrag({node:e.dragNode,subNodes:e.dropNode.parent?e.dropNode.parent.children:this.tree.value,index:i})}onDropNodeDragEnter(e){this.tree.droppableNodes&&this.node?.droppable!==!1&&this.tree.allowDrop(this.tree.dragNode,this.node,this.tree.dragNodeScope)&&(this.draghoverNode=!0)}onDropNodeDragLeave(e){if(this.tree.droppableNodes){let i=e.currentTarget.getBoundingClientRect();(e.x>i.left+i.width||e.x<i.left||e.y>=Math.floor(i.top+i.height)||e.y<i.top)&&(this.draghoverNode=!1)}}onKeyDown(e){if(!(!this.isSameNode(e)||this.tree.contextMenu&&this.tree.contextMenu.containerViewChild?.nativeElement.style.display==="block"))switch(e.code){case"ArrowDown":this.onArrowDown(e);break;case"ArrowUp":this.onArrowUp(e);break;case"ArrowRight":this.onArrowRight(e);break;case"ArrowLeft":this.onArrowLeft(e);break;case"Enter":case"Space":case"NumpadEnter":this.onEnter(e);break;case"Tab":this.setAllNodesTabIndexes();break;default:break}}onArrowUp(e){let i=e.target.getAttribute("data-pc-section")==="toggler"?e.target.closest('[role="treeitem"]'):e.target.parentElement;if(i.previousElementSibling)this.focusRowChange(i,i.previousElementSibling,this.findLastVisibleDescendant(i.previousElementSibling));else{let o=this.getParentNodeElement(i);o&&this.focusRowChange(i,o)}e.preventDefault()}onArrowDown(e){let i=e.target.getAttribute("data-pc-section")==="toggler"?e.target.closest('[role="treeitem"]'):e.target,o=i.children[1];if(o&&o.children.length>0)this.focusRowChange(i,o.children[0]);else if(i.parentElement.nextElementSibling)this.focusRowChange(i,i.parentElement.nextElementSibling);else{let n=this.findNextSiblingOfAncestor(i.parentElement);n&&this.focusRowChange(i,n)}e.preventDefault()}onArrowRight(e){!this.node?.expanded&&!this.tree.isNodeLeaf(this.node)&&(this.expand(e),e.currentTarget.tabIndex=-1,setTimeout(()=>{this.onArrowDown(e)},1)),e.preventDefault()}onArrowLeft(e){let i=e.target.getAttribute("data-pc-section")==="toggler"?e.target.closest('[role="treeitem"]'):e.target;if(this.level===0&&!this.node?.expanded)return!1;if(this.node?.expanded){this.collapse(e);return}let o=this.getParentNodeElement(i.parentElement);o&&this.focusRowChange(e.currentTarget,o),e.preventDefault()}onEnter(e){this.tree.onNodeClick(e,this.node),this.setTabIndexForSelectionMode(e,this.tree.nodeTouched),e.preventDefault()}setAllNodesTabIndexes(){let e=de(this.tree.el.nativeElement,".p-tree-node"),i=[...e].some(o=>o.getAttribute("aria-selected")==="true"||o.getAttribute("aria-checked")==="true");if([...e].forEach(o=>{o.tabIndex=-1}),i){let o=[...e].filter(n=>n.getAttribute("aria-selected")==="true"||n.getAttribute("aria-checked")==="true");o[0].tabIndex=0;return}e.length&&([...e][0].tabIndex=0)}setTabIndexForSelectionMode(e,i){if(this.tree.selectionMode!==null){let o=[...de(this.tree.el.nativeElement,'[role="treeitem"]')];e.currentTarget.tabIndex=i===!1?-1:0,o.every(n=>n.tabIndex===-1)&&(o[0].tabIndex=0)}}findNextSiblingOfAncestor(e){let i=this.getParentNodeElement(e);return i?i.nextElementSibling?i.nextElementSibling:this.findNextSiblingOfAncestor(i):null}findLastVisibleDescendant(e){let o=Array.from(e.children).find(n=>ee(n,"p-tree-node"))?.children[1];if(o&&o.children.length>0){let n=o.children[o.children.length-1];return this.findLastVisibleDescendant(n)}else return e}getParentNodeElement(e){let i=e.parentElement?.parentElement?.parentElement;return i?.tagName==="P-TREENODE"?i:null}focusNode(e){this.tree.droppableNodes?e.children[1].focus():e.children[0].focus()}focusRowChange(e,i,o){e.tabIndex="-1",i.children[0].tabIndex="0",this.focusNode(o||i)}focusVirtualNode(){this.timeout=setTimeout(()=>{let e=be(document.body,`[data-id="${this.node?.key??this.node?.data}"]`);Ce(e)},1)}static \u0275fac=(()=>{let e;return function(o){return(e||(e=F(t)))(o||t)}})();static \u0275cmp=$({type:t,selectors:[["p-treeNode"]],inputs:{rowNode:"rowNode",node:"node",parentNode:"parentNode",root:[2,"root","root",_],index:[2,"index","index",E],firstChild:[2,"firstChild","firstChild",_],lastChild:[2,"lastChild","lastChild",_],level:[2,"level","level",E],indentation:[2,"indentation","indentation",E],itemSize:[2,"itemSize","itemSize",E],loadingMode:"loadingMode"},features:[O],decls:1,vars:1,consts:[["icon",""],["class","p-tree-node-droppoint",3,"ngClass","drop","dragover","dragenter","dragleave",4,"ngIf"],["role","treeitem",3,"keydown","ngClass","ngStyle"],[3,"click","contextmenu","dblclick","touchend","drop","dragover","dragenter","dragleave","dragstart","dragend","ngClass","draggable"],["type","button","pRipple","","tabindex","-1",1,"p-tree-node-toggle-button",3,"click"],[4,"ngIf"],["class","p-tree-node-toggle-icon",4,"ngIf"],["styleClass","p-tree-node-checkbox",3,"ngModel","binary","indeterminate","disabled","variant","tabindex","click",4,"ngIf"],[3,"class",4,"ngIf"],[1,"p-tree-node-label"],["class","p-tree-node-children","style","display: none;","role","group",3,"display",4,"ngIf"],[1,"p-tree-node-droppoint",3,"drop","dragover","dragenter","dragleave","ngClass"],[3,"styleClass",4,"ngIf"],[3,"styleClass"],[1,"p-tree-node-toggle-icon"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],["styleClass","p-tree-node-checkbox",3,"click","ngModel","binary","indeterminate","disabled","variant","tabindex"],["role","group",1,"p-tree-node-children",2,"display","none"],[3,"node","parentNode","firstChild","lastChild","index","itemSize","level","loadingMode",4,"ngFor","ngForOf","ngForTrackBy"],[3,"node","parentNode","firstChild","lastChild","index","itemSize","level","loadingMode"]],template:function(i,o){i&1&&s(0,zt,13,31),i&2&&ne(o.node?0:-1)},dependencies:[t,q,P,ae,Q,H,ce,Ee,oe,he,$e,Oe,De,we,pe,V],encapsulation:2})}return t})(),Xe=(()=>{class t extends K{dragDropService;value;selectionMode;loadingMode="mask";selection;style;styleClass;contextMenu;draggableScope;droppableScope;draggableNodes;droppableNodes;metaKeySelection=!1;propagateSelectionUp=!0;propagateSelectionDown=!0;loading;loadingIcon;emptyMessage="";ariaLabel;togglerAriaLabel;ariaLabelledBy;validateDrop;filter;filterInputAutoFocus=!1;filterBy="label";filterMode="lenient";filterOptions;filterPlaceholder;filteredNodes;filterLocale;scrollHeight;lazy=!1;virtualScroll;virtualScrollItemSize;virtualScrollOptions;indentation=1.5;_templateMap;trackBy=(e,i)=>i;highlightOnSelect=!1;_virtualNodeHeight;get virtualNodeHeight(){return this._virtualNodeHeight}set virtualNodeHeight(e){this._virtualNodeHeight=e,console.log("The virtualNodeHeight property is deprecated, use virtualScrollItemSize property instead.")}selectionChange=new C;onNodeSelect=new C;onNodeUnselect=new C;onNodeExpand=new C;onNodeCollapse=new C;onNodeContextMenuSelect=new C;onNodeDoubleClick=new C;onNodeDrop=new C;onLazyLoad=new C;onScroll=new C;onScrollIndexChange=new C;onFilter=new C;filterTemplate;nodeTemplate;headerTemplate;footerTemplate;loaderTemplate;emptyMessageTemplate;togglerIconTemplate;checkboxIconTemplate;loadingIconTemplate;filterIconTemplate;filterViewChild;scroller;wrapperViewChild;templates;_headerTemplate;_emptyMessageTemplate;_footerTemplate;_loaderTemplate;_togglerIconTemplate;_checkboxIconTemplate;_loadingIconTemplate;_filterIconTemplate;_filterTemplate;ngAfterContentInit(){this.templates.length&&(this._templateMap={}),this.templates.forEach(e=>{switch(e.getType()){case"header":this._headerTemplate=e.template;break;case"empty":this._emptyMessageTemplate=e.template;break;case"footer":this._footerTemplate=e.template;break;case"loader":this._loaderTemplate=e.template;break;case"togglericon":this._togglerIconTemplate=e.template;break;case"checkboxicon":this._checkboxIconTemplate=e.template;break;case"loadingicon":this._loadingIconTemplate=e.template;break;case"filtericon":this._filterIconTemplate=e.template;break;case"filter":this._filterTemplate=e.template;break;default:this._templateMap[e.name]=e.template;break}})}serializedValue;nodeTouched;dragNodeTree;dragNode;dragNodeSubNodes;dragNodeIndex;dragNodeScope;dragHover;dragStartSubscription;dragStopSubscription;_componentStyle=R(Ke);constructor(e){super(),this.dragDropService=e}ngOnInit(){super.ngOnInit(),this.filterBy&&(this.filterOptions={filter:e=>this._filter(e),reset:()=>this.resetFilter()}),this.droppableNodes&&(this.dragStartSubscription=this.dragDropService.dragStart$.subscribe(e=>{this.dragNodeTree=e.tree,this.dragNode=e.node,this.dragNodeSubNodes=e.subNodes,this.dragNodeIndex=e.index,this.dragNodeScope=e.scope}),this.dragStopSubscription=this.dragDropService.dragStop$.subscribe(e=>{this.dragNodeTree=null,this.dragNode=null,this.dragNodeSubNodes=null,this.dragNodeIndex=null,this.dragNodeScope=null,this.dragHover=!1}))}ngOnChanges(e){super.ngOnChanges(e),e.value&&(this.updateSerializedValue(),this.hasFilterActive()&&this._filter(this.filterViewChild.nativeElement.value))}get containerClass(){return this._componentStyle.classes.root({instance:this})}get emptyMessageLabel(){return this.emptyMessage||this.config.getTranslation(Te.EMPTY_MESSAGE)}updateSerializedValue(){this.serializedValue=[],this.serializeNodes(null,this.getRootNode(),0,!0)}serializeNodes(e,i,o,n){if(i&&i.length)for(let r of i){r.parent=e;let p={node:r,parent:e,level:o,visible:n&&(e?e.expanded:!0)};this.serializedValue.push(p),p.visible&&r.expanded&&this.serializeNodes(r,r.children,o+1,p.visible)}}onNodeClick(e,i){let o=e.target;if(!(ee(o,"p-tree-toggler")||ee(o,"p-tree-toggler-icon"))){if(this.selectionMode){if(i.selectable===!1){i.style="--p-focus-ring-color: none;";return}else i.style?.includes("--p-focus-ring-color")||(i.style=i.style?`${i.style}--p-focus-ring-color: var(--primary-color)`:"--p-focus-ring-color: var(--primary-color)");if(this.hasFilteredNodes()&&(i=this.getNodeWithKey(i.key,this.filteredNodes),!i))return;let n=this.findIndexInSelection(i),r=n>=0;if(this.isCheckboxSelectionMode())r?(this.propagateSelectionDown?this.propagateDown(i,!1):this.selection=this.selection.filter((p,b)=>b!=n),this.propagateSelectionUp&&i.parent&&this.propagateUp(i.parent,!1),this.selectionChange.emit(this.selection),this.onNodeUnselect.emit({originalEvent:e,node:i})):(this.propagateSelectionDown?this.propagateDown(i,!0):this.selection=[...this.selection||[],i],this.propagateSelectionUp&&i.parent&&this.propagateUp(i.parent,!0),this.selectionChange.emit(this.selection),this.onNodeSelect.emit({originalEvent:e,node:i}));else if(this.nodeTouched?!1:this.metaKeySelection){let b=e.metaKey||e.ctrlKey;r&&b?(this.isSingleSelectionMode()?this.selectionChange.emit(null):(this.selection=this.selection.filter((S,Ye)=>Ye!=n),this.selectionChange.emit(this.selection)),this.onNodeUnselect.emit({originalEvent:e,node:i})):(this.isSingleSelectionMode()?this.selectionChange.emit(i):this.isMultipleSelectionMode()&&(this.selection=b?this.selection||[]:[],this.selection=[...this.selection,i],this.selectionChange.emit(this.selection)),this.onNodeSelect.emit({originalEvent:e,node:i}))}else this.isSingleSelectionMode()?r?(this.selection=null,this.onNodeUnselect.emit({originalEvent:e,node:i})):(this.selection=i,setTimeout(()=>{this.onNodeSelect.emit({originalEvent:e,node:i})})):r?(this.selection=this.selection.filter((b,S)=>S!=n),this.onNodeUnselect.emit({originalEvent:e,node:i})):(this.selection=[...this.selection||[],i],setTimeout(()=>{this.onNodeSelect.emit({originalEvent:e,node:i})})),this.selectionChange.emit(this.selection)}this.nodeTouched=!1}}onNodeTouchEnd(){this.nodeTouched=!0}onNodeRightClick(e,i){if(this.contextMenu){let o=e.target;if(o.className&&o.className.indexOf("p-tree-toggler")===0)return;this.findIndexInSelection(i)>=0||(this.isSingleSelectionMode()?this.selectionChange.emit(i):this.selectionChange.emit([i])),this.contextMenu.show(e),this.onNodeContextMenuSelect.emit({originalEvent:e,node:i})}}onNodeDblClick(e,i){this.onNodeDoubleClick.emit({originalEvent:e,node:i})}findIndexInSelection(e){let i=-1;if(this.selectionMode&&this.selection)if(this.isSingleSelectionMode())i=this.selection.key&&this.selection.key===e.key||this.selection==e?0:-1;else for(let o=0;o<this.selection.length;o++){let n=this.selection[o];if(n.key&&n.key===e.key||n==e){i=o;break}}return i}syncNodeOption(e,i,o,n){let r=this.hasFilteredNodes()?this.getNodeWithKey(e.key,i):null;r&&(r[o]=n||e[o])}hasFilteredNodes(){return this.filter&&this.filteredNodes&&this.filteredNodes.length}hasFilterActive(){return this.filter&&this.filterViewChild?.nativeElement?.value.length>0}getNodeWithKey(e,i){for(let o of i){if(o.key===e)return o;if(o.children){let n=this.getNodeWithKey(e,o.children);if(n)return n}}}propagateUp(e,i){if(e.children&&e.children.length){let n=0,r=!1;for(let p of e.children)this.isSelected(p)?n++:p.partialSelected&&(r=!0);if(i&&n==e.children.length)this.selection=[...this.selection||[],e],e.partialSelected=!1;else{if(!i){let p=this.findIndexInSelection(e);p>=0&&(this.selection=this.selection.filter((b,S)=>S!=p))}r||n>0&&n!=e.children.length?e.partialSelected=!0:e.partialSelected=!1}this.syncNodeOption(e,this.filteredNodes,"partialSelected")}let o=e.parent;o&&this.propagateUp(o,i)}propagateDown(e,i){let o=this.findIndexInSelection(e);if(i&&o==-1?this.selection=[...this.selection||[],e]:!i&&o>-1&&(this.selection=this.selection.filter((n,r)=>r!=o)),e.partialSelected=!1,this.syncNodeOption(e,this.filteredNodes,"partialSelected"),e.children&&e.children.length)for(let n of e.children)this.propagateDown(n,i)}isSelected(e){return this.findIndexInSelection(e)!=-1}isSingleSelectionMode(){return this.selectionMode&&this.selectionMode=="single"}isMultipleSelectionMode(){return this.selectionMode&&this.selectionMode=="multiple"}isCheckboxSelectionMode(){return this.selectionMode&&this.selectionMode=="checkbox"}isNodeLeaf(e){return e.leaf==!1?!1:!(e.children&&e.children.length)}getRootNode(){return this.filteredNodes?this.filteredNodes:this.value}getTemplateForNode(e){return this._templateMap?e.type?this._templateMap[e.type]:this._templateMap.default:null}onDragOver(e){this.droppableNodes&&(!this.value||this.value.length===0)&&(e.dataTransfer.dropEffect="move",e.preventDefault())}onDrop(e){if(this.droppableNodes&&(!this.value||this.value.length===0)){e.preventDefault();let i=this.dragNode;if(this.allowDrop(i,null,this.dragNodeScope)){let o=this.dragNodeIndex;this.value=this.value||[],this.validateDrop?this.onNodeDrop.emit({originalEvent:e,dragNode:i,dropNode:null,index:o,accept:()=>{this.processTreeDrop(i,o)}}):(this.onNodeDrop.emit({originalEvent:e,dragNode:i,dropNode:null,index:o}),this.processTreeDrop(i,o))}}}processTreeDrop(e,i){this.dragNodeSubNodes.splice(i,1),this.value.push(e),this.dragDropService.stopDrag({node:e})}onDragEnter(){this.droppableNodes&&this.allowDrop(this.dragNode,null,this.dragNodeScope)&&(this.dragHover=!0)}onDragLeave(e){if(this.droppableNodes){let i=e.currentTarget.getBoundingClientRect();(e.x>i.left+i.width||e.x<i.left||e.y>i.top+i.height||e.y<i.top)&&(this.dragHover=!1)}}allowDrop(e,i,o){if(e)if(this.isValidDragScope(o)){let n=!0;if(i)if(e===i)n=!1;else{let r=i.parent;for(;r!=null;){if(r===e){n=!1;break}r=r.parent}}return n}else return!1;else return!1}isValidDragScope(e){let i=this.droppableScope;if(i){if(typeof i=="string"){if(typeof e=="string")return i===e;if(Array.isArray(e))return e.indexOf(i)!=-1}else if(Array.isArray(i)){if(typeof e=="string")return i.indexOf(e)!=-1;if(Array.isArray(e)){for(let o of i)for(let n of e)if(o===n)return!0}}return!1}else return!0}_filter(e){let i=e;if(i==="")this.filteredNodes=null;else{this.filteredNodes=[];let o=this.filterBy.split(","),n=se(i).toLocaleLowerCase(this.filterLocale),r=this.filterMode==="strict";for(let p of this.value){let b=A({},p),S={searchFields:o,filterText:n,isStrictMode:r};(r&&(this.findFilteredNodes(b,S)||this.isFilterMatched(b,S))||!r&&(this.isFilterMatched(b,S)||this.findFilteredNodes(b,S)))&&this.filteredNodes.push(b)}}this.updateSerializedValue(),this.onFilter.emit({filter:i,filteredValue:this.filteredNodes})}resetFilter(){this.filteredNodes=null,this.filterViewChild&&this.filterViewChild.nativeElement&&(this.filterViewChild.nativeElement.value="")}scrollToVirtualIndex(e){this.virtualScroll&&this.scroller?.scrollToIndex(e)}scrollTo(e){this.virtualScroll?this.scroller?.scrollTo(e):this.wrapperViewChild&&this.wrapperViewChild.nativeElement&&(this.wrapperViewChild.nativeElement.scrollTo?this.wrapperViewChild.nativeElement.scrollTo(e):(this.wrapperViewChild.nativeElement.scrollLeft=e.left,this.wrapperViewChild.nativeElement.scrollTop=e.top))}findFilteredNodes(e,i){if(e){let o=!1;if(e.children){let n=[...e.children];e.children=[];for(let r of n){let p=A({},r);this.isFilterMatched(p,i)&&(o=!0,e.children.push(p))}}if(o)return e.expanded=!0,!0}}isFilterMatched(e,i){let{searchFields:o,filterText:n,isStrictMode:r}=i,p=!1;for(let b of o)se(String(ye(e,b))).toLocaleLowerCase(this.filterLocale).indexOf(n)>-1&&(p=!0);return(!p||r&&!this.isNodeLeaf(e))&&(p=this.findFilteredNodes(e,{searchFields:o,filterText:n,isStrictMode:r})||p),p}getIndex(e,i){let o=e.getItemOptions;return o?o(i).index:i}getBlockableElement(){return this.el.nativeElement.children[0]}ngOnDestroy(){this.dragStartSubscription&&this.dragStartSubscription.unsubscribe(),this.dragStopSubscription&&this.dragStopSubscription.unsubscribe(),super.ngOnDestroy()}static \u0275fac=function(i){return new(i||t)(fe(ve,8))};static \u0275cmp=$({type:t,selectors:[["p-tree"]],contentQueries:function(i,o,n){if(i&1&&(k(n,qe,4),k(n,Lt,4),k(n,Ut,4),k(n,At,4),k(n,Rt,4),k(n,Bt,4),k(n,Pt,4),k(n,Qt,4),k(n,Ht,4),k(n,qt,4),k(n,te,4)),i&2){let r;m(r=x())&&(o.filterTemplate=r.first),m(r=x())&&(o.nodeTemplate=r.first),m(r=x())&&(o.headerTemplate=r.first),m(r=x())&&(o.footerTemplate=r.first),m(r=x())&&(o.loaderTemplate=r.first),m(r=x())&&(o.emptyMessageTemplate=r.first),m(r=x())&&(o.togglerIconTemplate=r.first),m(r=x())&&(o.checkboxIconTemplate=r.first),m(r=x())&&(o.loadingIconTemplate=r.first),m(r=x())&&(o.filterIconTemplate=r.first),m(r=x())&&(o.templates=r)}},viewQuery:function(i,o){if(i&1&&(U(qe,5),U(Kt,5),U(jt,5)),i&2){let n;m(n=x())&&(o.filterViewChild=n.first),m(n=x())&&(o.scroller=n.first),m(n=x())&&(o.wrapperViewChild=n.first)}},inputs:{value:"value",selectionMode:"selectionMode",loadingMode:"loadingMode",selection:"selection",style:"style",styleClass:"styleClass",contextMenu:"contextMenu",draggableScope:"draggableScope",droppableScope:"droppableScope",draggableNodes:[2,"draggableNodes","draggableNodes",_],droppableNodes:[2,"droppableNodes","droppableNodes",_],metaKeySelection:[2,"metaKeySelection","metaKeySelection",_],propagateSelectionUp:[2,"propagateSelectionUp","propagateSelectionUp",_],propagateSelectionDown:[2,"propagateSelectionDown","propagateSelectionDown",_],loading:[2,"loading","loading",_],loadingIcon:"loadingIcon",emptyMessage:"emptyMessage",ariaLabel:"ariaLabel",togglerAriaLabel:"togglerAriaLabel",ariaLabelledBy:"ariaLabelledBy",validateDrop:[2,"validateDrop","validateDrop",_],filter:[2,"filter","filter",_],filterInputAutoFocus:[2,"filterInputAutoFocus","filterInputAutoFocus",_],filterBy:"filterBy",filterMode:"filterMode",filterOptions:"filterOptions",filterPlaceholder:"filterPlaceholder",filteredNodes:"filteredNodes",filterLocale:"filterLocale",scrollHeight:"scrollHeight",lazy:[2,"lazy","lazy",_],virtualScroll:[2,"virtualScroll","virtualScroll",_],virtualScrollItemSize:[2,"virtualScrollItemSize","virtualScrollItemSize",E],virtualScrollOptions:"virtualScrollOptions",indentation:[2,"indentation","indentation",E],_templateMap:"_templateMap",trackBy:"trackBy",highlightOnSelect:[2,"highlightOnSelect","highlightOnSelect",_],virtualNodeHeight:"virtualNodeHeight"},outputs:{selectionChange:"selectionChange",onNodeSelect:"onNodeSelect",onNodeUnselect:"onNodeUnselect",onNodeExpand:"onNodeExpand",onNodeCollapse:"onNodeCollapse",onNodeContextMenuSelect:"onNodeContextMenuSelect",onNodeDoubleClick:"onNodeDoubleClick",onNodeDrop:"onNodeDrop",onLazyLoad:"onLazyLoad",onScroll:"onScroll",onScrollIndexChange:"onScrollIndexChange",onFilter:"onFilter"},features:[J([Ke]),O,X],decls:8,vars:10,consts:[["filter",""],["scroller",""],["content",""],["treeNode",""],["loader",""],["wrapper",""],["emptyFilter",""],[3,"drop","dragover","dragenter","dragleave","ngClass","ngStyle"],["class","p-tree-mask p-overlay-mask",4,"ngIf"],[4,"ngTemplateOutlet"],[4,"ngIf"],["class","p-tree-empty-message",4,"ngIf"],[1,"p-tree-mask","p-overlay-mask"],[3,"class",4,"ngIf"],[3,"spin","styleClass",4,"ngIf"],["class","p-tree-loading-icon",4,"ngIf"],[3,"spin","styleClass"],[1,"p-tree-loading-icon"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],["pInputText","","type","search","autocomplete","off",1,"p-tree-filter-input",3,"keydown.enter","input","pAutoFocus"],["class","p-tree-filter-icon",4,"ngIf"],[1,"p-tree-filter-icon"],["styleClass","p-tree-root",3,"items","tabindex","style","scrollHeight","itemSize","lazy","options","onScroll","onScrollIndexChange","onLazyLoad",4,"ngIf"],["styleClass","p-tree-root",3,"onScroll","onScrollIndexChange","onLazyLoad","items","tabindex","scrollHeight","itemSize","lazy","options"],["class","p-tree-root-children","role","tree",3,"ngClass","style",4,"ngIf"],["role","tree",1,"p-tree-root-children",3,"ngClass"],[3,"level","rowNode","node","parentNode","firstChild","lastChild","index","itemSize","indentation","loadingMode",4,"ngFor","ngForOf","ngForTrackBy"],[3,"level","rowNode","node","parentNode","firstChild","lastChild","index","itemSize","indentation","loadingMode"],[1,"p-tree-root"],["class","p-tree-root-children","role","tree",4,"ngIf"],["role","tree",1,"p-tree-root-children"],[3,"node","firstChild","lastChild","index","level","loadingMode",4,"ngFor","ngForOf","ngForTrackBy"],[3,"node","firstChild","lastChild","index","level","loadingMode"],[1,"p-tree-empty-message"],[4,"ngIf","ngIfElse"]],template:function(i,o){if(i&1&&(u(0,"div",7),I("drop",function(r){return o.onDrop(r)})("dragover",function(r){return o.onDragOver(r)})("dragenter",function(){return o.onDragEnter()})("dragleave",function(r){return o.onDragLeave(r)}),s(1,ti,3,2,"div",8)(2,ii,1,0,"ng-container",9)(3,ni,1,4,"ng-container")(4,si,1,1,"p-iconField")(5,yi,3,2,"ng-container",10)(6,vi,3,3,"div",11)(7,Ii,1,0,"ng-container",9),f()),i&2){let n;w(o.styleClass),a("ngClass",o.containerClass)("ngStyle",o.style),d(),a("ngIf",o.loading&&o.loadingMode==="mask"),d(),a("ngTemplateOutlet",o.headerTemplate||o._headerTemplate),d(),ne(o.filterTemplate||o._filterTemplate?3:4),d(2),a("ngIf",(n=o.getRootNode())==null?null:n.length),d(),a("ngIf",!o.loading&&(o.getRootNode()==null||o.getRootNode().length===0)),d(),a("ngTemplateOutlet",o.footerTemplate||o._footerTemplate)}},dependencies:[q,P,ae,Q,H,ce,Be,V,Me,pe,Ue,he,Ae,Re,Di,Le,ze],encapsulation:2})}return t})(),wo=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=Y({type:t});static \u0275inj=G({imports:[Xe,V,V]})}return t})();export{oe as a,Xi as b,Xe as c,wo as d};
