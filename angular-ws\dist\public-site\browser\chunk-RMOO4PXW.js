import{b as n}from"./chunk-T2K2OPF3.js";import{R as a,W as l}from"./chunk-BL4EGCPV.js";import{a as i}from"./chunk-4CLCTAJ7.js";var c=class o{constructor(t){this.restService=t;this.apiName="Default";this.get=(t,e)=>this.restService.request({method:"GET",url:`/api/app/read-only-collection/${t}`},i({apiName:this.apiName},e));this.getCollectionArticleTitles=(t,e)=>this.restService.request({method:"GET",url:`/api/app/read-only-collection/collection-article-titles/${t}`},i({apiName:this.apiName},e));this.getCollectionSummary=(t,e,r)=>this.restService.request({method:"GET",url:`/api/app/read-only-collection/collection-summary/${t}`,params:{skip:e.skip,maxResultCount:e.maxResultCount,sorting:e.sorting,year:e.year,month:e.month}},i({apiName:this.apiName},r));this.getCollectionTree=(t,e)=>this.restService.request({method:"GET",url:`/api/app/read-only-collection/collection-tree/${t}`},i({apiName:this.apiName},e));this.getCollectionTreeAndArticleTitles=(t,e)=>this.restService.request({method:"GET",url:`/api/app/read-only-collection/collection-tree-and-article-titles/${t}`},i({apiName:this.apiName},e));this.getFirstByChannelId=(t,e)=>this.restService.request({method:"GET",url:`/api/app/read-only-collection/first-by-channel-id/${t}`},i({apiName:this.apiName},e));this.getLanguageMatchingCollection=(t,e,r)=>this.restService.request({method:"GET",url:"/api/app/read-only-collection/language-matching-collection",params:{contentCode:t,languageCode:e}},i({apiName:this.apiName},r))}static{this.\u0275fac=function(e){return new(e||o)(l(n))}}static{this.\u0275prov=a({token:o,factory:o.\u0275fac,providedIn:"root"})}};export{c as a};
