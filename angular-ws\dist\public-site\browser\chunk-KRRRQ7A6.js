import{a as M,c as f}from"./chunk-5G3J65ZF.js";import{d as R,e as q}from"./chunk-7X7MFIN2.js";import{A as It,D as k,E as kt,S as Mt,W as Rt,Z as Vt,_ as Q,a as I,b as rt,ca as K,d as xt,ea as W,f as Et,fa as L,g as _t,h as St,k as u,m as lt,o as Dt,q as at,r as pt,y as v}from"./chunk-LS3LVTXN.js";import{c as Lt,d as et,f as it,h as ot,i as nt,j as st}from"./chunk-BMA7WWEI.js";import{j as bt,l as Ot,m as wt,n as Tt,q as Ct,t as N}from"./chunk-D6WDCTDG.js";import{$b as P,Bb as vt,Cb as Y,Hb as J,Ib as y,Jb as ft,Kb as mt,Nb as X,Ob as tt,Pb as _,Qb as S,R as H,Ra as U,S as A,Ta as ht,Wa as O,X as z,Ya as ct,Zb as j,ab as c,bb as F,bc as yt,ca as dt,cb as ut,da as x,ea as E,eb as a,fa as b,ga as l,gb as Z,gc as gt,ma as h,mb as $,na as B,nb as w,rb as m,uc as D,vc as C,wb as p,xb as d,yb as T}from"./chunk-BL4EGCPV.js";import{a as r}from"./chunk-4CLCTAJ7.js";var le=(()=>{class e extends L{static \u0275fac=(()=>{let t;return function(o){return(t||(t=l(e)))(o||e)}})();static \u0275cmp=c({type:e,selectors:[["AngleRightIcon"]],features:[a],decls:2,vars:5,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M5.25 11.1728C5.14929 11.1694 5.05033 11.1455 4.9592 11.1025C4.86806 11.0595 4.78666 10.9984 4.72 10.9228C4.57955 10.7822 4.50066 10.5916 4.50066 10.3928C4.50066 10.1941 4.57955 10.0035 4.72 9.86283L7.72 6.86283L4.72 3.86283C4.66067 3.71882 4.64765 3.55991 4.68275 3.40816C4.71785 3.25642 4.79932 3.11936 4.91585 3.01602C5.03238 2.91268 5.17819 2.84819 5.33305 2.83149C5.4879 2.81479 5.64411 2.84671 5.78 2.92283L9.28 6.42283C9.42045 6.56346 9.49934 6.75408 9.49934 6.95283C9.49934 7.15158 9.42045 7.34221 9.28 7.48283L5.78 10.9228C5.71333 10.9984 5.63193 11.0595 5.5408 11.1025C5.44966 11.1455 5.35071 11.1694 5.25 11.1728Z","fill","currentColor"]],template:function(i,o){i&1&&(b(),p(0,"svg",0),T(1,"path",1),d()),i&2&&(m(o.getClassNames()),$("aria-label",o.ariaLabel)("aria-hidden",o.ariaHidden)("role",o.role))},encapsulation:2})}return e})();var de=(()=>{class e extends L{static \u0275fac=(()=>{let t;return function(o){return(t||(t=l(e)))(o||e)}})();static \u0275cmp=c({type:e,selectors:[["AngleDownIcon"]],features:[a],decls:2,vars:5,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M3.58659 4.5007C3.68513 4.50023 3.78277 4.51945 3.87379 4.55723C3.9648 4.59501 4.04735 4.65058 4.11659 4.7207L7.11659 7.7207L10.1166 4.7207C10.2619 4.65055 10.4259 4.62911 10.5843 4.65956C10.7427 4.69002 10.8871 4.77074 10.996 4.88976C11.1049 5.00877 11.1726 5.15973 11.1889 5.32022C11.2052 5.48072 11.1693 5.6422 11.0866 5.7807L7.58659 9.2807C7.44597 9.42115 7.25534 9.50004 7.05659 9.50004C6.85784 9.50004 6.66722 9.42115 6.52659 9.2807L3.02659 5.7807C2.88614 5.64007 2.80725 5.44945 2.80725 5.2507C2.80725 5.05195 2.88614 4.86132 3.02659 4.7207C3.09932 4.64685 3.18675 4.58911 3.28322 4.55121C3.37969 4.51331 3.48305 4.4961 3.58659 4.5007Z","fill","currentColor"]],template:function(i,o){i&1&&(b(),p(0,"svg",0),T(1,"path",1),d()),i&2&&(m(o.getClassNames()),$("aria-label",o.ariaLabel)("aria-hidden",o.ariaHidden)("role",o.role))},encapsulation:2})}return e})();var ue=(()=>{class e extends L{static \u0275fac=(()=>{let t;return function(o){return(t||(t=l(e)))(o||e)}})();static \u0275cmp=c({type:e,selectors:[["BlankIcon"]],features:[a],decls:2,vars:0,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["width","1","height","1","fill","currentColor","fill-opacity","0"]],template:function(i,o){i&1&&(b(),p(0,"svg",0),T(1,"rect",1),d())},encapsulation:2})}return e})();var Ht=["content"],Nt=["overlay"],Qt=["*"],Kt=(e,g,t,i,o,n,s,V,Bt,Ft,Zt,$t,jt,Pt)=>({"p-overlay p-component":!0,"p-overlay-modal p-overlay-mask p-overlay-mask-enter":e,"p-overlay-center":g,"p-overlay-top":t,"p-overlay-top-start":i,"p-overlay-top-end":o,"p-overlay-bottom":n,"p-overlay-bottom-start":s,"p-overlay-bottom-end":V,"p-overlay-left":Bt,"p-overlay-left-start":Ft,"p-overlay-left-end":Zt,"p-overlay-right":$t,"p-overlay-right-start":jt,"p-overlay-right-end":Pt}),Wt=(e,g,t)=>({showTransitionParams:e,hideTransitionParams:g,transform:t}),qt=e=>({value:"visible",params:e}),Gt=e=>({mode:e}),Ut=e=>({$implicit:e});function Yt(e,g){e&1&&vt(0)}function Jt(e,g){if(e&1){let t=Y();p(0,"div",3,1),J("click",function(o){x(t);let n=y(2);return E(n.onOverlayContentClick(o))})("@overlayContentAnimation.start",function(o){x(t);let n=y(2);return E(n.onOverlayContentAnimationStart(o))})("@overlayContentAnimation.done",function(o){x(t);let n=y(2);return E(n.onOverlayContentAnimationDone(o))}),mt(2),Z(3,Yt,1,0,"ng-container",4),d()}if(e&2){let t=y(2);m(t.contentStyleClass),w("ngStyle",t.contentStyle)("ngClass","p-overlay-content")("@overlayContentAnimation",P(11,qt,yt(7,Wt,t.showTransitionOptions,t.hideTransitionOptions,t.transformOptions[t.modal?t.overlayResponsiveDirection:"default"]))),U(3),w("ngTemplateOutlet",t.contentTemplate||t._contentTemplate)("ngTemplateOutletContext",P(15,Ut,P(13,Gt,t.overlayMode)))}}function Xt(e,g){if(e&1){let t=Y();p(0,"div",3,0),J("click",function(){x(t);let o=y();return E(o.onOverlayClick())}),Z(2,Jt,4,17,"div",2),d()}if(e&2){let t=y();m(t.styleClass),w("ngStyle",t.style)("ngClass",gt(5,Kt,[t.modal,t.modal&&t.overlayResponsiveDirection==="center",t.modal&&t.overlayResponsiveDirection==="top",t.modal&&t.overlayResponsiveDirection==="top-start",t.modal&&t.overlayResponsiveDirection==="top-end",t.modal&&t.overlayResponsiveDirection==="bottom",t.modal&&t.overlayResponsiveDirection==="bottom-start",t.modal&&t.overlayResponsiveDirection==="bottom-end",t.modal&&t.overlayResponsiveDirection==="left",t.modal&&t.overlayResponsiveDirection==="left-start",t.modal&&t.overlayResponsiveDirection==="left-end",t.modal&&t.overlayResponsiveDirection==="right",t.modal&&t.overlayResponsiveDirection==="right-start",t.modal&&t.overlayResponsiveDirection==="right-end"])),U(2),w("ngIf",t.visible)}}var te=({dt:e})=>`
.p-overlay {
    position: absolute;
    top: 0;
}

.p-overlay-modal {
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.p-overlay-content {
    transform-origin: inherit;
}

.p-overlay-modal > .p-overlay-content {
    z-index: 1;
    width: 90%;
}

/* Position */
/* top */
.p-overlay-top {
    align-items: flex-start;
}
.p-overlay-top-start {
    align-items: flex-start;
    justify-content: flex-start;
}
.p-overlay-top-end {
    align-items: flex-start;
    justify-content: flex-end;
}

/* bottom */
.p-overlay-bottom {
    align-items: flex-end;
}
.p-overlay-bottom-start {
    align-items: flex-end;
    justify-content: flex-start;
}
.p-overlay-bottom-end {
    align-items: flex-end;
    justify-content: flex-end;
}

/* left */
.p-overlay-left {
    justify-content: flex-start;
}
.p-overlay-left-start {
    justify-content: flex-start;
    align-items: flex-start;
}
.p-overlay-left-end {
    justify-content: flex-start;
    align-items: flex-end;
}

/* right */
.p-overlay-right {
    justify-content: flex-end;
}
.p-overlay-right-start {
    justify-content: flex-end;
    align-items: flex-start;
}
.p-overlay-right-end {
    justify-content: flex-end;
    align-items: flex-end;
}
`,At=(()=>{class e extends K{name="overlay";theme=te;static \u0275fac=(()=>{let t;return function(o){return(t||(t=l(e)))(o||e)}})();static \u0275prov=H({token:e,factory:e.\u0275fac})}return e})(),ee=nt([it({transform:"{{transform}}",opacity:0}),et("{{showTransitionParams}}")]),ie=nt([et("{{hideTransitionParams}}",it({transform:"{{transform}}",opacity:0}))]),oe=(()=>{class e extends W{overlayService;zone;get visible(){return this._visible}set visible(t){this._visible=t,this._visible&&!this.modalVisible&&(this.modalVisible=!0)}get mode(){return this._mode||this.overlayOptions?.mode}set mode(t){this._mode=t}get style(){return M.merge(this._style,this.modal?this.overlayResponsiveOptions?.style:this.overlayOptions?.style)}set style(t){this._style=t}get styleClass(){return M.merge(this._styleClass,this.modal?this.overlayResponsiveOptions?.styleClass:this.overlayOptions?.styleClass)}set styleClass(t){this._styleClass=t}get contentStyle(){return M.merge(this._contentStyle,this.modal?this.overlayResponsiveOptions?.contentStyle:this.overlayOptions?.contentStyle)}set contentStyle(t){this._contentStyle=t}get contentStyleClass(){return M.merge(this._contentStyleClass,this.modal?this.overlayResponsiveOptions?.contentStyleClass:this.overlayOptions?.contentStyleClass)}set contentStyleClass(t){this._contentStyleClass=t}get target(){let t=this._target||this.overlayOptions?.target;return t===void 0?"@prev":t}set target(t){this._target=t}get appendTo(){return this._appendTo||this.overlayOptions?.appendTo}set appendTo(t){this._appendTo=t}get autoZIndex(){let t=this._autoZIndex||this.overlayOptions?.autoZIndex;return t===void 0?!0:t}set autoZIndex(t){this._autoZIndex=t}get baseZIndex(){let t=this._baseZIndex||this.overlayOptions?.baseZIndex;return t===void 0?0:t}set baseZIndex(t){this._baseZIndex=t}get showTransitionOptions(){let t=this._showTransitionOptions||this.overlayOptions?.showTransitionOptions;return t===void 0?".12s cubic-bezier(0, 0, 0.2, 1)":t}set showTransitionOptions(t){this._showTransitionOptions=t}get hideTransitionOptions(){let t=this._hideTransitionOptions||this.overlayOptions?.hideTransitionOptions;return t===void 0?".1s linear":t}set hideTransitionOptions(t){this._hideTransitionOptions=t}get listener(){return this._listener||this.overlayOptions?.listener}set listener(t){this._listener=t}get responsive(){return this._responsive||this.overlayOptions?.responsive}set responsive(t){this._responsive=t}get options(){return this._options}set options(t){this._options=t}visibleChange=new h;onBeforeShow=new h;onShow=new h;onBeforeHide=new h;onHide=new h;onAnimationStart=new h;onAnimationDone=new h;overlayViewChild;contentViewChild;contentTemplate;templates;_contentTemplate;_visible=!1;_mode;_style;_styleClass;_contentStyle;_contentStyleClass;_target;_appendTo;_autoZIndex;_baseZIndex;_showTransitionOptions;_hideTransitionOptions;_listener;_responsive;_options;modalVisible=!1;isOverlayClicked=!1;isOverlayContentClicked=!1;scrollHandler;documentClickListener;documentResizeListener;_componentStyle=z(At);documentKeyboardListener;window;transformOptions={default:"scaleY(0.8)",center:"scale(0.7)",top:"translate3d(0px, -100%, 0px)","top-start":"translate3d(0px, -100%, 0px)","top-end":"translate3d(0px, -100%, 0px)",bottom:"translate3d(0px, 100%, 0px)","bottom-start":"translate3d(0px, 100%, 0px)","bottom-end":"translate3d(0px, 100%, 0px)",left:"translate3d(-100%, 0px, 0px)","left-start":"translate3d(-100%, 0px, 0px)","left-end":"translate3d(-100%, 0px, 0px)",right:"translate3d(100%, 0px, 0px)","right-start":"translate3d(100%, 0px, 0px)","right-end":"translate3d(100%, 0px, 0px)"};get modal(){if(N(this.platformId))return this.mode==="modal"||this.overlayResponsiveOptions&&this.document.defaultView?.matchMedia(this.overlayResponsiveOptions.media?.replace("@media","")||`(max-width: ${this.overlayResponsiveOptions.breakpoint})`).matches}get overlayMode(){return this.mode||(this.modal?"modal":"overlay")}get overlayOptions(){return r(r({},this.config?.overlayOptions),this.options)}get overlayResponsiveOptions(){return r(r({},this.overlayOptions?.responsive),this.responsive)}get overlayResponsiveDirection(){return this.overlayResponsiveOptions?.direction||"center"}get overlayEl(){return this.overlayViewChild?.nativeElement}get contentEl(){return this.contentViewChild?.nativeElement}get targetEl(){return It(this.target,this.el?.nativeElement)}constructor(t,i){super(),this.overlayService=t,this.zone=i}ngAfterContentInit(){this.templates?.forEach(t=>{switch(t.getType()){case"content":this._contentTemplate=t.template;break;default:this._contentTemplate=t.template;break}})}show(t,i=!1){this.onVisibleChange(!0),this.handleEvents("onShow",{overlay:t||this.overlayEl,target:this.targetEl,mode:this.overlayMode}),i&&pt(this.targetEl),this.modal&&rt(this.document?.body,"p-overflow-hidden")}hide(t,i=!1){if(this.visible)this.onVisibleChange(!1),this.handleEvents("onHide",{overlay:t||this.overlayEl,target:this.targetEl,mode:this.overlayMode}),i&&pt(this.targetEl),this.modal&&xt(this.document?.body,"p-overflow-hidden");else return}alignOverlay(){!this.modal&&R.alignOverlay(this.overlayEl,this.targetEl,this.appendTo)}onVisibleChange(t){this._visible=t,this.visibleChange.emit(t)}onOverlayClick(){this.isOverlayClicked=!0}onOverlayContentClick(t){this.overlayService.add({originalEvent:t,target:this.targetEl}),this.isOverlayContentClicked=!0}onOverlayContentAnimationStart(t){switch(t.toState){case"visible":this.handleEvents("onBeforeShow",{overlay:this.overlayEl,target:this.targetEl,mode:this.overlayMode}),this.autoZIndex&&f.set(this.overlayMode,this.overlayEl,this.baseZIndex+this.config?.zIndex[this.overlayMode]),R.appendOverlay(this.overlayEl,this.appendTo==="body"?this.document.body:this.appendTo,this.appendTo),this.alignOverlay();break;case"void":this.handleEvents("onBeforeHide",{overlay:this.overlayEl,target:this.targetEl,mode:this.overlayMode}),this.modal&&rt(this.overlayEl,"p-overlay-mask-leave");break}this.handleEvents("onAnimationStart",t)}onOverlayContentAnimationDone(t){let i=this.overlayEl||t.element.parentElement;switch(t.toState){case"visible":this.visible&&(this.show(i,!0),this.bindListeners());break;case"void":if(!this.visible){this.hide(i,!0),this.modalVisible=!1,this.unbindListeners(),R.appendOverlay(this.overlayEl,this.targetEl,this.appendTo),f.clear(i),this.cd.markForCheck();break}}this.handleEvents("onAnimationDone",t)}handleEvents(t,i){this[t].emit(i),this.options&&this.options[t]&&this.options[t](i),this.config?.overlayOptions&&(this.config?.overlayOptions)[t]&&(this.config?.overlayOptions)[t](i)}bindListeners(){this.bindScrollListener(),this.bindDocumentClickListener(),this.bindDocumentResizeListener(),this.bindDocumentKeyboardListener()}unbindListeners(){this.unbindScrollListener(),this.unbindDocumentClickListener(),this.unbindDocumentResizeListener(),this.unbindDocumentKeyboardListener()}bindScrollListener(){this.scrollHandler||(this.scrollHandler=new q(this.targetEl,t=>{(this.listener?this.listener(t,{type:"scroll",mode:this.overlayMode,valid:!0}):!0)&&this.hide(t,!0)})),this.scrollHandler.bindScrollListener()}unbindScrollListener(){this.scrollHandler&&this.scrollHandler.unbindScrollListener()}bindDocumentClickListener(){this.documentClickListener||(this.documentClickListener=this.renderer.listen(this.document,"click",t=>{let o=!(this.targetEl&&(this.targetEl.isSameNode(t.target)||!this.isOverlayClicked&&this.targetEl.contains(t.target)))&&!this.isOverlayContentClicked;(this.listener?this.listener(t,{type:"outside",mode:this.overlayMode,valid:t.which!==3&&o}):o)&&this.hide(t),this.isOverlayClicked=this.isOverlayContentClicked=!1}))}unbindDocumentClickListener(){this.documentClickListener&&(this.documentClickListener(),this.documentClickListener=null)}bindDocumentResizeListener(){this.documentResizeListener||(this.documentResizeListener=this.renderer.listen(this.document.defaultView,"resize",t=>{(this.listener?this.listener(t,{type:"resize",mode:this.overlayMode,valid:!k()}):!k())&&this.hide(t,!0)}))}unbindDocumentResizeListener(){this.documentResizeListener&&(this.documentResizeListener(),this.documentResizeListener=null)}bindDocumentKeyboardListener(){this.documentKeyboardListener||this.zone.runOutsideAngular(()=>{this.documentKeyboardListener=this.renderer.listen(this.document.defaultView,"keydown",t=>{if(this.overlayOptions.hideOnEscape===!1||t.code!=="Escape")return;(this.listener?this.listener(t,{type:"keydown",mode:this.overlayMode,valid:!k()}):!k())&&this.zone.run(()=>{this.hide(t,!0)})})})}unbindDocumentKeyboardListener(){this.documentKeyboardListener&&(this.documentKeyboardListener(),this.documentKeyboardListener=null)}ngOnDestroy(){this.hide(this.overlayEl,!0),this.overlayEl&&(R.appendOverlay(this.overlayEl,this.targetEl,this.appendTo),f.clear(this.overlayEl)),this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.unbindListeners(),super.ngOnDestroy()}static \u0275fac=function(i){return new(i||e)(O(Rt),O(B))};static \u0275cmp=c({type:e,selectors:[["p-overlay"]],contentQueries:function(i,o,n){if(i&1&&(X(n,Ht,4),X(n,Vt,4)),i&2){let s;_(s=S())&&(o.contentTemplate=s.first),_(s=S())&&(o.templates=s)}},viewQuery:function(i,o){if(i&1&&(tt(Nt,5),tt(Ht,5)),i&2){let n;_(n=S())&&(o.overlayViewChild=n.first),_(n=S())&&(o.contentViewChild=n.first)}},inputs:{visible:"visible",mode:"mode",style:"style",styleClass:"styleClass",contentStyle:"contentStyle",contentStyleClass:"contentStyleClass",target:"target",appendTo:"appendTo",autoZIndex:"autoZIndex",baseZIndex:"baseZIndex",showTransitionOptions:"showTransitionOptions",hideTransitionOptions:"hideTransitionOptions",listener:"listener",responsive:"responsive",options:"options"},outputs:{visibleChange:"visibleChange",onBeforeShow:"onBeforeShow",onShow:"onShow",onBeforeHide:"onBeforeHide",onHide:"onHide",onAnimationStart:"onAnimationStart",onAnimationDone:"onAnimationDone"},features:[j([At]),a],ngContentSelectors:Qt,decls:1,vars:1,consts:[["overlay",""],["content",""],[3,"ngStyle","class","ngClass","click",4,"ngIf"],[3,"click","ngStyle","ngClass"],[4,"ngTemplateOutlet","ngTemplateOutletContext"]],template:function(i,o){i&1&&(ft(),Z(0,Xt,3,20,"div",2)),i&2&&w("ngIf",o.modalVisible)},dependencies:[Ct,bt,Ot,Tt,wt,Q],encapsulation:2,data:{animation:[Lt("overlayContentAnimation",[ot(":enter",[st(ee)]),ot(":leave",[st(ie)])])]},changeDetection:0})}return e})(),Ae=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=F({type:e});static \u0275inj=A({imports:[oe,Q,Q]})}return e})();var ne=({dt:e})=>`
.p-tooltip {
    position: absolute;
    display: none;
    max-width: ${e("tooltip.max.width")};
}

.p-tooltip-right,
.p-tooltip-left {
    padding: 0 ${e("tooltip.gutter")};
}

.p-tooltip-top,
.p-tooltip-bottom {
    padding: ${e("tooltip.gutter")} 0;
}

.p-tooltip-text {
    white-space: pre-line;
    word-break: break-word;
    background: ${e("tooltip.background")};
    color: ${e("tooltip.color")};
    padding: ${e("tooltip.padding")};
    box-shadow: ${e("tooltip.shadow")};
    border-radius: ${e("tooltip.border.radius")};
}

.p-tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    scale: 2;
}

.p-tooltip-right .p-tooltip-arrow {
    top: 50%;
    left: 0;
    margin-top: calc(-1 * ${e("tooltip.gutter")});
    border-width: ${e("tooltip.gutter")} ${e("tooltip.gutter")} ${e("tooltip.gutter")} 0;
    border-right-color: ${e("tooltip.background")};
}

.p-tooltip-left .p-tooltip-arrow {
    top: 50%;
    right: 0;
    margin-top: calc(-1 * ${e("tooltip.gutter")});
    border-width: ${e("tooltip.gutter")} 0 ${e("tooltip.gutter")} ${e("tooltip.gutter")};
    border-left-color: ${e("tooltip.background")};
}

.p-tooltip-top .p-tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: calc(-1 * ${e("tooltip.gutter")});
    border-width: ${e("tooltip.gutter")} ${e("tooltip.gutter")} 0 ${e("tooltip.gutter")};
    border-top-color: ${e("tooltip.background")};
    border-bottom-color: ${e("tooltip.background")};
}

.p-tooltip-bottom .p-tooltip-arrow {
    top: 0;
    left: 50%;
    margin-left: calc(-1 * ${e("tooltip.gutter")});
    border-width: 0 ${e("tooltip.gutter")} ${e("tooltip.gutter")} ${e("tooltip.gutter")};
    border-top-color: ${e("tooltip.background")};
    border-bottom-color: ${e("tooltip.background")};
}
`,se={root:"p-tooltip p-component",arrow:"p-tooltip-arrow",text:"p-tooltip-text"},zt=(()=>{class e extends K{name="tooltip";theme=ne;classes=se;static \u0275fac=(()=>{let t;return function(o){return(t||(t=l(e)))(o||e)}})();static \u0275prov=H({token:e,factory:e.\u0275fac})}return e})();var Ue=(()=>{class e extends W{zone;viewContainer;tooltipPosition;tooltipEvent="hover";appendTo;positionStyle;tooltipStyleClass;tooltipZIndex;escape=!0;showDelay;hideDelay;life;positionTop;positionLeft;autoHide=!0;fitContent=!0;hideOnEscape=!0;content;get disabled(){return this._disabled}set disabled(t){this._disabled=t,this.deactivate()}tooltipOptions;_tooltipOptions={tooltipLabel:null,tooltipPosition:"right",tooltipEvent:"hover",appendTo:"body",positionStyle:null,tooltipStyleClass:null,tooltipZIndex:"auto",escape:!0,disabled:null,showDelay:null,hideDelay:null,positionTop:null,positionLeft:null,life:null,autoHide:!0,hideOnEscape:!0,id:Mt("pn_id_")+"_tooltip"};_disabled;container;styleClass;tooltipText;showTimeout;hideTimeout;active;mouseEnterListener;mouseLeaveListener;containerMouseleaveListener;clickListener;focusListener;blurListener;documentEscapeListener;scrollHandler;resizeListener;_componentStyle=z(zt);interactionInProgress=!1;constructor(t,i){super(),this.zone=t,this.viewContainer=i}ngAfterViewInit(){super.ngAfterViewInit(),N(this.platformId)&&this.zone.runOutsideAngular(()=>{let t=this.getOption("tooltipEvent");if((t==="hover"||t==="both")&&(this.mouseEnterListener=this.onMouseEnter.bind(this),this.mouseLeaveListener=this.onMouseLeave.bind(this),this.clickListener=this.onInputClick.bind(this),this.el.nativeElement.addEventListener("mouseenter",this.mouseEnterListener),this.el.nativeElement.addEventListener("click",this.clickListener),this.el.nativeElement.addEventListener("mouseleave",this.mouseLeaveListener)),t==="focus"||t==="both"){this.focusListener=this.onFocus.bind(this),this.blurListener=this.onBlur.bind(this);let i=this.el.nativeElement.querySelector(".p-component");i||(i=this.getTarget(this.el.nativeElement)),i.addEventListener("focus",this.focusListener),i.addEventListener("blur",this.blurListener)}})}ngOnChanges(t){super.ngOnChanges(t),t.tooltipPosition&&this.setOption({tooltipPosition:t.tooltipPosition.currentValue}),t.tooltipEvent&&this.setOption({tooltipEvent:t.tooltipEvent.currentValue}),t.appendTo&&this.setOption({appendTo:t.appendTo.currentValue}),t.positionStyle&&this.setOption({positionStyle:t.positionStyle.currentValue}),t.tooltipStyleClass&&this.setOption({tooltipStyleClass:t.tooltipStyleClass.currentValue}),t.tooltipZIndex&&this.setOption({tooltipZIndex:t.tooltipZIndex.currentValue}),t.escape&&this.setOption({escape:t.escape.currentValue}),t.showDelay&&this.setOption({showDelay:t.showDelay.currentValue}),t.hideDelay&&this.setOption({hideDelay:t.hideDelay.currentValue}),t.life&&this.setOption({life:t.life.currentValue}),t.positionTop&&this.setOption({positionTop:t.positionTop.currentValue}),t.positionLeft&&this.setOption({positionLeft:t.positionLeft.currentValue}),t.disabled&&this.setOption({disabled:t.disabled.currentValue}),t.content&&(this.setOption({tooltipLabel:t.content.currentValue}),this.active&&(t.content.currentValue?this.container&&this.container.offsetParent?(this.updateText(),this.align()):this.show():this.hide())),t.autoHide&&this.setOption({autoHide:t.autoHide.currentValue}),t.id&&this.setOption({id:t.id.currentValue}),t.tooltipOptions&&(this._tooltipOptions=r(r({},this._tooltipOptions),t.tooltipOptions.currentValue),this.deactivate(),this.active&&(this.getOption("tooltipLabel")?this.container&&this.container.offsetParent?(this.updateText(),this.align()):this.show():this.hide()))}isAutoHide(){return this.getOption("autoHide")}onMouseEnter(t){!this.container&&!this.showTimeout&&this.activate()}onMouseLeave(t){this.isAutoHide()?this.deactivate():!(I(t.relatedTarget,"p-tooltip")||I(t.relatedTarget,"p-tooltip-text")||I(t.relatedTarget,"p-tooltip-arrow"))&&this.deactivate()}onFocus(t){this.activate()}onBlur(t){this.deactivate()}onInputClick(t){this.deactivate()}activate(){if(!this.interactionInProgress){if(this.active=!0,this.clearHideTimeout(),this.getOption("showDelay")?this.showTimeout=setTimeout(()=>{this.show()},this.getOption("showDelay")):this.show(),this.getOption("life")){let t=this.getOption("showDelay")?this.getOption("life")+this.getOption("showDelay"):this.getOption("life");this.hideTimeout=setTimeout(()=>{this.hide()},t)}this.getOption("hideOnEscape")&&(this.documentEscapeListener=this.renderer.listen("document","keydown.escape",()=>{this.deactivate(),this.documentEscapeListener()})),this.interactionInProgress=!0}}deactivate(){this.interactionInProgress=!1,this.active=!1,this.clearShowTimeout(),this.getOption("hideDelay")?(this.clearHideTimeout(),this.hideTimeout=setTimeout(()=>{this.hide()},this.getOption("hideDelay"))):this.hide(),this.documentEscapeListener&&this.documentEscapeListener()}create(){this.container&&(this.clearHideTimeout(),this.remove()),this.container=document.createElement("div"),this.container.setAttribute("id",this.getOption("id")),this.container.setAttribute("role","tooltip");let t=document.createElement("div");t.className="p-tooltip-arrow",this.container.appendChild(t),this.tooltipText=document.createElement("div"),this.tooltipText.className="p-tooltip-text",this.updateText(),this.getOption("positionStyle")&&(this.container.style.position=this.getOption("positionStyle")),this.container.appendChild(this.tooltipText),this.getOption("appendTo")==="body"?document.body.appendChild(this.container):this.getOption("appendTo")==="target"?lt(this.container,this.el.nativeElement):lt(this.getOption("appendTo"),this.container),this.container.style.display="none",this.fitContent&&(this.container.style.width="fit-content"),this.isAutoHide()?this.container.style.pointerEvents="none":(this.container.style.pointerEvents="unset",this.bindContainerMouseleaveListener())}bindContainerMouseleaveListener(){if(!this.containerMouseleaveListener){let t=this.container??this.container.nativeElement;this.containerMouseleaveListener=this.renderer.listen(t,"mouseleave",i=>{this.deactivate()})}}unbindContainerMouseleaveListener(){this.containerMouseleaveListener&&(this.bindContainerMouseleaveListener(),this.containerMouseleaveListener=null)}show(){if(!this.getOption("tooltipLabel")||this.getOption("disabled"))return;this.create(),this.el.nativeElement.closest("p-dialog")?setTimeout(()=>{this.container&&(this.container.style.display="inline-block"),this.container&&this.align()},100):(this.container.style.display="inline-block",this.align()),Dt(this.container,250),this.getOption("tooltipZIndex")==="auto"?f.set("tooltip",this.container,this.config.zIndex.tooltip):this.container.style.zIndex=this.getOption("tooltipZIndex"),this.bindDocumentResizeListener(),this.bindScrollListener()}hide(){this.getOption("tooltipZIndex")==="auto"&&f.clear(this.container),this.remove()}updateText(){let t=this.getOption("tooltipLabel");if(t instanceof ht){let i=this.viewContainer.createEmbeddedView(t);i.detectChanges(),i.rootNodes.forEach(o=>this.tooltipText.appendChild(o))}else this.getOption("escape")?(this.tooltipText.innerHTML="",this.tooltipText.appendChild(document.createTextNode(t))):this.tooltipText.innerHTML=t}align(){let t=this.getOption("tooltipPosition"),i={top:[this.alignTop,this.alignBottom,this.alignRight,this.alignLeft],bottom:[this.alignBottom,this.alignTop,this.alignRight,this.alignLeft],left:[this.alignLeft,this.alignRight,this.alignTop,this.alignBottom],right:[this.alignRight,this.alignLeft,this.alignTop,this.alignBottom]};for(let[o,n]of i[t].entries())if(o===0)n.call(this);else if(this.isOutOfBounds())n.call(this);else break}getHostOffset(){if(this.getOption("appendTo")==="body"||this.getOption("appendTo")==="target"){let t=this.el.nativeElement.getBoundingClientRect(),i=t.left+_t(),o=t.top+St();return{left:i,top:o}}else return{left:0,top:0}}get activeElement(){return this.el.nativeElement.nodeName.startsWith("P-")?at(this.el.nativeElement,".p-component"):this.el.nativeElement}alignRight(){this.preAlign("right");let t=this.activeElement,i=u(t),o=(v(t)-v(this.container))/2;this.alignTooltip(i,o)}alignLeft(){this.preAlign("left");let t=u(this.container),i=(v(this.el.nativeElement)-v(this.container))/2;this.alignTooltip(-t,i)}alignTop(){this.preAlign("top");let t=(u(this.el.nativeElement)-u(this.container))/2,i=v(this.container);this.alignTooltip(t,-i)}alignBottom(){this.preAlign("bottom");let t=(u(this.el.nativeElement)-u(this.container))/2,i=v(this.el.nativeElement);this.alignTooltip(t,i)}alignTooltip(t,i){let o=this.getHostOffset(),n=o.left+t,s=o.top+i;this.container.style.left=n+this.getOption("positionLeft")+"px",this.container.style.top=s+this.getOption("positionTop")+"px"}setOption(t){this._tooltipOptions=r(r({},this._tooltipOptions),t)}getOption(t){return this._tooltipOptions[t]}getTarget(t){return I(t,"p-inputwrapper")?at(t,"input"):t}preAlign(t){this.container.style.left="-999px",this.container.style.top="-999px";let i="p-tooltip p-component p-tooltip-"+t;this.container.className=this.getOption("tooltipStyleClass")?i+" "+this.getOption("tooltipStyleClass"):i}isOutOfBounds(){let t=this.container.getBoundingClientRect(),i=t.top,o=t.left,n=u(this.container),s=v(this.container),V=Et();return o+n>V.width||o<0||i<0||i+s>V.height}onWindowResize(t){this.hide()}bindDocumentResizeListener(){this.zone.runOutsideAngular(()=>{this.resizeListener=this.onWindowResize.bind(this),window.addEventListener("resize",this.resizeListener)})}unbindDocumentResizeListener(){this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null)}bindScrollListener(){this.scrollHandler||(this.scrollHandler=new q(this.el.nativeElement,()=>{this.container&&this.hide()})),this.scrollHandler.bindScrollListener()}unbindScrollListener(){this.scrollHandler&&this.scrollHandler.unbindScrollListener()}unbindEvents(){let t=this.getOption("tooltipEvent");if((t==="hover"||t==="both")&&(this.el.nativeElement.removeEventListener("mouseenter",this.mouseEnterListener),this.el.nativeElement.removeEventListener("mouseleave",this.mouseLeaveListener),this.el.nativeElement.removeEventListener("click",this.clickListener)),t==="focus"||t==="both"){let i=this.el.nativeElement.querySelector(".p-component");i||(i=this.getTarget(this.el.nativeElement)),i.removeEventListener("focus",this.focusListener),i.removeEventListener("blur",this.blurListener)}this.unbindDocumentResizeListener()}remove(){this.container&&this.container.parentElement&&(this.getOption("appendTo")==="body"?document.body.removeChild(this.container):this.getOption("appendTo")==="target"?this.el.nativeElement.removeChild(this.container):kt(this.getOption("appendTo"),this.container)),this.unbindDocumentResizeListener(),this.unbindScrollListener(),this.unbindContainerMouseleaveListener(),this.clearTimeouts(),this.container=null,this.scrollHandler=null}clearShowTimeout(){this.showTimeout&&(clearTimeout(this.showTimeout),this.showTimeout=null)}clearHideTimeout(){this.hideTimeout&&(clearTimeout(this.hideTimeout),this.hideTimeout=null)}clearTimeouts(){this.clearShowTimeout(),this.clearHideTimeout()}ngOnDestroy(){this.unbindEvents(),super.ngOnDestroy(),this.container&&f.clear(this.container),this.remove(),this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.documentEscapeListener&&this.documentEscapeListener()}static \u0275fac=function(i){return new(i||e)(O(B),O(ct))};static \u0275dir=ut({type:e,selectors:[["","pTooltip",""]],inputs:{tooltipPosition:"tooltipPosition",tooltipEvent:"tooltipEvent",appendTo:"appendTo",positionStyle:"positionStyle",tooltipStyleClass:"tooltipStyleClass",tooltipZIndex:"tooltipZIndex",escape:[2,"escape","escape",D],showDelay:[2,"showDelay","showDelay",C],hideDelay:[2,"hideDelay","hideDelay",C],life:[2,"life","life",C],positionTop:[2,"positionTop","positionTop",C],positionLeft:[2,"positionLeft","positionLeft",C],autoHide:[2,"autoHide","autoHide",D],fitContent:[2,"fitContent","fitContent",D],hideOnEscape:[2,"hideOnEscape","hideOnEscape",D],content:[0,"pTooltip","content"],disabled:[0,"tooltipDisabled","disabled"],tooltipOptions:"tooltipOptions"},features:[j([zt]),a,dt]})}return e})(),Ye=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=F({type:e});static \u0275inj=A({})}return e})();export{de as a,le as b,ue as c,oe as d,Ae as e,Ue as f,Ye as g};
