import{p as r,x as p}from"./chunk-CNIH62FZ.js";import{q as i}from"./chunk-D6WDCTDG.js";import{ab as n,yb as s}from"./chunk-BL4EGCPV.js";import"./chunk-4CLCTAJ7.js";var a=class t{openBook(o){console.log("Opening book:",o)}navigateToLibrary(){console.log("Navigating to library")}static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275cmp=n({type:t,selectors:[["app-ebooks"]],decls:1,vars:0,template:function(e,l){e&1&&s(0,"router-outlet")},dependencies:[i,p,r],styles:["[_nghost-%COMP%]{flex:1;display:flex;justify-content:center}"]})}};export{a as EbooksComponent};
