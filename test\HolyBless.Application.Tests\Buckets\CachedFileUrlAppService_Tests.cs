using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using Microsoft.Extensions.Caching.Memory;
using Shouldly;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace HolyBless.Buckets
{
    public abstract class CachedFileUrlAppService_Tests<TStartupModule> : HolyBlessApplicationTestBase<TStartupModule>
     where TStartupModule : IAbpModule
    {
        private readonly ICachedFileUrlAppService _cachedFileUrlAppService;
        private readonly IRepository<BucketFileUrl, int> _bucketFileUrlRepository;
        private readonly IMemoryCache _memoryCache;

        public CachedFileUrlAppService_Tests()
        {
            _cachedFileUrlAppService = GetRequiredService<ICachedFileUrlAppService>();
            _bucketFileUrlRepository = GetRequiredService<IRepository<BucketFileUrl, int>>();
            _memoryCache = GetRequiredService<IMemoryCache>();
        }

        [Fact]
        public async Task GetCachedComputeUrlAsync_Should_Return_Url_From_Database_When_Not_Cached()
        {
            // Arrange
            var fileId = 1;
            var providerCode = ProviderCodeConstants.CloudFlare;
            var expectedUrl = "https://example.com/test-file.jpg";

            // Create test data
            var bucketFileUrl = new BucketFileUrl
            {
                BucketFileId = fileId,
                ProviderCode = providerCode,
                ComputeUrl = expectedUrl
            };
            await _bucketFileUrlRepository.InsertAsync(bucketFileUrl, autoSave: true);

            // Act
            var result = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(fileId, providerCode);

            // Assert
            result.ShouldBe(expectedUrl);
        }

        [Fact]
        public async Task GetCachedComputeUrlAsync_Should_Return_Cached_Url_On_Second_Call()
        {
            // Arrange
            var fileId = 2;
            var providerCode = ProviderCodeConstants.CloudFlare;
            var expectedUrl = "https://example.com/cached-file.jpg";

            // Create test data
            var bucketFileUrl = new BucketFileUrl
            {
                BucketFileId = fileId,
                ProviderCode = providerCode,
                ComputeUrl = expectedUrl
            };
            await _bucketFileUrlRepository.InsertAsync(bucketFileUrl, autoSave: true);

            // Act - First call should cache the result
            var firstResult = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(fileId, providerCode);

            // Delete from database to ensure second call uses cache
            await _bucketFileUrlRepository.DeleteAsync(bucketFileUrl, autoSave: true);

            // Second call should return cached result
            var secondResult = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(fileId, providerCode);

            // Assert
            firstResult.ShouldBe(expectedUrl);
            secondResult.ShouldBe(expectedUrl); // Should be same even though deleted from DB
        }

        [Fact]
        public async Task GetCachedComputeUrlAsync_Should_Return_Empty_String_When_Not_Found()
        {
            // Arrange
            var fileId = 999; // Non-existent file
            var providerCode = ProviderCodeConstants.CloudFlare;

            // Act
            var result = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(fileId, providerCode);

            // Assert
            result.ShouldBe(string.Empty);
        }

        [Fact]
        public async Task ClearFileUrlCache_Should_Remove_Specific_Cache_Entry()
        {
            // Arrange
            var fileId = 3;
            var providerCode = ProviderCodeConstants.CloudFlare;
            var expectedUrl = "https://example.com/clear-test.jpg";

            var bucketFileUrl = new BucketFileUrl
            {
                BucketFileId = fileId,
                ProviderCode = providerCode,
                ComputeUrl = expectedUrl
            };
            await _bucketFileUrlRepository.InsertAsync(bucketFileUrl, autoSave: true);

            // Cache the result
            var cachedResult = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(fileId, providerCode);
            cachedResult.ShouldBe(expectedUrl);

            // Act - Clear the cache
            _cachedFileUrlAppService.ClearFileUrlCache(fileId, providerCode);

            // Delete from database
            await _bucketFileUrlRepository.DeleteAsync(bucketFileUrl, autoSave: true);

            // Get again - should return empty since cache is cleared and DB entry is deleted
            var resultAfterClear = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(fileId, providerCode);

            // Assert
            resultAfterClear.ShouldBe(string.Empty);
        }

        [Fact]
        public async Task ClearFileUrlCachesByFileId_Should_Remove_All_Entries_For_File()
        {
            // Arrange
            var fileId = 4;
            var provider1 = ProviderCodeConstants.CloudFlare;
            var provider2 = "AnotherProvider";
            var url1 = "https://cloudflare.com/file.jpg";
            var url2 = "https://another.com/file.jpg";

            // Create test data for multiple providers
            var bucketFileUrl1 = new BucketFileUrl
            {
                BucketFileId = fileId,
                ProviderCode = provider1,
                ComputeUrl = url1
            };
            var bucketFileUrl2 = new BucketFileUrl
            {
                BucketFileId = fileId,
                ProviderCode = provider2,
                ComputeUrl = url2
            };

            await _bucketFileUrlRepository.InsertAsync(bucketFileUrl1, autoSave: true);
            await _bucketFileUrlRepository.InsertAsync(bucketFileUrl2, autoSave: true);

            // Cache both results
            var cached1 = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(fileId, provider1);
            var cached2 = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(fileId, provider2);
            cached1.ShouldBe(url1);
            cached2.ShouldBe(url2);

            // Act - Clear all caches for this file
            _cachedFileUrlAppService.ClearFileUrlCachesByFileId(fileId);

            // Delete from database
            await _bucketFileUrlRepository.DeleteAsync(bucketFileUrl1, autoSave: true);
            await _bucketFileUrlRepository.DeleteAsync(bucketFileUrl2, autoSave: true);

            // Get again - both should return empty
            var result1 = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(fileId, provider1);
            var result2 = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(fileId, provider2);

            // Assert
            result1.ShouldBe(string.Empty);
            result2.ShouldBe(string.Empty);
        }

        [Fact]
        public void ClearAllFileUrlCaches_Should_Remove_All_Cache_Entries()
        {
            // This test is more of a smoke test since we can't easily verify
            // that all entries are cleared without reflection or other complex setup

            // Act - Should not throw
            _cachedFileUrlAppService.ClearAllFileUrlCaches();

            // Assert - If we get here without exception, the method works
            Assert.True(true);
        }
    }
}