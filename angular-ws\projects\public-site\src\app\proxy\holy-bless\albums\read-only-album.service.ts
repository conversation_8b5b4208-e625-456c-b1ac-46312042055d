import type { AlbumDto, AlbumFileDto, AlbumSearchDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyAlbumService {
  apiName = 'Default';
  

  getAlbumFiles = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumFileDto[]>({
      method: 'GET',
      url: '/api/app/read-only-album/album-files',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getAlbumFiles = (albumId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumFileDto[]>({
      method: 'GET',
      url: `/api/app/read-only-album/album-files/${albumId}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: AlbumSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AlbumDto>>({
      method: 'GET',
      url: '/api/app/read-only-album',
      params: { channelId: input.channelId, albumType: input.albumType, channelContentCode: input.channelContentCode, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
