import{a as q,b as Y}from"./chunk-XN4TN4BS.js";import"./chunk-PNGXS4EP.js";import{a as J}from"./chunk-EJOHHYKG.js";import{a as G}from"./chunk-RMOO4PXW.js";import"./chunk-T2K2OPF3.js";import"./chunk-CX2NCE6L.js";import"./chunk-2WHDSBNT.js";import"./chunk-XDERG77Q.js";import{a as j,b as z}from"./chunk-TPWS2CUE.js";import{c as N,d as $}from"./chunk-O7DPC57I.js";import"./chunk-KRRRQ7A6.js";import"./chunk-EFRKI2JO.js";import{c as U,d as V}from"./chunk-7JNW5ZNC.js";import"./chunk-5G3J65ZF.js";import"./chunk-7X7MFIN2.js";import{Z as H}from"./chunk-LS3LVTXN.js";import{d as A,e as W,g as L}from"./chunk-YFEKHFVJ.js";import"./chunk-BMA7WWEI.js";import{o as E,r as B}from"./chunk-CNIH62FZ.js";import{o as C,q as F}from"./chunk-D6WDCTDG.js";import{Cb as w,Hb as c,Ib as d,Lb as S,Na as P,Pa as v,Ra as l,Sb as g,Ub as u,Wb as D,X as p,Xb as T,Yb as R,Zb as k,ab as I,da as h,ea as y,gb as f,hc as O,jc as x,nb as m,ub as M,vb as b,wb as o,xb as a,yb as _}from"./chunk-BL4EGCPV.js";import"./chunk-4CLCTAJ7.js";var Q=(n,e)=>e.id;function X(n,e){if(n&1&&_(0,"img",14),n&2){let i=d().$implicit;m("src",i.thumbnailUrl,P)("alt",i.title+" \u5C01\u9762\u56FE\u7247")}}function Z(n,e){if(n&1&&(o(0,"div",15),_(1,"i",16),g(2),O(3,"date"),a()),n&2){let i=d().$implicit;l(2),u(" ",x(3,1,i.creationTime,"yyyy-MM-dd HH:mm:ss")," ")}}function ee(n,e){if(n&1){let i=w();o(0,"p-card",7),f(1,X,1,2,"ng-template",10),o(2,"p",11),c("click",function(){let s=h(i).$implicit,r=d();return y(r.navigateToArticle(s.id))}),g(3),a(),o(4,"p",12),g(5),a(),f(6,Z,4,4,"ng-template",13),a()}if(n&2){let i=e.$implicit;m("id","card-"+i.id),l(3),u(" ",i.title," "),l(2),u(" ",i.description," ")}}var K=class n{constructor(){this.i18nService=p(J);this.totalRecords=0;this.rows=6;this.first=0;this.isMobile=!1;this.selectedDate=null;this.collectionId=null;this.#e=p(G);this.#t=p(E);this.router=p(B);this.cardItems=[];this.checkMobile()}#e;#t;ngOnInit(){this.#t.queryParams.subscribe(e=>{this.collectionId=e.collectionId,this.loadCollectionSummary()})}loadCollectionSummary(){this.collectionId&&this.#e.getCollectionSummary(this.collectionId,{skip:this.first,maxResultCount:this.rows,year:this.selectedDate?.getFullYear(),month:(this.selectedDate?.getMonth()||0)+1}).subscribe({next:e=>{this.cardItems=e.articles,this.totalRecords=e.totalRecords},error:e=>{console.error("\u83B7\u53D6\u6458\u8981\u6570\u636E\u5931\u8D25:",e)}})}navigateToArticle(e){this.router.navigateByUrl(`/home/<USER>"app-image-cards"]],hostBindings:function(i,t){i&1&&c("resize",function(r){return t.onResize(r)},!1,v)},features:[k([C])],decls:11,vars:11,consts:[[1,"image-cards-container","p-6"],[1,"filters-toolbar","flex","justify-end","items-center","gap-4","mb-6","rounded-lg"],[1,"date-picker-container"],[1,"p-float-label"],["view","month","dateFormat","yy-mm","showClear","",3,"ngModelChange","onSelect","onClear","placeholder","ngModel","readonlyInput"],["icon","pi pi-play","severity","primary",3,"onClick","label","outlined"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-3","gap-6","mb-8"],["styleClass","card-item",3,"id"],[1,"pagination-container"],["styleClass","custom-paginator",3,"onPageChange","first","rows","totalRecords","rowsPerPageOptions","showPageLinks","showCurrentPageReport"],["pTemplate","header"],[1,"p-card-title","cursor-pointer",3,"click"],[1,"mt-2","text-gray-500","text-sm"],["pTemplate","footer"],[1,"card-image",3,"src","alt"],[1,"text-gray-500","text-sm","flex","items-center"],[1,"pi","pi-clock","mr-2"]],template:function(i,t){i&1&&(o(0,"div",0)(1,"div",1)(2,"div",2)(3,"span",3)(4,"p-calendar",4),R("ngModelChange",function(r){return T(t.selectedDate,r)||(t.selectedDate=r),r}),c("onSelect",function(r){return t.onDateChange(r)})("onClear",function(){return t.onDateChange(null)}),a()()(),o(5,"p-button",5),c("onClick",function(){return t.playCurrentPage()}),a()(),o(6,"div",6),M(7,ee,7,3,"p-card",7,Q),a(),o(9,"div",8)(10,"p-paginator",9),c("onPageChange",function(r){return t.onPageChange(r)}),a()()()),i&2&&(l(4),S("placeholder",t.i18nService.translate("common.selectDate")),D("ngModel",t.selectedDate),m("readonlyInput",!0),l(),m("label",t.i18nService.translate("common.play"))("outlined",!0),l(2),b(t.cardItems),l(3),m("first",t.first)("rows",t.rows)("totalRecords",t.totalRecords)("rowsPerPageOptions",t.rowsPerPageOptions)("showPageLinks",!t.isMobile)("showCurrentPageReport",t.isMobile))},dependencies:[F,C,z,j,H,$,N,Y,q,V,U,L,A,W],styles:["[_nghost-%COMP%]{flex:1}.card-image[_ngcontent-%COMP%]{width:100%;height:12rem;object-fit:cover;transition:transform .3s ease}.card-image[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.pagination-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:2rem}.pagination-container[_ngcontent-%COMP%]   .custom-paginator[_ngcontent-%COMP%]{border:none;background:transparent}"]})}};export{K as ImageCardsComponent};
