# HolyBless MySQL to PostgreSQL Migration Tool

This tool migrates chapter data from MySQL to PostgreSQL database according to the specifications in Migration.md.

## Overview

- **Source Database**: MySQL
- **Target Database**: PostgreSQL  
- **Migration Tool**: Dapper.NET
- **Data**: Chapter information from `holybless.admincms_cms_channeltwo` table

## Configuration

Update the connection strings in `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "MySQL": "Server=your_mysql_server;Database=holybless;Uid=your_username;Pwd=your_password;",
    "PostgreSQL": "Host=your_postgres_server;Database=holybless;Username=your_username;Password=your_password;"
  }
}
```

## Data Transformation

The migration performs the following transformations:

1. **Source SQL Query**:
   ```sql
   select 1 as BookId, 0 as Views, 0 as Likes, Id, parent_id, name,
   description, acc.weigh, FROM_UNIXTIME(acc.createtime) as createtime, 
   FROM_UNIXTIME(acc.updatetime) as updatetime 
   from holybless.admincms_cms_channeltwo acc 
   order by acc.parent_id, acc.weigh
   ```

2. **Data Transformation**:
   - `parent_id = 0` → `null` (for ParentChapterId)
   - `name` → `Title`
   - `description` → `Content`
   - `weigh` → `Weight`
   - Unix timestamps converted to DateTime

3. **Target Table**: PostgreSQL `public."Chapters"` table

## Usage

1. **Build the project**:
   ```bash
   dotnet build
   ```

2. **Run the migration**:
   ```bash
   dotnet run
   ```

3. **Monitor the output**:
   - The tool provides progress updates every 100 records
   - Logs are written to console
   - Transaction rollback on errors

## Features

- ✅ **Transaction Safety**: Uses database transactions with rollback on failure
- ✅ **Progress Monitoring**: Logs progress every 100 records
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Data Validation**: Handles null values and data type conversions
- ✅ **Configuration**: Flexible connection string configuration
- ✅ **Logging**: Detailed logging for troubleshooting

## Error Handling

- Database connection failures are logged and reported
- SQL execution errors trigger transaction rollback
- All exceptions are logged with full stack traces
- Process exits with error code 1 on failure

## Dependencies

- Dapper 2.1.35
- MySql.Data 8.4.0
- Npgsql 8.0.3
- Microsoft.Extensions.* packages for hosting and configuration
