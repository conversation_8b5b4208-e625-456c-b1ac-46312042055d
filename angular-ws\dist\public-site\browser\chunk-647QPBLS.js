import{c as j,d as W}from"./chunk-OONYURMG.js";import{a as V}from"./chunk-G3TS7SIT.js";import{a as P}from"./chunk-RMOO4PXW.js";import"./chunk-T2K2OPF3.js";import"./chunk-XDERG77Q.js";import"./chunk-7X7MFIN2.js";import"./chunk-LS3LVTXN.js";import"./chunk-YFEKHFVJ.js";import{n as q,o as z}from"./chunk-CNIH62FZ.js";import{k as R,l as O,o as L,q as B}from"./chunk-D6WDCTDG.js";import{Cb as w,Hb as C,Ib as y,Ma as S,Ra as c,Sb as m,Tb as D,Ub as x,Wb as F,X as u,Xb as N,Yb as k,ab as A,da as I,ea as _,gb as v,hc as M,jc as H,nb as f,qa as b,ra as g,sb as E,wb as a,xb as n,yb as T}from"./chunk-BL4EGCPV.js";import"./chunk-4CLCTAJ7.js";function K(r,e){if(r&1&&(T(0,"i",14),m(1),M(2,"date")),r&2){let t=y();c(),x(" ",H(2,1,t.articleDetail.deliveryDate,"yy-MM-dd HH:mm:ss")," ")}}function Q(r,e){r&1&&(a(0,"div",15),m(1," \u6682\u65E0\u76EE\u5F55 "),n())}function U(r,e){if(r&1){let t=w();a(0,"a",16),C("click",function(){let p=I(t).$implicit,l=y();return _(l.onTocItemClick(p))}),m(1),n()}if(r&2){let t=e.$implicit;c(),x(" ",t.text," ")}}var $=class r{constructor(){this.#e=u(P);this.#t=u(V);this.#i=u(z);this.sanitizer=u(q);this.elementRef=u(b);this.collectionId=null;this.files=[];this.articleId=null;this.articleDetail=null;this.tocItems=g([]);this.articleContent=g("")}#e;#t;#i;ngOnInit(){this.#i.queryParams.subscribe(e=>{this.collectionId=e.collectionId,this.loadCollectionSummary()})}loadCollectionSummary(){this.#e.getCollectionTreeAndArticleTitles(this.collectionId).subscribe({next:e=>{this.files=e.map(t=>this.buildTreeNode(t))},error:e=>{console.error("\u83B7\u53D6\u6587\u7AE0\u6811\u6570\u636E\u5931\u8D25:",e)}})}buildTreeNode(e){return{key:e.id,label:e.name||e.title,data:e,children:e.articles?e.articles.map(t=>this.buildTreeNode(t)):[]}}loadArticleDetail(e){e.key&&this.#t.getArticleAggregate(Number(e.key)).subscribe({next:t=>{this.articleDetail=t,this.extractTocFromContent()},error:t=>{console.error("\u83B7\u53D6\u6587\u7AE0\u8BE6\u60C5\u5931\u8D25:",t)}})}extractTocFromContent(){let e=this.articleDetail?.content;if(!e){this.tocItems.set([]),this.articleContent.set("");return}let t=document.createElement("div");t.innerHTML=e;let i=t.querySelectorAll('a[href^="#"]'),p=[];i.forEach(d=>{let s=d.getAttribute("href"),o=d.textContent?.trim();if(s&&o){let h=s.substring(1);p.push({href:s,text:o,anchorId:h})}});let l=t.querySelector('a[name^="_"]');if(l?.parentElement){let d=l.parentElement,s=d.parentNode;if(s){let o=d.previousSibling;for(;o;){let h=o;o=o.previousSibling,s.removeChild(h)}}}let G=p.filter((d,s,o)=>s===o.findIndex(h=>h.anchorId===d.anchorId)),J=this.sanitizer.bypassSecurityTrustHtml(t.innerHTML);this.articleContent.set(J),this.tocItems.set(G)}onTocItemClick(e){this.scrollToAnchor(e.anchorId.trim())}scrollToAnchor(e){let t=this.elementRef.nativeElement.querySelector(".articaldetail-container");if(!t)return;let i=t.querySelector(`a[name="${e}"]`);i?i.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"}):console.warn(`\u672A\u627E\u5230\u951A\u70B9: ${e}`)}trackByTocItem(e,t){return t.anchorId}static{this.\u0275fac=function(t){return new(t||r)}}static{this.\u0275cmp=A({type:r,selectors:[["app-collection-artical-tree"]],decls:22,vars:10,consts:[[1,"flex","flex-1"],["styleClass","w-[20rem] h-full","selectionMode","single","virtualScrollItemSize","36",3,"selectionChange","onNodeSelect","value","selection","virtualScroll","filter"],[1,"articaldetail-container","prose","max-w-none","p-6","flex-1",2,"overflow-y","auto","max-height","calc(100vh - 60px)"],[1,"text-3xl","font-bold","mb-4"],[1,"text-sm","mb-4","flex","items-center","gap-2"],[1,"mb-4","articaldetail-container",3,"innerHTML"],[1,"p-6","w-[20rem]","border-l-2"],[1,"flex","flex-col","max-h-[60vh]","overflow-y-auto"],[1,"text-lg","font-semibold","mb-4"],["class","text-sm italic",4,"ngIf"],["class","mt-2 cursor-pointer hover:text-primary-600 underline",3,"click",4,"ngFor","ngForOf","ngForTrackBy"],[1,"mt-6"],[1,"flex","justify-between","items-center","mt-3"],[1,"pi","pi-download"],[1,"pi","pi-clock"],[1,"text-sm","italic"],[1,"mt-2","cursor-pointer","hover:text-primary-600","underline",3,"click"]],template:function(t,i){t&1&&(a(0,"div",0)(1,"p-tree",1),k("selectionChange",function(l){return N(i.selectedFile,l)||(i.selectedFile=l),l}),C("onNodeSelect",function(l){return i.loadArticleDetail(l.node)}),n(),a(2,"div",2)(3,"h1",3),m(4),n(),a(5,"p",4),v(6,K,3,4),n(),T(7,"div",5),n(),a(8,"div",6)(9,"div")(10,"div",7)(11,"h3",8),m(12,"\u76EE\u5F55"),n(),v(13,Q,2,0,"div",9)(14,U,2,1,"a",10),n(),a(15,"div",11)(16,"h3"),m(17,"\u9644\u4EF6\u4E0B\u8F7D"),n(),a(18,"p",12)(19,"span"),m(20,"Attachment.zip"),n(),T(21,"i",13),n()()()()()),t&2&&(c(),f("value",i.files),F("selection",i.selectedFile),f("virtualScroll",!0)("filter",!0),c(3),D(i.articleDetail==null?null:i.articleDetail.title),c(2),E(i.articleDetail?6:-1),c(),f("innerHTML",i.articleContent(),S),c(6),f("ngIf",i.tocItems().length===0),c(),f("ngForOf",i.tocItems())("ngForTrackBy",i.trackByTocItem))},dependencies:[B,R,O,L,W,j],styles:["[_nghost-%COMP%]{flex:1;display:flex}[_nghost-%COMP%]     .p-virtualscroller{height:calc(100% - 30px)!important}"]})}};export{$ as CollectionArticleTreeComponent};
