using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.VirtualFolders
{
    public interface IReadOnlyVirtualFolderAppService : IApplicationService
    {
        Task<PagedResultDto<VirtualFolderDto>> GetListAsync(VirtualFolderSearchDto input);

        Task<PagedResultDto<VirtualFolderFileDto>> GetFolderFilesAsync(VirtualFolderFileSearchDto input);
    }
}