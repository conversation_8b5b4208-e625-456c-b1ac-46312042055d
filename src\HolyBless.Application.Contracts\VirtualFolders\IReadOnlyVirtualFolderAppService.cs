using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.VirtualFolders
{
    public interface IReadOnlyVirtualFolderAppService : IApplicationService
    {
        Task<string> GetFolderTreeJson(int rootFolderId);
        Task<VirtualFolderTreeDto> GetFolderTreeWithFilesAsync(int rootFolderId);
        Task<PagedResultDto<VirtualFolderDto>> GetListAsync(VirtualFolderSearchDto input);
    }
}
