import { CommonModule } from '@angular/common';
import { Component, HostListener, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';

@Component({
  selector: 'app-album-card',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    CardModule,
    FormsModule,
    PaginatorModule,
    ButtonModule
  ],
  templateUrl: './album-card.html',
  styleUrls: ['./album-card.scss'],
})
export class AlbumCardComponent {
  router = inject(Router);
  totalRecords = 50;
  rows = 10;
  first = 0;
  private _isMobile = false;

  constructor() {
    this.checkMobile();
  }

  // 检测是否为移动端
  get isMobile(): boolean {
    return this._isMobile;
  }

  // 根据设备类型返回每页条数选项
  get rowsPerPageOptions(): number[] | undefined {
    return this.isMobile ? undefined : [10, 20, 50];
  }

  private checkMobile() {
    this._isMobile = window.innerWidth <= 768;
  }

  cardItems = [
    {
      id: '1',
      name: 'Ebook 1',
      description: 'Description for Ebook 1',
      title: 'Ebook 1 Title',
      thumbnailUrl: 'assets/images/灵音.jpg',
    },
    {
      id: '2',
      name: 'Ebook 2',
      description: 'Description for Ebook 2',
      title: 'Ebook 1 Title',
      thumbnailUrl: 'assets/images/灵音.jpg',
    },
    {
      id: '3',
      name: 'Ebook 3',
      description: 'Description for Ebook 3',
      title: 'Ebook 1 Title',
      thumbnailUrl: 'assets/images/灵音.jpg',
    },
  ];

  openBook(item: any) {
    this.router.navigate(['/podcast/album-detail']);
  }

  onPageChange(event: any) {
    this.first = event.first;
    this.rows = event.rows;
    console.log('页面变化:', event);
    // 这里可以添加数据加载逻辑
  }

  // 监听窗口大小变化
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkMobile();
  }
}
