﻿using System;
using HolyBless.Enums;
using HolyBless.Interfaces;

namespace HolyBless.Buckets.Dtos
{
    public class FileSearchResultDto : IHaveFileUrl
    {
        public string FileName { get; set; } = string.Empty;
        public string? MediaType { get; set; }
        public ContentCategory? ContentCategory { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public int FileId { get; set; }
        public string? FileUrl { get; set; }
    }
}