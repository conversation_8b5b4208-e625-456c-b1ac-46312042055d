﻿using System;
using System.Threading.Tasks;
using HolyBless.MySqlMigration.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace HolyBless.MySqlMigration
{
    /// <summary>
    /// Main program for MySQL to PostgreSQL migration
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("HolyBless MySQL to PostgreSQL Migration Tool");
            Console.WriteLine("============================================");

            try
            {
                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(AppContext.BaseDirectory)
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
                    .AddEnvironmentVariables()
                    .Build();

                // Build host
                var host = Host.CreateDefaultBuilder(args)
                    .ConfigureServices((context, services) =>
                    {
                        services.AddSingleton<IConfiguration>(configuration);
                        services.AddScoped<ChapterMigrationService>();
                        services.AddLogging(builder =>
                        {
                            builder.AddConsole();
                            builder.SetMinimumLevel(LogLevel.Information);
                        });
                    })
                    .Build();

                // Get services
                using var scope = host.Services.CreateScope();
                var migrationService = scope.ServiceProvider.GetRequiredService<ChapterMigrationService>();
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

                // Execute migration
                logger.LogInformation("Starting chapter migration process...");
                var migratedCount = await migrationService.MigrateChaptersAsync();

                Console.WriteLine($"Migration completed successfully! Migrated {migratedCount} chapters.");
                logger.LogInformation($"Migration completed successfully! Migrated {migratedCount} chapters.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Migration failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Environment.Exit(1);
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}