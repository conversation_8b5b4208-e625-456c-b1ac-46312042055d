using Volo.Abp.Application.Dtos;
using HolyBless.Enums;
using System.Collections.Generic;
using System;

namespace HolyBless.Buckets.Dtos
{
    public class BucketFileDto : EntityDto<int>
    {
        public string FileName { get; set; } = default!;
        public string? Title { get; set; }
        public string RelativePathInBucket { get; set; } = "";
        public string? LanguageCode { get; set; }
        public string? SpokenLangCode { get; set; }
        public MediaType MediaType { get; set; }
        public ContentCategory ContentCategory { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public string BucketName { get; set; } = "";//Name of the bucket where this file is stored
        public long? Size { get; set; }
        public int Views { get; set; } = 0;
        public string? YoutubeId { get; set; }
        public string Environment { get; set; } = "";
        public bool Exists { get; set; } = true;
    }
}