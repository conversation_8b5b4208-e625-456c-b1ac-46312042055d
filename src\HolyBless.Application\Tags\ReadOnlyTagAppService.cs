using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Entities.Tags;
using HolyBless.Tags.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Tags
{
    [AllowAnonymous]
    public class ReadOnlyTagAppService : ApplicationService, IReadOnlyTagAppService
    {
        protected readonly IRepository<Tag, int> _repository;

        public ReadOnlyTagAppService(IRepository<Tag, int> repository)
        {
            _repository = repository;
        }

        public virtual async Task<TagDto> GetAsync(int id)
        {
            var queryable = await _repository.GetQueryableAsync();
            var tag = await queryable
                .Include(x => x.ContentCode)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (tag == null)
            {
                throw new EntityNotFoundException(typeof(Tag), id);
            }

            return ObjectMapper.Map<Tag, TagDto>(tag);
        }

        public virtual async Task<PagedResultDto<TagDto>> GetListAsync(TagSearchDto input)
        {
            var queryable = await _repository.GetQueryableAsync();
            queryable = queryable
                .Include(x => x.ContentCode)
                .WhereIf(
                    !string.IsNullOrWhiteSpace(input.TagName),
                    x => x.TagName.Contains(input.TagName!)
                )
                .WhereIf(
                    !string.IsNullOrWhiteSpace(input.ContentCode),
                    x => x.ContentCode == input.ContentCode
                );
            var totalCount = await AsyncExecuter.CountAsync(queryable);
            queryable = queryable
                .OrderBy(input.Sorting ?? "TagName")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);
            var tags = await AsyncExecuter.ToListAsync(queryable);
            var dtoList = ObjectMapper.Map<List<Tag>, List<TagDto>>(tags);
            return new PagedResultDto<TagDto>(
                totalCount,
                dtoList
            );
        }
    }
}