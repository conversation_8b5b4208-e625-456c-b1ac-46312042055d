using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;
using HolyBless.Enums;

namespace HolyBless.Buckets.Dtos
{
    public class BucketFileSearchDto : PagedAndSortedResultRequestDto
    {
        public string? FileName { get; set; }
        public DateTime? DeliveryDateStart { get; set; }
        public DateTime? DeliveryDateEnd { get; set; }
        public List<ContentCategory>? ContentCategories { get; set; }
    }
}