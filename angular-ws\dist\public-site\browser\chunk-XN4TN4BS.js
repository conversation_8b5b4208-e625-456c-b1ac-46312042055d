import{a as Xe}from"./chunk-PNGXS4EP.js";import{a as tt}from"./chunk-CX2NCE6L.js";import{a as nt}from"./chunk-2WHDSBNT.js";import{a as it}from"./chunk-XDERG77Q.js";import{a as rt}from"./chunk-EFRKI2JO.js";import{c as dt}from"./chunk-7JNW5ZNC.js";import{c as oe}from"./chunk-5G3J65ZF.js";import{e as st,f as lt,h as ct}from"./chunk-7X7MFIN2.js";import{$ as N,D as ze,G as We,J as Qe,P as ae,S as qe,W as je,Z as Ze,_ as de,a as R,b as he,c as Le,ca as Ge,e as Be,ea as Je,ga as et,ha as at,i as Re,j as Ne,k as ne,l as Ue,m as Ke,p as W,q as E,t as me,w as re}from"./chunk-LS3LVTXN.js";import{a as ot}from"./chunk-YFEKHFVJ.js";import{c as Pe,d as te,f as j,g as Ae,h as ie}from"./chunk-BMA7WWEI.js";import{j as Ee,k as Oe,l as Fe,m as $e,n as Ye,q as He}from"./chunk-D6WDCTDG.js";import{$b as B,Ab as $,Bb as Q,Cb as M,Hb as C,Ib as a,Jb as Se,Kb as _e,Nb as Y,Ob as ce,Pb as I,Q as ve,Qb as V,R as ke,Ra as l,Rb as Me,S as ye,Sb as x,Tb as L,Ub as z,Vb as Ie,Wa as ue,X as be,Zb as Ve,ab as xe,ac as ee,bb as Te,da as d,ea as p,eb as De,ga as we,gb as _,ma as H,mb as S,na as Ce,nb as c,rb as X,uc as T,vc as q,wb as m,xb as h,yb as P,zb as F}from"./chunk-BL4EGCPV.js";var mt=["date"],ft=["header"],gt=["footer"],vt=["disabledDate"],kt=["decade"],yt=["previousicon"],bt=["nexticon"],wt=["triggericon"],Ct=["clearicon"],xt=["decrementicon"],Tt=["incrementicon"],Dt=["inputicon"],St=["container"],Mt=["inputfield"],It=["contentWrapper"],Vt=[[["p-header"]],[["p-footer"]]],Et=["p-header","p-footer"],Ot=n=>({clickCallBack:n}),Ft=n=>({"p-datepicker-input-icon":n}),$t=(n,s)=>({showTransitionParams:n,hideTransitionParams:s}),Yt=n=>({value:"visible",params:n}),pt=n=>({visibility:n}),fe=n=>({$implicit:n}),Ht=(n,s)=>({"p-datepicker-day-cell":!0,"p-datepicker-other-month":n,"p-datepicker-today":s}),Pt=(n,s)=>({"p-datepicker-month":!0,"p-datepicker-month-selected":n,"p-disabled":s}),At=(n,s)=>({"p-datepicker-year":!0,"p-datepicker-year-selected":n,"p-disabled":s}),ut=n=>[n];function Lt(n,s){if(n&1){let e=M();m(0,"TimesIcon",11),C("click",function(){d(e);let i=a(3);return p(i.clear())}),h()}n&2&&X("p-datepicker-clear-icon")}function Bt(n,s){}function Rt(n,s){n&1&&_(0,Bt,0,0,"ng-template")}function Nt(n,s){if(n&1){let e=M();m(0,"span",12),C("click",function(){d(e);let i=a(3);return p(i.clear())}),_(1,Rt,1,0,null,13),h()}if(n&2){let e=a(3);l(),c("ngTemplateOutlet",e.clearIconTemplate||e._clearIconTemplate)}}function Ut(n,s){if(n&1&&(F(0),_(1,Lt,1,2,"TimesIcon",9)(2,Nt,2,1,"span",10),$()),n&2){let e=a(2);l(),c("ngIf",!e.clearIconTemplate&&!e._clearIconTemplate),l(),c("ngIf",e.clearIconTemplate||e._clearIconTemplate)}}function Kt(n,s){if(n&1&&P(0,"span",16),n&2){let e=a(3);c("ngClass",e.icon)}}function zt(n,s){n&1&&P(0,"CalendarIcon")}function Wt(n,s){}function Qt(n,s){n&1&&_(0,Wt,0,0,"ng-template")}function qt(n,s){if(n&1&&(F(0),_(1,zt,1,0,"CalendarIcon",7)(2,Qt,1,0,null,13),$()),n&2){let e=a(3);l(),c("ngIf",!e.triggerIconTemplate&&!e._triggerIconTemplate),l(),c("ngTemplateOutlet",e.triggerIconTemplate||e._triggerIconTemplate)}}function jt(n,s){if(n&1){let e=M();m(0,"button",14),C("click",function(i){d(e),a();let r=Me(1),o=a();return p(o.onButtonClick(i,r))}),_(1,Kt,1,1,"span",15)(2,qt,3,2,"ng-container",7),h()}if(n&2){let e,t=a(2);c("disabled",t.disabled),S("aria-label",t.iconButtonAriaLabel)("aria-expanded",(e=t.overlayVisible)!==null&&e!==void 0?e:!1)("aria-controls",t.overlayVisible?t.panelId:null),l(),c("ngIf",t.icon),l(),c("ngIf",!t.icon)}}function Zt(n,s){if(n&1){let e=M();m(0,"CalendarIcon",20),C("click",function(i){d(e);let r=a(3);return p(r.onButtonClick(i))}),h()}if(n&2){let e=a(3);c("ngClass",B(1,Ft,e.showOnFocus))}}function Gt(n,s){n&1&&Q(0)}function Jt(n,s){if(n&1&&(F(0),m(1,"span",17),_(2,Zt,1,3,"CalendarIcon",18)(3,Gt,1,0,"ng-container",19),h(),$()),n&2){let e=a(2);l(2),c("ngIf",!e.inputIconTemplate&&!e._inputIconTemplate),l(),c("ngTemplateOutlet",e.inputIconTemplate||e._inputIconTemplate)("ngTemplateOutletContext",B(3,Ot,e.onButtonClick.bind(e)))}}function Xt(n,s){if(n&1){let e=M();m(0,"input",6,1),C("focus",function(i){d(e);let r=a();return p(r.onInputFocus(i))})("keydown",function(i){d(e);let r=a();return p(r.onInputKeydown(i))})("click",function(){d(e);let i=a();return p(i.onInputClick())})("blur",function(i){d(e);let r=a();return p(r.onInputBlur(i))})("input",function(i){d(e);let r=a();return p(r.onUserInput(i))}),h(),_(2,Ut,3,2,"ng-container",7)(3,jt,3,6,"button",8)(4,Jt,4,5,"ng-container",7)}if(n&2){let e,t=a();X(t.inputStyleClass),c("value",t.inputFieldValue)("readonly",t.readonlyInput)("ngStyle",t.inputStyle)("ngClass","p-datepicker-input")("placeholder",t.placeholder||"")("disabled",t.disabled)("pAutoFocus",t.autofocus)("variant",t.variant)("fluid",t.hasFluid),S("id",t.inputId)("name",t.name)("required",t.required)("aria-required",t.required)("aria-expanded",(e=t.overlayVisible)!==null&&e!==void 0?e:!1)("aria-controls",t.overlayVisible?t.panelId:null)("aria-labelledby",t.ariaLabelledBy)("aria-label",t.ariaLabel)("tabindex",t.tabindex)("inputmode",t.touchUI?"off":null),l(2),c("ngIf",t.showClear&&!t.disabled&&t.value!=null),l(),c("ngIf",t.showIcon&&t.iconDisplay==="button"),l(),c("ngIf",t.iconDisplay==="input"&&t.showIcon)}}function ei(n,s){n&1&&Q(0)}function ti(n,s){n&1&&P(0,"ChevronLeftIcon")}function ii(n,s){}function ni(n,s){n&1&&_(0,ii,0,0,"ng-template")}function ri(n,s){if(n&1&&(m(0,"span"),_(1,ni,1,0,null,13),h()),n&2){let e=a(4);l(),c("ngTemplateOutlet",e.previousIconTemplate||e._previousIconTemplate)}}function ai(n,s){if(n&1){let e=M();m(0,"button",37),C("click",function(i){d(e);let r=a(4);return p(r.switchToMonthView(i))})("keydown",function(i){d(e);let r=a(4);return p(r.onContainerButtonKeydown(i))}),x(1),h()}if(n&2){let e=a().$implicit,t=a(3);c("disabled",t.switchViewButtonDisabled()),S("aria-label",t.getTranslation("chooseMonth")),l(),z(" ",t.getMonthName(e.month)," ")}}function oi(n,s){if(n&1){let e=M();m(0,"button",38),C("click",function(i){d(e);let r=a(4);return p(r.switchToYearView(i))})("keydown",function(i){d(e);let r=a(4);return p(r.onContainerButtonKeydown(i))}),x(1),h()}if(n&2){let e=a().$implicit,t=a(3);c("disabled",t.switchViewButtonDisabled()),S("aria-label",t.getTranslation("chooseYear")),l(),z(" ",t.getYear(e)," ")}}function si(n,s){if(n&1&&(F(0),x(1),$()),n&2){let e=a(5);l(),Ie("",e.yearPickerValues()[0]," - ",e.yearPickerValues()[e.yearPickerValues().length-1],"")}}function li(n,s){n&1&&Q(0)}function ci(n,s){if(n&1&&(m(0,"span",39),_(1,si,2,2,"ng-container",7)(2,li,1,0,"ng-container",19),h()),n&2){let e=a(4);l(),c("ngIf",!e.decadeTemplate&&e._decadeTemplate),l(),c("ngTemplateOutlet",e.decadeTemplate||e._decadeTemplate)("ngTemplateOutletContext",B(3,fe,e.yearPickerValues))}}function di(n,s){n&1&&P(0,"ChevronRightIcon")}function pi(n,s){}function ui(n,s){n&1&&_(0,pi,0,0,"ng-template")}function _i(n,s){if(n&1&&(m(0,"span"),_(1,ui,1,0,null,13),h()),n&2){let e=a(4);l(),c("ngTemplateOutlet",e.nextIconTemplate||e._nextIconTemplate)}}function hi(n,s){if(n&1&&(m(0,"th",44)(1,"span"),x(2),h()()),n&2){let e=a(5);l(2),L(e.getTranslation("weekHeader"))}}function mi(n,s){if(n&1&&(m(0,"th",45)(1,"span",46),x(2),h()()),n&2){let e=s.$implicit;l(2),L(e)}}function fi(n,s){if(n&1&&(m(0,"td",49)(1,"span",50),x(2),h()()),n&2){let e=a().index,t=a(2).$implicit;l(2),z(" ",t.weekNumbers[e]," ")}}function gi(n,s){if(n&1&&(F(0),x(1),$()),n&2){let e=a(2).$implicit;l(),L(e.day)}}function vi(n,s){n&1&&Q(0)}function ki(n,s){if(n&1&&(F(0),_(1,vi,1,0,"ng-container",19),$()),n&2){let e=a(2).$implicit,t=a(6);l(),c("ngTemplateOutlet",t.dateTemplate||t._dateTemplate)("ngTemplateOutletContext",B(2,fe,e))}}function yi(n,s){n&1&&Q(0)}function bi(n,s){if(n&1&&(F(0),_(1,yi,1,0,"ng-container",19),$()),n&2){let e=a(2).$implicit,t=a(6);l(),c("ngTemplateOutlet",t.disabledDateTemplate||t._disabledDateTemplate)("ngTemplateOutletContext",B(2,fe,e))}}function wi(n,s){if(n&1&&(m(0,"div",53),x(1),h()),n&2){let e=a(2).$implicit;l(),z(" ",e.day," ")}}function Ci(n,s){if(n&1){let e=M();F(0),m(1,"span",51),C("click",function(i){d(e);let r=a().$implicit,o=a(6);return p(o.onDateSelect(i,r))})("keydown",function(i){d(e);let r=a().$implicit,o=a(3).index,u=a(3);return p(u.onDateCellKeydown(i,r,o))}),_(2,gi,2,1,"ng-container",7)(3,ki,2,4,"ng-container",7)(4,bi,2,4,"ng-container",7),h(),_(5,wi,2,1,"div",52),$()}if(n&2){let e=a().$implicit,t=a(6);l(),c("ngClass",t.dayClass(e)),S("data-date",t.formatDateKey(t.formatDateMetaToDate(e))),l(),c("ngIf",!t.dateTemplate&&!t._dateTemplate&&(e.selectable||!t.disabledDateTemplate&&!t._disabledDateTemplate)),l(),c("ngIf",e.selectable||!t.disabledDateTemplate&&!t._disabledDateTemplate),l(),c("ngIf",!e.selectable),l(),c("ngIf",t.isSelected(e))}}function xi(n,s){if(n&1&&(m(0,"td",16),_(1,Ci,6,6,"ng-container",7),h()),n&2){let e=s.$implicit,t=a(6);c("ngClass",ee(3,Ht,e.otherMonth,e.today)),S("aria-label",e.day),l(),c("ngIf",e.otherMonth?t.showOtherMonths:!0)}}function Ti(n,s){if(n&1&&(m(0,"tr"),_(1,fi,3,1,"td",47)(2,xi,2,6,"td",48),h()),n&2){let e=s.$implicit,t=a(5);l(),c("ngIf",t.showWeek),l(),c("ngForOf",e)}}function Di(n,s){if(n&1&&(m(0,"table",40)(1,"thead")(2,"tr"),_(3,hi,3,1,"th",41)(4,mi,3,1,"th",42),h()(),m(5,"tbody"),_(6,Ti,3,2,"tr",43),h()()),n&2){let e=a().$implicit,t=a(3);l(3),c("ngIf",t.showWeek),l(),c("ngForOf",t.weekDays),l(2),c("ngForOf",e.dates)}}function Si(n,s){if(n&1){let e=M();m(0,"div",28)(1,"div",29)(2,"p-button",30),C("keydown",function(i){d(e);let r=a(3);return p(r.onContainerButtonKeydown(i))})("onClick",function(i){d(e);let r=a(3);return p(r.onPrevButtonClick(i))}),_(3,ti,1,0,"ChevronLeftIcon",7)(4,ri,2,1,"span",7),h(),m(5,"div",31),_(6,ai,2,3,"button",32)(7,oi,2,3,"button",33)(8,ci,3,5,"span",34),h(),m(9,"p-button",35),C("keydown",function(i){d(e);let r=a(3);return p(r.onContainerButtonKeydown(i))})("onClick",function(i){d(e);let r=a(3);return p(r.onNextButtonClick(i))}),_(10,di,1,0,"ChevronRightIcon",7)(11,_i,2,1,"span",7),h()(),_(12,Di,7,3,"table",36),h()}if(n&2){let e=s.index,t=a(3);l(2),c("ngStyle",B(12,pt,e===0?"visible":"hidden")),S("aria-label",t.prevIconAriaLabel),l(),c("ngIf",!t.previousIconTemplate&&!t._previousIconTemplate),l(),c("ngIf",t.previousIconTemplate||!t._previousIconTemplate),l(2),c("ngIf",t.currentView==="date"),l(),c("ngIf",t.currentView!=="year"),l(),c("ngIf",t.currentView==="year"),l(),c("ngStyle",B(14,pt,e===t.months.length-1?"visible":"hidden")),S("aria-label",t.nextIconAriaLabel),l(),c("ngIf",!t.nextIconTemplate&&!t._nextIconTemplate),l(),c("ngIf",t.nextIconTemplate||!t._nextIconTemplate),l(),c("ngIf",t.currentView==="date")}}function Mi(n,s){if(n&1&&(m(0,"div",53),x(1),h()),n&2){let e=a().$implicit;l(),z(" ",e," ")}}function Ii(n,s){if(n&1){let e=M();m(0,"span",56),C("click",function(i){let r=d(e).index,o=a(4);return p(o.onMonthSelect(i,r))})("keydown",function(i){let r=d(e).index,o=a(4);return p(o.onMonthCellKeydown(i,r))}),x(1),_(2,Mi,2,1,"div",52),h()}if(n&2){let e=s.$implicit,t=s.index,i=a(4);c("ngClass",ee(3,Pt,i.isMonthSelected(t),i.isMonthDisabled(t))),l(),z(" ",e," "),l(),c("ngIf",i.isMonthSelected(t))}}function Vi(n,s){if(n&1&&(m(0,"div",54),_(1,Ii,3,6,"span",55),h()),n&2){let e=a(3);l(),c("ngForOf",e.monthPickerValues())}}function Ei(n,s){if(n&1&&(m(0,"div",53),x(1),h()),n&2){let e=a().$implicit;l(),z(" ",e," ")}}function Oi(n,s){if(n&1){let e=M();m(0,"span",56),C("click",function(i){let r=d(e).$implicit,o=a(4);return p(o.onYearSelect(i,r))})("keydown",function(i){let r=d(e).$implicit,o=a(4);return p(o.onYearCellKeydown(i,r))}),x(1),_(2,Ei,2,1,"div",52),h()}if(n&2){let e=s.$implicit,t=a(4);c("ngClass",ee(3,At,t.isYearSelected(e),t.isYearDisabled(e))),l(),z(" ",e," "),l(),c("ngIf",t.isYearSelected(e))}}function Fi(n,s){if(n&1&&(m(0,"div",57),_(1,Oi,3,6,"span",55),h()),n&2){let e=a(3);l(),c("ngForOf",e.yearPickerValues())}}function $i(n,s){if(n&1&&(F(0),m(1,"div",24),_(2,Si,13,16,"div",25),h(),_(3,Vi,2,1,"div",26)(4,Fi,2,1,"div",27),$()),n&2){let e=a(2);l(2),c("ngForOf",e.months),l(),c("ngIf",e.currentView==="month"),l(),c("ngIf",e.currentView==="year")}}function Yi(n,s){n&1&&P(0,"ChevronUpIcon")}function Hi(n,s){}function Pi(n,s){n&1&&_(0,Hi,0,0,"ng-template")}function Ai(n,s){n&1&&(F(0),x(1,"0"),$())}function Li(n,s){n&1&&P(0,"ChevronDownIcon")}function Bi(n,s){}function Ri(n,s){n&1&&_(0,Bi,0,0,"ng-template")}function Ni(n,s){n&1&&P(0,"ChevronUpIcon")}function Ui(n,s){}function Ki(n,s){n&1&&_(0,Ui,0,0,"ng-template")}function zi(n,s){n&1&&(F(0),x(1,"0"),$())}function Wi(n,s){n&1&&P(0,"ChevronDownIcon")}function Qi(n,s){}function qi(n,s){n&1&&_(0,Qi,0,0,"ng-template")}function ji(n,s){if(n&1&&(F(0),_(1,qi,1,0,null,13),$()),n&2){let e=a(3);l(),c("ngTemplateOutlet",e.decrementIconTemplate||e._decrementIconTemplate)}}function Zi(n,s){if(n&1&&(m(0,"div",61)(1,"span"),x(2),h()()),n&2){let e=a(3);l(2),L(e.timeSeparator)}}function Gi(n,s){n&1&&P(0,"ChevronUpIcon")}function Ji(n,s){}function Xi(n,s){n&1&&_(0,Ji,0,0,"ng-template")}function en(n,s){n&1&&(F(0),x(1,"0"),$())}function tn(n,s){n&1&&P(0,"ChevronDownIcon")}function nn(n,s){}function rn(n,s){n&1&&_(0,nn,0,0,"ng-template")}function an(n,s){if(n&1){let e=M();m(0,"div",66)(1,"p-button",60),C("keydown",function(i){d(e);let r=a(3);return p(r.onContainerButtonKeydown(i))})("keydown.enter",function(i){d(e);let r=a(3);return p(r.incrementSecond(i))})("keydown.space",function(i){d(e);let r=a(3);return p(r.incrementSecond(i))})("mousedown",function(i){d(e);let r=a(3);return p(r.onTimePickerElementMouseDown(i,2,1))})("mouseup",function(i){d(e);let r=a(3);return p(r.onTimePickerElementMouseUp(i))})("keyup.enter",function(i){d(e);let r=a(3);return p(r.onTimePickerElementMouseUp(i))})("keyup.space",function(i){d(e);let r=a(3);return p(r.onTimePickerElementMouseUp(i))})("mouseleave",function(){d(e);let i=a(3);return p(i.onTimePickerElementMouseLeave())}),_(2,Gi,1,0,"ChevronUpIcon",7)(3,Xi,1,0,null,13),h(),m(4,"span"),_(5,en,2,0,"ng-container",7),x(6),h(),m(7,"p-button",60),C("keydown",function(i){d(e);let r=a(3);return p(r.onContainerButtonKeydown(i))})("keydown.enter",function(i){d(e);let r=a(3);return p(r.decrementSecond(i))})("keydown.space",function(i){d(e);let r=a(3);return p(r.decrementSecond(i))})("mousedown",function(i){d(e);let r=a(3);return p(r.onTimePickerElementMouseDown(i,2,-1))})("mouseup",function(i){d(e);let r=a(3);return p(r.onTimePickerElementMouseUp(i))})("keyup.enter",function(i){d(e);let r=a(3);return p(r.onTimePickerElementMouseUp(i))})("keyup.space",function(i){d(e);let r=a(3);return p(r.onTimePickerElementMouseUp(i))})("mouseleave",function(){d(e);let i=a(3);return p(i.onTimePickerElementMouseLeave())}),_(8,tn,1,0,"ChevronDownIcon",7)(9,rn,1,0,null,13),h()()}if(n&2){let e=a(3);l(),S("aria-label",e.getTranslation("nextSecond")),l(),c("ngIf",!e.incrementIconTemplate&&!e._incrementIconTemplate),l(),c("ngTemplateOutlet",e.incrementIconTemplate||e._incrementIconTemplate),l(2),c("ngIf",e.currentSecond<10),l(),L(e.currentSecond),l(),S("aria-label",e.getTranslation("prevSecond")),l(),c("ngIf",!e.decrementIconTemplate&&!e._decrementIconTemplate),l(),c("ngTemplateOutlet",e.decrementIconTemplate||e._decrementIconTemplate)}}function on(n,s){if(n&1&&(m(0,"div",61)(1,"span"),x(2),h()()),n&2){let e=a(3);l(2),L(e.timeSeparator)}}function sn(n,s){n&1&&P(0,"ChevronUpIcon")}function ln(n,s){}function cn(n,s){n&1&&_(0,ln,0,0,"ng-template")}function dn(n,s){n&1&&P(0,"ChevronDownIcon")}function pn(n,s){}function un(n,s){n&1&&_(0,pn,0,0,"ng-template")}function _n(n,s){if(n&1){let e=M();m(0,"div",67)(1,"p-button",68),C("keydown",function(i){d(e);let r=a(3);return p(r.onContainerButtonKeydown(i))})("onClick",function(i){d(e);let r=a(3);return p(r.toggleAMPM(i))})("keydown.enter",function(i){d(e);let r=a(3);return p(r.toggleAMPM(i))}),_(2,sn,1,0,"ChevronUpIcon",7)(3,cn,1,0,null,13),h(),m(4,"span"),x(5),h(),m(6,"p-button",69),C("keydown",function(i){d(e);let r=a(3);return p(r.onContainerButtonKeydown(i))})("click",function(i){d(e);let r=a(3);return p(r.toggleAMPM(i))})("keydown.enter",function(i){d(e);let r=a(3);return p(r.toggleAMPM(i))}),_(7,dn,1,0,"ChevronDownIcon",7)(8,un,1,0,null,13),h()()}if(n&2){let e=a(3);l(),S("aria-label",e.getTranslation("am")),l(),c("ngIf",!e.incrementIconTemplate&&!e._incrementIconTemplate),l(),c("ngTemplateOutlet",e.incrementIconTemplate||e._incrementIconTemplate),l(2),L(e.pm?"PM":"AM"),l(),S("aria-label",e.getTranslation("pm")),l(),c("ngIf",!e.decrementIconTemplate&&!e._decrementIconTemplate),l(),c("ngTemplateOutlet",e.decrementIconTemplate||e._decrementIconTemplate)}}function hn(n,s){if(n&1){let e=M();m(0,"div",58)(1,"div",59)(2,"p-button",60),C("keydown",function(i){d(e);let r=a(2);return p(r.onContainerButtonKeydown(i))})("keydown.enter",function(i){d(e);let r=a(2);return p(r.incrementHour(i))})("keydown.space",function(i){d(e);let r=a(2);return p(r.incrementHour(i))})("mousedown",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseDown(i,0,1))})("mouseup",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("keyup.enter",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("keyup.space",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("mouseleave",function(){d(e);let i=a(2);return p(i.onTimePickerElementMouseLeave())}),_(3,Yi,1,0,"ChevronUpIcon",7)(4,Pi,1,0,null,13),h(),m(5,"span"),_(6,Ai,2,0,"ng-container",7),x(7),h(),m(8,"p-button",60),C("keydown",function(i){d(e);let r=a(2);return p(r.onContainerButtonKeydown(i))})("keydown.enter",function(i){d(e);let r=a(2);return p(r.decrementHour(i))})("keydown.space",function(i){d(e);let r=a(2);return p(r.decrementHour(i))})("mousedown",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseDown(i,0,-1))})("mouseup",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("keyup.enter",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("keyup.space",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("mouseleave",function(){d(e);let i=a(2);return p(i.onTimePickerElementMouseLeave())}),_(9,Li,1,0,"ChevronDownIcon",7)(10,Ri,1,0,null,13),h()(),m(11,"div",61)(12,"span"),x(13),h()(),m(14,"div",62)(15,"p-button",60),C("keydown",function(i){d(e);let r=a(2);return p(r.onContainerButtonKeydown(i))})("keydown.enter",function(i){d(e);let r=a(2);return p(r.incrementMinute(i))})("keydown.space",function(i){d(e);let r=a(2);return p(r.incrementMinute(i))})("mousedown",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseDown(i,1,1))})("mouseup",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("keyup.enter",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("keyup.space",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("mouseleave",function(){d(e);let i=a(2);return p(i.onTimePickerElementMouseLeave())}),_(16,Ni,1,0,"ChevronUpIcon",7)(17,Ki,1,0,null,13),h(),m(18,"span"),_(19,zi,2,0,"ng-container",7),x(20),h(),m(21,"p-button",60),C("keydown",function(i){d(e);let r=a(2);return p(r.onContainerButtonKeydown(i))})("keydown.enter",function(i){d(e);let r=a(2);return p(r.decrementMinute(i))})("keydown.space",function(i){d(e);let r=a(2);return p(r.decrementMinute(i))})("mousedown",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseDown(i,1,-1))})("mouseup",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("keyup.enter",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("keyup.space",function(i){d(e);let r=a(2);return p(r.onTimePickerElementMouseUp(i))})("mouseleave",function(){d(e);let i=a(2);return p(i.onTimePickerElementMouseLeave())}),_(22,Wi,1,0,"ChevronDownIcon",7)(23,ji,2,1,"ng-container",7),h()(),_(24,Zi,3,1,"div",63)(25,an,10,8,"div",64)(26,on,3,1,"div",63)(27,_n,9,7,"div",65),h()}if(n&2){let e=a(2);l(2),S("aria-label",e.getTranslation("nextHour")),l(),c("ngIf",!e.incrementIconTemplate&&!e._incrementIconTemplate),l(),c("ngTemplateOutlet",e.incrementIconTemplate||e._incrementIconTemplate),l(2),c("ngIf",e.currentHour<10),l(),L(e.currentHour),l(),S("aria-label",e.getTranslation("prevHour")),l(),c("ngIf",!e.decrementIconTemplate&&!e._decrementIconTemplate),l(),c("ngTemplateOutlet",e.decrementIconTemplate||e._decrementIconTemplate),l(3),L(e.timeSeparator),l(2),S("aria-label",e.getTranslation("nextMinute")),l(),c("ngIf",!e.incrementIconTemplate&&!e._incrementIconTemplate),l(),c("ngTemplateOutlet",e.incrementIconTemplate||e._incrementIconTemplate),l(2),c("ngIf",e.currentMinute<10),l(),L(e.currentMinute),l(),S("aria-label",e.getTranslation("prevMinute")),l(),c("ngIf",!e.decrementIconTemplate&&!e._decrementIconTemplate),l(),c("ngIf",e.decrementIconTemplate||e._decrementIconTemplate),l(),c("ngIf",e.showSeconds),l(),c("ngIf",e.showSeconds),l(),c("ngIf",e.hourFormat=="12"),l(),c("ngIf",e.hourFormat=="12")}}function mn(n,s){if(n&1){let e=M();m(0,"div",70)(1,"p-button",71),C("keydown",function(i){d(e);let r=a(2);return p(r.onContainerButtonKeydown(i))})("onClick",function(i){d(e);let r=a(2);return p(r.onTodayButtonClick(i))}),h(),m(2,"p-button",72),C("keydown",function(i){d(e);let r=a(2);return p(r.onContainerButtonKeydown(i))})("onClick",function(i){d(e);let r=a(2);return p(r.onClearButtonClick(i))}),h()()}if(n&2){let e=a(2);l(),c("label",e.getTranslation("today"))("ngClass",B(4,ut,e.todayButtonStyleClass)),l(),c("label",e.getTranslation("clear"))("ngClass",B(6,ut,e.clearButtonStyleClass))}}function fn(n,s){n&1&&Q(0)}function gn(n,s){if(n&1){let e=M();m(0,"div",21,2),C("@overlayAnimation.start",function(i){d(e);let r=a();return p(r.onOverlayAnimationStart(i))})("@overlayAnimation.done",function(i){d(e);let r=a();return p(r.onOverlayAnimationDone(i))})("click",function(i){d(e);let r=a();return p(r.onOverlayClick(i))}),_e(2),_(3,ei,1,0,"ng-container",13)(4,$i,5,3,"ng-container",7)(5,hn,28,21,"div",22)(6,mn,3,8,"div",23),_e(7,1),_(8,fn,1,0,"ng-container",13),h()}if(n&2){let e=a();X(e.panelStyleClass),c("ngStyle",e.panelStyle)("ngClass",e.panelClass)("@overlayAnimation",B(18,Yt,ee(15,$t,e.showTransitionOptions,e.hideTransitionOptions)))("@.disabled",e.inline===!0),S("id",e.panelId)("aria-label",e.getTranslation("chooseDate"))("role",e.inline?null:"dialog")("aria-modal",e.inline?null:"true"),l(3),c("ngTemplateOutlet",e.headerTemplate||e._headerTemplate),l(),c("ngIf",!e.timeOnly),l(),c("ngIf",(e.showTime||e.timeOnly)&&e.currentView==="date"),l(),c("ngIf",e.showButtonBar),l(2),c("ngTemplateOutlet",e.footerTemplate||e._footerTemplate)}}var vn=({dt:n})=>`
.p-datepicker {
position: relative;
    display: inline-flex;
    max-width: 100%;
}

.p-datepicker-input {
    flex: 1 1 auto;
    width: 1%;
}

.p-datepicker:has(.p-datepicker-dropdown) .p-datepicker-input {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
}

.p-datepicker-dropdown {
    cursor: pointer;
    display: inline-flex;
    cursor: pointer;
    user-select: none;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    width: ${n("datepicker.dropdown.width")};
    border-start-end-radius: ${n("datepicker.dropdown.border.radius")};
    border-end-end-radius: ${n("datepicker.dropdown.border.radius")};
    background: ${n("datepicker.dropdown.background")};
    border: 1px solid ${n("datepicker.dropdown.border.color")};
    border-left: 0 none;
    color: ${n("datepicker.dropdown.color")};
    transition: background ${n("datepicker.transition.duration")}, color ${n("datepicker.transition.duration")}, border-color ${n("datepicker.transition.duration")}, outline-color ${n("datepicker.transition.duration")};
    outline-color: transparent;
}

.p-datepicker-dropdown:not(:disabled):hover {
    background: ${n("datepicker.dropdown.hover.background")};
    border-color: ${n("datepicker.dropdown.hover.border.color")};
    color: ${n("datepicker.dropdown.hover.color")};
}

.p-datepicker-dropdown:not(:disabled):active {
    background: ${n("datepicker.dropdown.active.background")};
    border-color: ${n("datepicker.dropdown.active.border.color")};
    color: ${n("datepicker.dropdown.active.color")};
}

.p-datepicker-dropdown:focus-visible {
    box-shadow: ${n("datepicker.dropdown.focus.ring.shadow")};
    outline: ${n("datepicker.dropdown.focus.ring.width")} ${n("datepicker.dropdown.focus.ring.style")} ${n("datepicker.dropdown.focus.ring.color")};
    outline-offset: ${n("datepicker.dropdown.focus.ring.offset")};
}

.p-datepicker:has(.p-datepicker-input-icon-container) {
    position: relative;
}

.p-datepicker:has(.p-datepicker-input-icon-container) .p-datepicker-input {
    padding-right: calc((${n("form.field.padding.x")} * 2) + ${n("icon.size")});
}

.p-datepicker-input-icon-container {
    cursor: pointer;
    position: absolute;
    top: 50%;
    right: ${n("form.field.padding.x")};
    margin-top: calc(-1 * (${n("icon.size")} / 2));
    color: ${n("datepicker.input.icon.color")};
}

.p-datepicker-fluid {
    display: flex;
}

.p-datepicker-fluid .p-datepicker-input {
    width: 1%;
}

.p-datepicker .p-datepicker-panel {
    min-width: 100%;
}

.p-datepicker-panel {
    position: absolute;
    width: auto;
    padding: ${n("datepicker.panel.padding")};
    background: ${n("datepicker.panel.background")};
    color: ${n("datepicker.panel.color")};
    border: 1px solid ${n("datepicker.panel.border.color")};
    border-radius: ${n("datepicker.panel.border.radius")};
    box-shadow: ${n("datepicker.panel.shadow")};
}

.p-datepicker-panel-inline {
    display: inline-block;
    overflow-x: auto;
    box-shadow: none;
}

.p-datepicker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: ${n("datepicker.header.padding")};
    font-weight: ${n("datepicker.header.font.weight")};
    background: ${n("datepicker.header.background")};
    color: ${n("datepicker.header.color")};
    border-bottom: 1px solid ${n("datepicker.header.border.color")};
}

.p-datepicker-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: ${n("datepicker.title.gap")};
    font-weight: ${n("datepicker.title.font.weight")};
}

.p-datepicker-select-year,
.p-datepicker-select-month {
    border: none;
    background: transparent;
    margin: 0;
    cursor: pointer;
    font-weight: inherit;
    transition: background ${n("datepicker.transition.duration")}, color ${n("datepicker.transition.duration")}, border-color ${n("datepicker.transition.duration")}, outline-color ${n("datepicker.transition.duration")}, box-shadow ${n("datepicker.transition.duration")};
}

.p-datepicker-select-month {
    padding: ${n("datepicker.select.month.padding")};
    color: ${n("datepicker.select.month.color")};
    border-radius: ${n("datepicker.select.month.border.radius")};
}

.p-datepicker-select-year {
    padding: ${n("datepicker.select.year.padding")};
    color: ${n("datepicker.select.year.color")};
    border-radius: ${n("datepicker.select.year.border.radius")};
}

.p-datepicker-select-month:enabled:hover {
    background: ${n("datepicker.select.month.hover.background")};
    color: ${n("datepicker.select.month.hover.color")};
}

.p-datepicker-select-year:enabled:hover {
    background: ${n("datepicker.select.year.hover.background")};
    color: ${n("datepicker.select.year.hover.color")};
}

.p-datepicker-calendar-container {
    display: flex;
}

.p-datepicker-calendar-container .p-datepicker-calendar {
    flex: 1 1 auto;
    border-left: 1px solid ${n("datepicker.group.border.color")};
    padding-right: ${n("datepicker.group.gap")};
    padding-left: ${n("datepicker.group.gap")};
}

.p-datepicker-calendar-container .p-datepicker-calendar:first-child {
    padding-left: 0;
    border-left: 0 none;
}

.p-datepicker-calendar-container .p-datepicker-calendar:last-child {
    padding-right: 0;
}

.p-datepicker-day-view {
    width: 100%;
    border-collapse: collapse;
    font-size: 1rem;
    margin: ${n("datepicker.day.view.margin")};
}

.p-datepicker-weekday-cell {
    padding: ${n("datepicker.week.day.padding")};
}

.p-datepicker-weekday {
    font-weight: ${n("datepicker.week.day.font.weight")};
    color: ${n("datepicker.week.day.color")};
}

.p-datepicker-day-cell {
    padding: ${n("datepicker.date.padding")};
}

.p-datepicker-day {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
    width: ${n("datepicker.date.width")};
    height: ${n("datepicker.date.height")};
    border-radius: ${n("datepicker.date.border.radius")};
    transition: background ${n("datepicker.transition.duration")}, color ${n("datepicker.transition.duration")}, border-color ${n("datepicker.transition.duration")},
        box-shadow ${n("datepicker.transition.duration")}, outline-color ${n("datepicker.transition.duration")};
    border: 1px solid transparent;
    outline-color: transparent;
    color: ${n("datepicker.date.color")};
}

.p-datepicker-day:not(.p-datepicker-day-selected):not(.p-disabled):hover {
    background: ${n("datepicker.date.hover.background")};
    color: ${n("datepicker.date.hover.color")};
}

.p-datepicker-day:focus-visible {
    box-shadow: ${n("datepicker.date.focus.ring.shadow")};
    outline: ${n("datepicker.date.focus.ring.width")} ${n("datepicker.date.focus.ring.style")} ${n("datepicker.date.focus.ring.color")};
    outline-offset: ${n("datepicker.date.focus.ring.offset")};
}

.p-datepicker-day-selected {
    background: ${n("datepicker.date.selected.background")};
    color: ${n("datepicker.date.selected.color")};
}

.p-datepicker-day-selected-range {
    background: ${n("datepicker.date.range.selected.background")};
    color: ${n("datepicker.date.range.selected.color")};
}

.p-datepicker-today > .p-datepicker-day {
    background: ${n("datepicker.today.background")};
    color: ${n("datepicker.today.color")};
}

.p-datepicker-today > .p-datepicker-day-selected {
    background: ${n("datepicker.date.selected.background")};
    color: ${n("datepicker.date.selected.color")};
}

.p-datepicker-today > .p-datepicker-day-selected-range {
    background: ${n("datepicker.date.range.selected.background")};
    color: ${n("datepicker.date.range.selected.color")};
}

.p-datepicker-weeknumber {
    text-align: center
}

.p-datepicker-month-view {
    margin: ${n("datepicker.month.view.margin")};
}

.p-datepicker-month {
    width: 33.3%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    padding: ${n("datepicker.month.padding")};
    transition: background ${n("datepicker.transition.duration")}, color ${n("datepicker.transition.duration")}, border-color ${n("datepicker.transition.duration")}, box-shadow ${n("datepicker.transition.duration")}, outline-color ${n("datepicker.transition.duration")};
    border-radius: ${n("datepicker.month.border.radius")};
    outline-color: transparent;
    color: ${n("datepicker.date.color")};
}

.p-datepicker-month:not(.p-disabled):not(.p-datepicker-month-selected):hover {
    color:  ${n("datepicker.date.hover.color")};
    background: ${n("datepicker.date.hover.background")};
}

.p-datepicker-month-selected {
    color: ${n("datepicker.date.selected.color")};
    background: ${n("datepicker.date.selected.background")};
}

.p-datepicker-month:not(.p-disabled):focus-visible {
    box-shadow: ${n("datepicker.date.focus.ring.shadow")};
    outline: ${n("datepicker.date.focus.ring.width")} ${n("datepicker.date.focus.ring.style")} ${n("datepicker.date.focus.ring.color")};
    outline-offset: ${n("datepicker.date.focus.ring.offset")};
}

.p-datepicker-year-view {
    margin: ${n("datepicker.year.view.margin")};
}

.p-datepicker-year {
    width: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    padding: ${n("datepicker.year.padding")};
    transition: background ${n("datepicker.transition.duration")}, color ${n("datepicker.transition.duration")}, border-color ${n("datepicker.transition.duration")}, box-shadow ${n("datepicker.transition.duration")}, outline-color ${n("datepicker.transition.duration")};
    border-radius: ${n("datepicker.year.border.radius")};
    outline-color: transparent;
    color: ${n("datepicker.date.color")};
}

.p-datepicker-year:not(.p-disabled):not(.p-datepicker-year-selected):hover {
    color: ${n("datepicker.date.hover.color")};
    background: ${n("datepicker.date.hover.background")};
}

.p-datepicker-year-selected {
    color: ${n("datepicker.date.selected.color")};
    background: ${n("datepicker.date.selected.background")};
}

.p-datepicker-year:not(.p-disabled):focus-visible {
    box-shadow: ${n("datepicker.date.focus.ring.shadow")};
    outline: ${n("datepicker.date.focus.ring.width")} ${n("datepicker.date.focus.ring.style")} ${n("datepicker.date.focus.ring.color")};
    outline-offset: ${n("datepicker.date.focus.ring.offset")};
}

.p-datepicker-buttonbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:  ${n("datepicker.buttonbar.padding")};
    border-top: 1px solid ${n("datepicker.buttonbar.border.color")};
}

.p-datepicker-buttonbar .p-button {
    width: auto;
}

.p-datepicker-time-picker {
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 1px solid ${n("datepicker.time.picker.border.color")};
    padding: 0;
    gap: ${n("datepicker.time.picker.gap")};
}

.p-datepicker-calendar-container + .p-datepicker-time-picker {
    padding: ${n("datepicker.time.picker.padding")};
}

.p-datepicker-time-picker > div {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: ${n("datepicker.time.picker.button.gap")};
}

.p-datepicker-time-picker span {
    font-size: 1rem;
}

.p-datepicker-timeonly .p-datepicker-time-picker {
    border-top: 0 none;
}

.p-datepicker-calendar:not(:first-child):not(:last-child) .p-datepicker-header {
    justify-content: center;
}

/* For PrimeNG */

p-calendar.ng-invalid.ng-dirty .p-datepicker.p-inputwrapper .p-inputtext{
    border-color: ${n("inputtext.invalid.border.color")};
}

p-datePicker.ng-invalid.ng-dirty .p-datepicker.p-inputwrapper .p-inputtext,
p-date-picker.ng-invalid.ng-dirty .p-datepicker.p-inputwrapper .p-inputtext,
p-datepicker.ng-invalid.ng-dirty .p-datepicker.p-inputwrapper .p-inputtext {
    border-color: ${n("inputtext.invalid.border.color")};
}
`,kn={root:({props:n})=>({position:n.appendTo==="self"?"relative":void 0})},yn={root:({instance:n})=>({"p-datepicker p-component p-inputwrapper":!0,"p-datepicker-fluid":n.hasFluid,"p-inputwrapper-filled":n.filled,"p-inputwrapper-focus":n.focus,"p-focus":n.focus||n.overlayVisible}),pcInput:"p-datepicker-input",dropdown:"p-datepicker-dropdown",inputIconContainer:"p-datepicker-input-icon-container",inputIcon:"p-datepicker-input-icon",panel:({instance:n})=>({"p-datepicker-panel p-component":!0,"p-datepicker-panel-inline":n.inline,"p-disabled":n.disabled,"p-datepicker-timeonly":n.timeOnly}),calendarContainer:"p-datepicker-calendar-container",calendar:"p-datepicker-calendar",header:"p-datepicker-header",pcPrevButton:"p-datepicker-prev-button",title:"p-datepicker-title",selectMonth:"p-datepicker-select-month",selectYear:"p-datepicker-select-year",decade:"p-datepicker-decade",pcNextButton:"p-datepicker-next-button",dayView:"p-datepicker-day-view",weekHeader:"p-datepicker-weekheader p-disabled",weekNumber:"p-datepicker-weeknumber",weekLabelContainer:"p-datepicker-weeklabel-container p-disabled",weekDayCell:"p-datepicker-weekday-cell",weekDay:"p-datepicker-weekday",dayCell:({date:n})=>["p-datepicker-day-cell",{"p-datepicker-other-month":n.otherMonth,"p-datepicker-today":n.today}],day:({instance:n,date:s})=>{let e="";return n.isRangeSelection()&&n.isSelected(s)&&s.selectable&&(e=s.day===n.value[0].getDate()||s.day===n.value[1].getDate()?"p-datepicker-day-selected":"p-datepicker-day-selected-range"),{"p-datepicker-day":!0,"p-datepicker-day-selected":!n.isRangeSelection()&&n.isSelected(s)&&s.selectable,"p-disabled":n.disabled||!s.selectable,[e]:!0}},monthView:"p-datepicker-month-view",month:({instance:n,props:s,month:e,index:t})=>["p-datepicker-month",{"p-datepicker-month-selected":n.isMonthSelected(t),"p-disabled":s.disabled||!e.selectable}],yearView:"p-datepicker-year-view",year:({instance:n,props:s,year:e})=>["p-datepicker-year",{"p-datepicker-year-selected":n.isYearSelected(e.value),"p-disabled":s.disabled||!e.selectable}],timePicker:"p-datepicker-time-picker",hourPicker:"p-datepicker-hour-picker",pcIncrementButton:"p-datepicker-increment-button",pcDecrementButton:"p-datepicker-decrement-button",separator:"p-datepicker-separator",minutePicker:"p-datepicker-minute-picker",secondPicker:"p-datepicker-second-picker",ampmPicker:"p-datepicker-ampm-picker",buttonbar:"p-datepicker-buttonbar",pcTodayButton:"p-datepicker-today-button",pcClearButton:"p-datepicker-clear-button"},_t=(()=>{class n extends Ge{name="datepicker";theme=vn;classes=yn;inlineStyles=kn;static \u0275fac=(()=>{let e;return function(i){return(e||(e=we(n)))(i||n)}})();static \u0275prov=ke({token:n,factory:n.\u0275fac})}return n})(),bn={provide:ot,useExisting:ve(()=>ht),multi:!0},ht=(()=>{class n extends Je{zone;overlayService;iconDisplay="button";style;styleClass;inputStyle;inputId;name;inputStyleClass;placeholder;ariaLabelledBy;ariaLabel;iconAriaLabel;disabled;dateFormat;multipleSeparator=",";rangeSeparator="-";inline=!1;showOtherMonths=!0;selectOtherMonths;showIcon;fluid;icon;appendTo;readonlyInput;shortYearCutoff="+10";monthNavigator;yearNavigator;hourFormat="24";timeOnly;stepHour=1;stepMinute=1;stepSecond=1;showSeconds=!1;required;showOnFocus=!0;showWeek=!1;startWeekFromFirstDayOfYear=!1;showClear=!1;dataType="date";selectionMode="single";maxDateCount;showButtonBar;todayButtonStyleClass;clearButtonStyleClass;autofocus;autoZIndex=!0;baseZIndex=0;panelStyleClass;panelStyle;keepInvalid=!1;hideOnDateTimeSelect=!0;touchUI;timeSeparator=":";focusTrap=!0;showTransitionOptions=".12s cubic-bezier(0, 0, 0.2, 1)";hideTransitionOptions=".1s linear";tabindex;variant;get minDate(){return this._minDate}set minDate(e){this._minDate=e,this.currentMonth!=null&&this.currentMonth!=null&&this.currentYear&&this.createMonths(this.currentMonth,this.currentYear)}get maxDate(){return this._maxDate}set maxDate(e){this._maxDate=e,this.currentMonth!=null&&this.currentMonth!=null&&this.currentYear&&this.createMonths(this.currentMonth,this.currentYear)}get disabledDates(){return this._disabledDates}set disabledDates(e){this._disabledDates=e,this.currentMonth!=null&&this.currentMonth!=null&&this.currentYear&&this.createMonths(this.currentMonth,this.currentYear)}get disabledDays(){return this._disabledDays}set disabledDays(e){this._disabledDays=e,this.currentMonth!=null&&this.currentMonth!=null&&this.currentYear&&this.createMonths(this.currentMonth,this.currentYear)}get yearRange(){return this._yearRange}set yearRange(e){if(this._yearRange=e,e){let t=e.split(":"),i=parseInt(t[0]),r=parseInt(t[1]);this.populateYearOptions(i,r)}}get showTime(){return this._showTime}set showTime(e){this._showTime=e,this.currentHour===void 0&&this.initTime(this.value||new Date),this.updateInputfield()}get responsiveOptions(){return this._responsiveOptions}set responsiveOptions(e){this._responsiveOptions=e,this.destroyResponsiveStyleElement(),this.createResponsiveStyle()}get numberOfMonths(){return this._numberOfMonths}set numberOfMonths(e){this._numberOfMonths=e,this.destroyResponsiveStyleElement(),this.createResponsiveStyle()}get firstDayOfWeek(){return this._firstDayOfWeek}set firstDayOfWeek(e){this._firstDayOfWeek=e,this.createWeekDays()}set locale(e){console.log("Locale property has no effect, use new i18n API instead.")}get view(){return this._view}set view(e){this._view=e,this.currentView=this._view}get defaultDate(){return this._defaultDate}set defaultDate(e){if(this._defaultDate=e,this.initialized){let t=e||new Date;this.currentMonth=t.getMonth(),this.currentYear=t.getFullYear(),this.initTime(t),this.createMonths(this.currentMonth,this.currentYear)}}onFocus=new H;onBlur=new H;onClose=new H;onSelect=new H;onClear=new H;onInput=new H;onTodayClick=new H;onClearClick=new H;onMonthChange=new H;onYearChange=new H;onClickOutside=new H;onShow=new H;dateTemplate;headerTemplate;footerTemplate;disabledDateTemplate;decadeTemplate;previousIconTemplate;nextIconTemplate;triggerIconTemplate;clearIconTemplate;decrementIconTemplate;incrementIconTemplate;inputIconTemplate;containerViewChild;inputfieldViewChild;set content(e){this.contentViewChild=e,this.contentViewChild&&(this.isMonthNavigate?(Promise.resolve(null).then(()=>this.updateFocus()),this.isMonthNavigate=!1):!this.focus&&!this.inline&&this.initFocusableCell())}_dateTemplate;_headerTemplate;_footerTemplate;_disabledDateTemplate;_decadeTemplate;_previousIconTemplate;_nextIconTemplate;_triggerIconTemplate;_clearIconTemplate;_decrementIconTemplate;_incrementIconTemplate;_inputIconTemplate;_componentStyle=be(_t);contentViewChild;value;dates;months;weekDays;currentMonth;currentYear;currentHour;currentMinute;currentSecond;pm;mask;maskClickListener;overlay;responsiveStyleElement;overlayVisible;onModelChange=()=>{};onModelTouched=()=>{};calendarElement;timePickerTimer;documentClickListener;animationEndListener;ticksTo1970;yearOptions;focus;isKeydown;filled;inputFieldValue=null;_minDate;_maxDate;_showTime;_yearRange;preventDocumentListener;dayClass(e){return this._componentStyle.classes.day({instance:this,date:e})}_disabledDates;_disabledDays;selectElement;todayElement;focusElement;scrollHandler;documentResizeListener;navigationState=null;isMonthNavigate;initialized;translationSubscription;_locale;_responsiveOptions;currentView;attributeSelector;panelId;_numberOfMonths=1;_firstDayOfWeek;_view="date";preventFocus;_defaultDate;_focusKey=null;window;get locale(){return this._locale}get iconButtonAriaLabel(){return this.iconAriaLabel?this.iconAriaLabel:this.getTranslation("chooseDate")}get prevIconAriaLabel(){return this.currentView==="year"?this.getTranslation("prevDecade"):this.currentView==="month"?this.getTranslation("prevYear"):this.getTranslation("prevMonth")}get nextIconAriaLabel(){return this.currentView==="year"?this.getTranslation("nextDecade"):this.currentView==="month"?this.getTranslation("nextYear"):this.getTranslation("nextMonth")}get rootClass(){return this._componentStyle.classes.root({instance:this})}get panelClass(){return this._componentStyle.classes.panel({instance:this})}get hasFluid(){let t=this.el.nativeElement.closest("p-fluid");return this.fluid||!!t}constructor(e,t){super(),this.zone=e,this.overlayService=t}ngOnInit(){console.log("Calendar component is deprecated as of v18, use DatePicker component instead."),super.ngOnInit(),this.attributeSelector=qe("pn_id_"),this.panelId=this.attributeSelector+"_panel";let e=this.defaultDate||new Date;this.createResponsiveStyle(),this.currentMonth=e.getMonth(),this.currentYear=e.getFullYear(),this.yearOptions=[],this.currentView=this.view,this.view==="date"&&(this.createWeekDays(),this.initTime(e),this.createMonths(this.currentMonth,this.currentYear),this.ticksTo1970=(1969*365+Math.floor(1970/4)-Math.floor(1970/100)+Math.floor(1970/400))*24*60*60*1e7),this.translationSubscription=this.config.translationObserver.subscribe(()=>{this.createWeekDays(),this.cd.markForCheck()}),this.initialized=!0}ngAfterViewInit(){super.ngAfterViewInit(),this.inline&&(this.contentViewChild&&this.contentViewChild.nativeElement.setAttribute(this.attributeSelector,""),!this.disabled&&!this.inline&&(this.initFocusableCell(),this.numberOfMonths===1&&this.contentViewChild&&this.contentViewChild.nativeElement&&(this.contentViewChild.nativeElement.style.width=ne(this.containerViewChild?.nativeElement)+"px")))}templates;ngAfterContentInit(){this.templates.forEach(e=>{switch(e.getType()){case"date":this._dateTemplate=e.template;break;case"decade":this._decadeTemplate=e.template;break;case"disabledDate":this._disabledDateTemplate=e.template;break;case"header":this._headerTemplate=e.template;break;case"inputicon":this._inputIconTemplate=e.template;break;case"previousicon":this._previousIconTemplate=e.template;break;case"nexticon":this._nextIconTemplate=e.template;break;case"triggericon":this._triggerIconTemplate=e.template;break;case"clearicon":this._clearIconTemplate=e.template;break;case"decrementicon":this._decrementIconTemplate=e.template;break;case"incrementicon":this._incrementIconTemplate=e.template;break;case"footer":this._footerTemplate=e.template;break;default:this._dateTemplate=e.template;break}})}getTranslation(e){return this.config.getTranslation(e)}populateYearOptions(e,t){this.yearOptions=[];for(let i=e;i<=t;i++)this.yearOptions.push(i)}createWeekDays(){this.weekDays=[];let e=this.getFirstDateOfWeek(),t=this.getTranslation(N.DAY_NAMES_MIN);for(let i=0;i<7;i++)this.weekDays.push(t[e]),e=e==6?0:++e}monthPickerValues(){let e=[];for(let t=0;t<=11;t++)e.push(this.config.getTranslation("monthNamesShort")[t]);return e}yearPickerValues(){let e=[],t=this.currentYear-this.currentYear%10;for(let i=0;i<10;i++)e.push(t+i);return e}createMonths(e,t){this.months=this.months=[];for(let i=0;i<this.numberOfMonths;i++){let r=e+i,o=t;r>11&&(r=r%11-1,o=t+1),this.months.push(this.createMonth(r,o))}}getWeekNumber(e){let t=new Date(e.getTime());if(this.startWeekFromFirstDayOfYear){let r=+this.getFirstDateOfWeek();t.setDate(t.getDate()+6+r-t.getDay())}else t.setDate(t.getDate()+4-(t.getDay()||7));let i=t.getTime();return t.setMonth(0),t.setDate(1),Math.floor(Math.round((i-t.getTime())/864e5)/7)+1}createMonth(e,t){let i=[],r=this.getFirstDayOfMonthIndex(e,t),o=this.getDaysCountInMonth(e,t),u=this.getDaysCountInPrevMonth(e,t),k=1,v=new Date,y=[],b=Math.ceil((o+r)/7);for(let O=0;O<b;O++){let w=[];if(O==0){for(let g=u-r+1;g<=u;g++){let D=this.getPreviousMonthAndYear(e,t);w.push({day:g,month:D.month,year:D.year,otherMonth:!0,today:this.isToday(v,g,D.month,D.year),selectable:this.isSelectable(g,D.month,D.year,!0)})}let f=7-w.length;for(let g=0;g<f;g++)w.push({day:k,month:e,year:t,today:this.isToday(v,k,e,t),selectable:this.isSelectable(k,e,t,!1)}),k++}else for(let f=0;f<7;f++){if(k>o){let g=this.getNextMonthAndYear(e,t);w.push({day:k-o,month:g.month,year:g.year,otherMonth:!0,today:this.isToday(v,k-o,g.month,g.year),selectable:this.isSelectable(k-o,g.month,g.year,!0)})}else w.push({day:k,month:e,year:t,today:this.isToday(v,k,e,t),selectable:this.isSelectable(k,e,t,!1)});k++}this.showWeek&&y.push(this.getWeekNumber(new Date(w[0].year,w[0].month,w[0].day))),i.push(w)}return{month:e,year:t,dates:i,weekNumbers:y}}initTime(e){this.pm=e.getHours()>11,this.showTime?(this.currentMinute=e.getMinutes(),this.currentSecond=e.getSeconds(),this.setCurrentHourPM(e.getHours())):this.timeOnly&&(this.currentMinute=0,this.currentHour=0,this.currentSecond=0)}navBackward(e){if(this.disabled){e.preventDefault();return}this.isMonthNavigate=!0,this.currentView==="month"?(this.decrementYear(),setTimeout(()=>{this.updateFocus()},1)):this.currentView==="year"?(this.decrementDecade(),setTimeout(()=>{this.updateFocus()},1)):(this.currentMonth===0?(this.currentMonth=11,this.decrementYear()):this.currentMonth--,this.onMonthChange.emit({month:this.currentMonth+1,year:this.currentYear}),this.createMonths(this.currentMonth,this.currentYear))}navForward(e){if(this.disabled){e.preventDefault();return}this.isMonthNavigate=!0,this.currentView==="month"?(this.incrementYear(),setTimeout(()=>{this.updateFocus()},1)):this.currentView==="year"?(this.incrementDecade(),setTimeout(()=>{this.updateFocus()},1)):(this.currentMonth===11?(this.currentMonth=0,this.incrementYear()):this.currentMonth++,this.onMonthChange.emit({month:this.currentMonth+1,year:this.currentYear}),this.createMonths(this.currentMonth,this.currentYear))}decrementYear(){this.currentYear--;let e=this.yearOptions;if(this.yearNavigator&&this.currentYear<e[0]){let t=e[e.length-1]-e[0];this.populateYearOptions(e[0]-t,e[e.length-1]-t)}}decrementDecade(){this.currentYear=this.currentYear-10}incrementDecade(){this.currentYear=this.currentYear+10}incrementYear(){this.currentYear++;let e=this.yearOptions;if(this.yearNavigator&&this.currentYear>e[e.length-1]){let t=e[e.length-1]-e[0];this.populateYearOptions(e[0]+t,e[e.length-1]+t)}}switchToMonthView(e){this.setCurrentView("month"),e.preventDefault()}switchToYearView(e){this.setCurrentView("year"),e.preventDefault()}onDateSelect(e,t){if(this.disabled||!t.selectable){e.preventDefault();return}this.isMultipleSelection()&&this.isSelected(t)?(this.value=this.value.filter((i,r)=>!this.isDateEquals(i,t)),this.value.length===0&&(this.value=null),this.updateModel(this.value)):this.shouldSelectDate(t)&&this.selectDate(t),(this.isSingleSelection()&&this.hideOnDateTimeSelect||this.isRangeSelection()&&this.value[1])&&setTimeout(()=>{e.preventDefault(),this.hideOverlay(),this.mask&&this.disableModality(),this.cd.markForCheck()},150),this.updateInputfield(),e.preventDefault()}shouldSelectDate(e){return this.isMultipleSelection()&&this.maxDateCount!=null?this.maxDateCount>(this.value?this.value.length:0):!0}onMonthSelect(e,t){this.view==="month"?this.onDateSelect(e,{year:this.currentYear,month:t,day:1,selectable:!0}):(this.currentMonth=t,this.createMonths(this.currentMonth,this.currentYear),this.setCurrentView("date"),this.onMonthChange.emit({month:this.currentMonth+1,year:this.currentYear}))}onYearSelect(e,t){this.view==="year"?this.onDateSelect(e,{year:t,month:0,day:1,selectable:!0}):(this.currentYear=t,this.setCurrentView("month"),this.onYearChange.emit({month:this.currentMonth+1,year:this.currentYear}))}updateInputfield(){let e="";if(this.value){if(this.isSingleSelection())e=this.formatDateTime(this.value);else if(this.isMultipleSelection())for(let t=0;t<this.value.length;t++){let i=this.formatDateTime(this.value[t]);e+=i,t!==this.value.length-1&&(e+=this.multipleSeparator+" ")}else if(this.isRangeSelection()&&this.value&&this.value.length){let t=this.value[0],i=this.value[1];e=this.formatDateTime(t),i&&(e+=" "+this.rangeSeparator+" "+this.formatDateTime(i))}}this.inputFieldValue=e,this.updateFilledState(),this.inputfieldViewChild&&this.inputfieldViewChild.nativeElement&&(this.inputfieldViewChild.nativeElement.value=this.inputFieldValue)}formatDateTime(e){let t=this.keepInvalid?e:null,i=this.isValidDateForTimeConstraints(e);return this.isValidDate(e)?this.timeOnly?t=this.formatTime(e):(t=this.formatDate(e,this.getDateFormat()),this.showTime&&(t+=" "+this.formatTime(e))):this.dataType==="string"&&(t=e),t=i?t:"",t}formatDateMetaToDate(e){return new Date(e.year,e.month,e.day)}formatDateKey(e){return`${e.getFullYear()}-${e.getMonth()}-${e.getDate()}`}setCurrentHourPM(e){this.hourFormat=="12"?(this.pm=e>11,e>=12?this.currentHour=e==12?12:e-12:this.currentHour=e==0?12:e):this.currentHour=e}setCurrentView(e){this.currentView=e,this.cd.detectChanges(),this.alignOverlay()}selectDate(e){let t=this.formatDateMetaToDate(e);if(this.showTime&&(this.hourFormat=="12"?this.currentHour===12?t.setHours(this.pm?12:0):t.setHours(this.pm?this.currentHour+12:this.currentHour):t.setHours(this.currentHour),t.setMinutes(this.currentMinute),t.setSeconds(this.currentSecond)),this.minDate&&this.minDate>t&&(t=this.minDate,this.setCurrentHourPM(t.getHours()),this.currentMinute=t.getMinutes(),this.currentSecond=t.getSeconds()),this.maxDate&&this.maxDate<t&&(t=this.maxDate,this.setCurrentHourPM(t.getHours()),this.currentMinute=t.getMinutes(),this.currentSecond=t.getSeconds()),this.isSingleSelection())this.updateModel(t);else if(this.isMultipleSelection())this.updateModel(this.value?[...this.value,t]:[t]);else if(this.isRangeSelection())if(this.value&&this.value.length){let i=this.value[0],r=this.value[1];!r&&t.getTime()>=i.getTime()?r=t:(i=t,r=null),this.updateModel([i,r])}else this.updateModel([t,null]);this.onSelect.emit(t)}updateModel(e){if(this.value=e,this.dataType=="date")this.onModelChange(this.value);else if(this.dataType=="string")if(this.isSingleSelection())this.onModelChange(this.formatDateTime(this.value));else{let t=null;Array.isArray(this.value)&&(t=this.value.map(i=>this.formatDateTime(i))),this.onModelChange(t)}}getFirstDayOfMonthIndex(e,t){let i=new Date;i.setDate(1),i.setMonth(e),i.setFullYear(t);let r=i.getDay()+this.getSundayIndex();return r>=7?r-7:r}getDaysCountInMonth(e,t){return 32-this.daylightSavingAdjust(new Date(t,e,32)).getDate()}getDaysCountInPrevMonth(e,t){let i=this.getPreviousMonthAndYear(e,t);return this.getDaysCountInMonth(i.month,i.year)}getPreviousMonthAndYear(e,t){let i,r;return e===0?(i=11,r=t-1):(i=e-1,r=t),{month:i,year:r}}getNextMonthAndYear(e,t){let i,r;return e===11?(i=0,r=t+1):(i=e+1,r=t),{month:i,year:r}}getSundayIndex(){let e=this.getFirstDateOfWeek();return e>0?7-e:0}isSelected(e){if(this.value){if(this.isSingleSelection())return this.isDateEquals(this.value,e);if(this.isMultipleSelection()){let t=!1;for(let i of this.value)if(t=this.isDateEquals(i,e),t)break;return t}else if(this.isRangeSelection())return this.value[1]?this.isDateEquals(this.value[0],e)||this.isDateEquals(this.value[1],e)||this.isDateBetween(this.value[0],this.value[1],e):this.isDateEquals(this.value[0],e)}else return!1}isComparable(){return this.value!=null&&typeof this.value!="string"}isMonthSelected(e){if(this.isComparable()&&!this.isMultipleSelection()){let[t,i]=this.isRangeSelection()?this.value:[this.value,this.value],r=new Date(this.currentYear,e,1);return r>=t&&r<=(i??t)}return!1}isMonthDisabled(e,t){let i=t??this.currentYear;for(let r=1;r<this.getDaysCountInMonth(e,i)+1;r++)if(this.isSelectable(r,e,i,!1))return!1;return!0}isYearDisabled(e){return Array(12).fill(0).every((t,i)=>this.isMonthDisabled(i,e))}isYearSelected(e){if(this.isComparable()){let t=this.isRangeSelection()?this.value[0]:this.value;return this.isMultipleSelection()?!1:t.getFullYear()===e}return!1}isDateEquals(e,t){return e&&ae(e)?e.getDate()===t.day&&e.getMonth()===t.month&&e.getFullYear()===t.year:!1}isDateBetween(e,t,i){let r=!1;if(ae(e)&&ae(t)){let o=this.formatDateMetaToDate(i);return e.getTime()<=o.getTime()&&t.getTime()>=o.getTime()}return r}isSingleSelection(){return this.selectionMode==="single"}isRangeSelection(){return this.selectionMode==="range"}isMultipleSelection(){return this.selectionMode==="multiple"}isToday(e,t,i,r){return e.getDate()===t&&e.getMonth()===i&&e.getFullYear()===r}isSelectable(e,t,i,r){let o=!0,u=!0,k=!0,v=!0;return r&&!this.selectOtherMonths?!1:(this.minDate&&(this.minDate.getFullYear()>i||this.minDate.getFullYear()===i&&this.currentView!="year"&&(this.minDate.getMonth()>t||this.minDate.getMonth()===t&&this.minDate.getDate()>e))&&(o=!1),this.maxDate&&(this.maxDate.getFullYear()<i||this.maxDate.getFullYear()===i&&(this.maxDate.getMonth()<t||this.maxDate.getMonth()===t&&this.maxDate.getDate()<e))&&(u=!1),this.disabledDates&&(k=!this.isDateDisabled(e,t,i)),this.disabledDays&&(v=!this.isDayDisabled(e,t,i)),o&&u&&k&&v)}isDateDisabled(e,t,i){if(this.disabledDates){for(let r of this.disabledDates)if(r.getFullYear()===i&&r.getMonth()===t&&r.getDate()===e)return!0}return!1}isDayDisabled(e,t,i){if(this.disabledDays){let o=new Date(i,t,e).getDay();return this.disabledDays.indexOf(o)!==-1}return!1}onInputFocus(e){this.focus=!0,this.showOnFocus&&this.showOverlay(),this.onFocus.emit(e)}onInputClick(){this.showOnFocus&&!this.overlayVisible&&this.showOverlay()}onInputBlur(e){this.focus=!1,this.onBlur.emit(e),this.keepInvalid||this.updateInputfield(),this.onModelTouched()}onButtonClick(e,t=this.inputfieldViewChild?.nativeElement){this.overlayVisible?this.hideOverlay():(t.focus(),this.showOverlay())}clear(){this.inputFieldValue=null,this.value=null,this.onModelChange(this.value),this.onClear.emit()}onOverlayClick(e){this.overlayService.add({originalEvent:e,target:this.el.nativeElement})}getMonthName(e){return this.config.getTranslation("monthNames")[e]}getYear(e){return this.currentView==="month"?this.currentYear:e.year}switchViewButtonDisabled(){return this.numberOfMonths>1||this.disabled}onPrevButtonClick(e){this.navigationState={backward:!0,button:!0},this.navBackward(e)}onNextButtonClick(e){this.navigationState={backward:!1,button:!0},this.navForward(e)}onContainerButtonKeydown(e){switch(e.which){case 9:if(this.inline||this.trapFocus(e),this.inline){let t=E(this.containerViewChild?.nativeElement,".p-datepicker-header"),i=e.target;if(this.timeOnly)return;i==t.children[t?.children?.length-1]&&this.initFocusableCell()}break;case 27:this.inputfieldViewChild?.nativeElement.focus(),this.overlayVisible=!1,e.preventDefault();break;default:break}}onInputKeydown(e){this.isKeydown=!0,e.keyCode===40&&this.contentViewChild?this.trapFocus(e):e.keyCode===27?this.overlayVisible&&(this.inputfieldViewChild?.nativeElement.focus(),this.overlayVisible=!1,e.preventDefault()):e.keyCode===13?this.overlayVisible&&(this.overlayVisible=!1,e.preventDefault()):e.keyCode===9&&this.contentViewChild&&(me(this.contentViewChild.nativeElement).forEach(t=>t.tabIndex="-1"),this.overlayVisible&&(this.overlayVisible=!1))}onDateCellKeydown(e,t,i){let r=e.currentTarget,o=r.parentElement,u=this.formatDateMetaToDate(t);switch(e.which){case 40:{r.tabIndex="-1";let f=re(o),g=o.parentElement.nextElementSibling;if(g){let D=g.children[f].children[0];R(D,"p-disabled")?(this.navigationState={backward:!1},this.navForward(e)):(g.children[f].children[0].tabIndex="0",g.children[f].children[0].focus())}else this.navigationState={backward:!1},this.navForward(e);e.preventDefault();break}case 38:{r.tabIndex="-1";let f=re(o),g=o.parentElement.previousElementSibling;if(g){let D=g.children[f].children[0];R(D,"p-disabled")?(this.navigationState={backward:!0},this.navBackward(e)):(D.tabIndex="0",D.focus())}else this.navigationState={backward:!0},this.navBackward(e);e.preventDefault();break}case 37:{r.tabIndex="-1";let f=o.previousElementSibling;if(f){let g=f.children[0];R(g,"p-disabled")||R(g.parentElement,"p-datepicker-weeknumber")?this.navigateToMonth(!0,i):(g.tabIndex="0",g.focus())}else this.navigateToMonth(!0,i);e.preventDefault();break}case 39:{r.tabIndex="-1";let f=o.nextElementSibling;if(f){let g=f.children[0];R(g,"p-disabled")?this.navigateToMonth(!1,i):(g.tabIndex="0",g.focus())}else this.navigateToMonth(!1,i);e.preventDefault();break}case 13:case 32:{this.onDateSelect(e,t),e.preventDefault();break}case 27:{this.inputfieldViewChild?.nativeElement.focus(),this.overlayVisible=!1,e.preventDefault();break}case 9:{this.inline||this.trapFocus(e);break}case 33:{r.tabIndex="-1";let f=new Date(u.getFullYear(),u.getMonth()-1,u.getDate()),g=this.formatDateKey(f);this.navigateToMonth(!0,i,`span[data-date='${g}']:not(.p-disabled):not(.p-ink)`),e.preventDefault();break}case 34:{r.tabIndex="-1";let f=new Date(u.getFullYear(),u.getMonth()+1,u.getDate()),g=this.formatDateKey(f);this.navigateToMonth(!1,i,`span[data-date='${g}']:not(.p-disabled):not(.p-ink)`),e.preventDefault();break}case 36:r.tabIndex="-1";let k=new Date(u.getFullYear(),u.getMonth(),1),v=this.formatDateKey(k),y=E(r.offsetParent,`span[data-date='${v}']:not(.p-disabled):not(.p-ink)`);y&&(y.tabIndex="0",y.focus()),e.preventDefault();break;case 35:r.tabIndex="-1";let b=new Date(u.getFullYear(),u.getMonth()+1,0),O=this.formatDateKey(b),w=E(r.offsetParent,`span[data-date='${O}']:not(.p-disabled):not(.p-ink)`);b&&(w.tabIndex="0",w.focus()),e.preventDefault();break;default:break}}onMonthCellKeydown(e,t){let i=e.currentTarget;switch(e.which){case 38:case 40:{i.tabIndex="-1";var r=i.parentElement.children,o=re(i);let u=r[e.which===40?o+3:o-3];u&&(u.tabIndex="0",u.focus()),e.preventDefault();break}case 37:{i.tabIndex="-1";let u=i.previousElementSibling;u?(u.tabIndex="0",u.focus()):(this.navigationState={backward:!0},this.navBackward(e)),e.preventDefault();break}case 39:{i.tabIndex="-1";let u=i.nextElementSibling;u?(u.tabIndex="0",u.focus()):(this.navigationState={backward:!1},this.navForward(e)),e.preventDefault();break}case 13:case 32:{this.onMonthSelect(e,t),e.preventDefault();break}case 27:{this.inputfieldViewChild?.nativeElement.focus(),this.overlayVisible=!1,e.preventDefault();break}case 9:{this.inline||this.trapFocus(e);break}default:break}}onYearCellKeydown(e,t){let i=e.currentTarget;switch(e.which){case 38:case 40:{i.tabIndex="-1";var r=i.parentElement.children,o=re(i);let u=r[e.which===40?o+2:o-2];u&&(u.tabIndex="0",u.focus()),e.preventDefault();break}case 37:{i.tabIndex="-1";let u=i.previousElementSibling;u?(u.tabIndex="0",u.focus()):(this.navigationState={backward:!0},this.navBackward(e)),e.preventDefault();break}case 39:{i.tabIndex="-1";let u=i.nextElementSibling;u?(u.tabIndex="0",u.focus()):(this.navigationState={backward:!1},this.navForward(e)),e.preventDefault();break}case 13:case 32:{this.onYearSelect(e,t),e.preventDefault();break}case 27:{this.inputfieldViewChild?.nativeElement.focus(),this.overlayVisible=!1,e.preventDefault();break}case 9:{this.trapFocus(e);break}default:break}}navigateToMonth(e,t,i){if(e)if(this.numberOfMonths===1||t===0)this.navigationState={backward:!0},this._focusKey=i,this.navBackward(event);else{let r=this.contentViewChild.nativeElement.children[t-1];if(i){let o=E(r,i);o.tabIndex="0",o.focus()}else{let o=W(r,".p-datepicker-calendar td span:not(.p-disabled):not(.p-ink)"),u=o[o.length-1];u.tabIndex="0",u.focus()}}else if(this.numberOfMonths===1||t===this.numberOfMonths-1)this.navigationState={backward:!1},this._focusKey=i,this.navForward(event);else{let r=this.contentViewChild.nativeElement.children[t+1];if(i){let o=E(r,i);o.tabIndex="0",o.focus()}else{let o=E(r,".p-datepicker-calendar td span:not(.p-disabled):not(.p-ink)");o.tabIndex="0",o.focus()}}}updateFocus(){let e;if(this.navigationState){if(this.navigationState.button)this.initFocusableCell(),this.navigationState.backward?E(this.contentViewChild.nativeElement,".p-datepicker-prev").focus():E(this.contentViewChild.nativeElement,".p-datepicker-next").focus();else{if(this.navigationState.backward){let t;this.currentView==="month"?t=W(this.contentViewChild.nativeElement,".p-monthpicker .p-monthpicker-month:not(.p-disabled)"):this.currentView==="year"?t=W(this.contentViewChild.nativeElement,".p-yearpicker .p-yearpicker-year:not(.p-disabled)"):t=W(this.contentViewChild.nativeElement,this._focusKey||".p-datepicker-calendar td span:not(.p-disabled):not(.p-ink)"),t&&t.length>0&&(e=t[t.length-1])}else this.currentView==="month"?e=E(this.contentViewChild.nativeElement,".p-monthpicker .p-monthpicker-month:not(.p-disabled)"):this.currentView==="year"?e=E(this.contentViewChild.nativeElement,".p-yearpicker .p-yearpicker-year:not(.p-disabled)"):e=E(this.contentViewChild.nativeElement,this._focusKey||".p-datepicker-calendar td span:not(.p-disabled):not(.p-ink)");e&&(e.tabIndex="0",e.focus())}this.navigationState=null,this._focusKey=null}else this.initFocusableCell()}initFocusableCell(){let e=this.contentViewChild?.nativeElement,t;if(this.currentView==="month"){let i=W(e,".p-monthpicker .p-monthpicker-month:not(.p-disabled)"),r=E(e,".p-monthpicker .p-monthpicker-month.p-highlight");i.forEach(o=>o.tabIndex=-1),t=r||i[0],i.length===0&&W(e,'.p-monthpicker .p-monthpicker-month.p-disabled[tabindex = "0"]').forEach(u=>u.tabIndex=-1)}else if(this.currentView==="year"){let i=W(e,".p-yearpicker .p-yearpicker-year:not(.p-disabled)"),r=E(e,".p-yearpicker .p-yearpicker-year.p-highlight");i.forEach(o=>o.tabIndex=-1),t=r||i[0],i.length===0&&W(e,'.p-yearpicker .p-yearpicker-year.p-disabled[tabindex = "0"]').forEach(u=>u.tabIndex=-1)}else if(t=E(e,"span.p-highlight"),!t){let i=E(e,"td.p-datepicker-today span:not(.p-disabled):not(.p-ink)");i?t=i:t=E(e,".p-datepicker-calendar td span:not(.p-disabled):not(.p-ink)")}t&&(t.tabIndex="0",!this.preventFocus&&(!this.navigationState||!this.navigationState.button)&&setTimeout(()=>{this.disabled||t.focus()},1),this.preventFocus=!1)}trapFocus(e){let t=me(this.contentViewChild.nativeElement);if(t&&t.length>0)if(!t[0].ownerDocument.activeElement)t[0].focus();else{let i=t.indexOf(t[0].ownerDocument.activeElement);if(e.shiftKey)if(i==-1||i===0)if(this.focusTrap)t[t.length-1].focus();else{if(i===-1)return this.hideOverlay();if(i===0)return}else t[i-1].focus();else if(i==-1)if(this.timeOnly)t[0].focus();else{let r=0;for(let o=0;o<t.length;o++)t[o].tagName==="SPAN"&&(r=o);t[r].focus()}else if(i===t.length-1){if(!this.focusTrap&&i!=-1)return this.hideOverlay();t[0].focus()}else t[i+1].focus()}e.preventDefault()}onMonthDropdownChange(e){this.currentMonth=parseInt(e),this.onMonthChange.emit({month:this.currentMonth+1,year:this.currentYear}),this.createMonths(this.currentMonth,this.currentYear)}onYearDropdownChange(e){this.currentYear=parseInt(e),this.onYearChange.emit({month:this.currentMonth+1,year:this.currentYear}),this.createMonths(this.currentMonth,this.currentYear)}convertTo24Hour(e,t){return this.hourFormat=="12"?e===12?t?12:0:t?e+12:e:e}constrainTime(e,t,i,r){let o=[e,t,i],u,k=this.value,v=this.convertTo24Hour(e,r),y=this.isRangeSelection(),b=this.isMultipleSelection();(y||b)&&(this.value||(this.value=[new Date,new Date]),y&&(k=this.value[1]||this.value[0]),b&&(k=this.value[this.value.length-1]));let w=k?k.toDateString():null,f=this.minDate&&w&&this.minDate.toDateString()===w,g=this.maxDate&&w&&this.maxDate.toDateString()===w;switch(f&&(u=this.minDate.getHours()>=12),!0){case(f&&u&&this.minDate.getHours()===12&&this.minDate.getHours()>v):o[0]=11;case(f&&this.minDate.getHours()===v&&this.minDate.getMinutes()>t):o[1]=this.minDate.getMinutes();case(f&&this.minDate.getHours()===v&&this.minDate.getMinutes()===t&&this.minDate.getSeconds()>i):o[2]=this.minDate.getSeconds();break;case(f&&!u&&this.minDate.getHours()-1===v&&this.minDate.getHours()>v):o[0]=11,this.pm=!0;case(f&&this.minDate.getHours()===v&&this.minDate.getMinutes()>t):o[1]=this.minDate.getMinutes();case(f&&this.minDate.getHours()===v&&this.minDate.getMinutes()===t&&this.minDate.getSeconds()>i):o[2]=this.minDate.getSeconds();break;case(f&&u&&this.minDate.getHours()>v&&v!==12):this.setCurrentHourPM(this.minDate.getHours()),o[0]=this.currentHour;case(f&&this.minDate.getHours()===v&&this.minDate.getMinutes()>t):o[1]=this.minDate.getMinutes();case(f&&this.minDate.getHours()===v&&this.minDate.getMinutes()===t&&this.minDate.getSeconds()>i):o[2]=this.minDate.getSeconds();break;case(f&&this.minDate.getHours()>v):o[0]=this.minDate.getHours();case(f&&this.minDate.getHours()===v&&this.minDate.getMinutes()>t):o[1]=this.minDate.getMinutes();case(f&&this.minDate.getHours()===v&&this.minDate.getMinutes()===t&&this.minDate.getSeconds()>i):o[2]=this.minDate.getSeconds();break;case(g&&this.maxDate.getHours()<v):o[0]=this.maxDate.getHours();case(g&&this.maxDate.getHours()===v&&this.maxDate.getMinutes()<t):o[1]=this.maxDate.getMinutes();case(g&&this.maxDate.getHours()===v&&this.maxDate.getMinutes()===t&&this.maxDate.getSeconds()<i):o[2]=this.maxDate.getSeconds();break}return o}incrementHour(e){let t=this.currentHour??0,i=(this.currentHour??0)+this.stepHour,r=this.pm;this.hourFormat=="24"?i=i>=24?i-24:i:this.hourFormat=="12"&&(t<12&&i>11&&(r=!this.pm),i=i>=13?i-12:i),this.toggleAMPMIfNotMinDate(r),[this.currentHour,this.currentMinute,this.currentSecond]=this.constrainTime(i,this.currentMinute,this.currentSecond,r),e.preventDefault()}toggleAMPMIfNotMinDate(e){let t=this.value,i=t?t.toDateString():null;this.minDate&&i&&this.minDate.toDateString()===i&&this.minDate.getHours()>=12?this.pm=!0:this.pm=e}onTimePickerElementMouseDown(e,t,i){this.disabled||(this.repeat(e,null,t,i),e.preventDefault())}onTimePickerElementMouseUp(e){this.disabled||(this.clearTimePickerTimer(),this.updateTime())}onTimePickerElementMouseLeave(){!this.disabled&&this.timePickerTimer&&(this.clearTimePickerTimer(),this.updateTime())}repeat(e,t,i,r){let o=t||500;switch(this.clearTimePickerTimer(),this.timePickerTimer=setTimeout(()=>{this.repeat(e,100,i,r),this.cd.markForCheck()},o),i){case 0:r===1?this.incrementHour(e):this.decrementHour(e);break;case 1:r===1?this.incrementMinute(e):this.decrementMinute(e);break;case 2:r===1?this.incrementSecond(e):this.decrementSecond(e);break}this.updateInputfield()}clearTimePickerTimer(){this.timePickerTimer&&(clearTimeout(this.timePickerTimer),this.timePickerTimer=null)}decrementHour(e){let t=(this.currentHour??0)-this.stepHour,i=this.pm;this.hourFormat=="24"?t=t<0?24+t:t:this.hourFormat=="12"&&(this.currentHour===12&&(i=!this.pm),t=t<=0?12+t:t),this.toggleAMPMIfNotMinDate(i),[this.currentHour,this.currentMinute,this.currentSecond]=this.constrainTime(t,this.currentMinute,this.currentSecond,i),e.preventDefault()}incrementMinute(e){let t=(this.currentMinute??0)+this.stepMinute;t=t>59?t-60:t,[this.currentHour,this.currentMinute,this.currentSecond]=this.constrainTime(this.currentHour,t,this.currentSecond,this.pm),e.preventDefault()}decrementMinute(e){let t=(this.currentMinute??0)-this.stepMinute;t=t<0?60+t:t,[this.currentHour,this.currentMinute,this.currentSecond]=this.constrainTime(this.currentHour,t,this.currentSecond,this.pm),e.preventDefault()}incrementSecond(e){let t=this.currentSecond+this.stepSecond;t=t>59?t-60:t,[this.currentHour,this.currentMinute,this.currentSecond]=this.constrainTime(this.currentHour,this.currentMinute,t,this.pm),e.preventDefault()}decrementSecond(e){let t=this.currentSecond-this.stepSecond;t=t<0?60+t:t,[this.currentHour,this.currentMinute,this.currentSecond]=this.constrainTime(this.currentHour,this.currentMinute,t,this.pm),e.preventDefault()}updateTime(){let e=this.value;this.isRangeSelection()&&(e=this.value[1]||this.value[0]),this.isMultipleSelection()&&(e=this.value[this.value.length-1]),e=e?new Date(e.getTime()):new Date,this.hourFormat=="12"?this.currentHour===12?e.setHours(this.pm?12:0):e.setHours(this.pm?this.currentHour+12:this.currentHour):e.setHours(this.currentHour),e.setMinutes(this.currentMinute),e.setSeconds(this.currentSecond),this.isRangeSelection()&&(this.value[1]?e=[this.value[0],e]:e=[e,null]),this.isMultipleSelection()&&(e=[...this.value.slice(0,-1),e]),this.updateModel(e),this.onSelect.emit(e),this.updateInputfield()}toggleAMPM(e){let t=!this.pm;this.pm=t,[this.currentHour,this.currentMinute,this.currentSecond]=this.constrainTime(this.currentHour,this.currentMinute,this.currentSecond,t),this.updateTime(),e.preventDefault()}onUserInput(e){if(!this.isKeydown)return;this.isKeydown=!1;let t=e.target.value;try{let i=this.parseValueFromString(t);this.isValidSelection(i)?(this.updateModel(i),this.updateUI()):this.keepInvalid&&this.updateModel(i)}catch{let r=this.keepInvalid?t:null;this.updateModel(r)}this.filled=t!=null&&t.length,this.onInput.emit(e)}isValidSelection(e){if(this.isSingleSelection())return this.isSelectable(e.getDate(),e.getMonth(),e.getFullYear(),!1);let t=e.every(i=>this.isSelectable(i.getDate(),i.getMonth(),i.getFullYear(),!1));return t&&this.isRangeSelection()&&(t=e.length===1||e.length>1&&e[1]>=e[0]),t}parseValueFromString(e){if(!e||e.trim().length===0)return null;let t;if(this.isSingleSelection())t=this.parseDateTime(e);else if(this.isMultipleSelection()){let i=e.split(this.multipleSeparator);t=[];for(let r of i)t.push(this.parseDateTime(r.trim()))}else if(this.isRangeSelection()){let i=e.split(" "+this.rangeSeparator+" ");t=[];for(let r=0;r<i.length;r++)t[r]=this.parseDateTime(i[r].trim())}return t}parseDateTime(e){let t,i=e.split(" ");if(this.timeOnly)t=new Date,this.populateTime(t,i[0],i[1]);else{let r=this.getDateFormat();if(this.showTime){let o=this.hourFormat=="12"?i.pop():null,u=i.pop();t=this.parseDate(i.join(" "),r),this.populateTime(t,u,o)}else t=this.parseDate(e,r)}return t}populateTime(e,t,i){if(this.hourFormat=="12"&&!i)throw"Invalid Time";this.pm=i==="PM"||i==="pm";let r=this.parseTime(t);e.setHours(r.hour),e.setMinutes(r.minute),e.setSeconds(r.second)}isValidDate(e){return ae(e)&&Qe(e)}updateUI(){let e=this.value;Array.isArray(e)&&(e=e.length===2?e[1]:e[0]);let t=this.defaultDate&&this.isValidDate(this.defaultDate)&&!this.value?this.defaultDate:e&&this.isValidDate(e)?e:new Date;this.currentMonth=t.getMonth(),this.currentYear=t.getFullYear(),this.createMonths(this.currentMonth,this.currentYear),(this.showTime||this.timeOnly)&&(this.setCurrentHourPM(t.getHours()),this.currentMinute=t.getMinutes(),this.currentSecond=t.getSeconds())}showOverlay(){this.overlayVisible||(this.updateUI(),this.touchUI||(this.preventFocus=!0),this.overlayVisible=!0)}hideOverlay(){this.inputfieldViewChild?.nativeElement.focus(),this.overlayVisible=!1,this.clearTimePickerTimer(),this.touchUI&&this.disableModality(),this.cd.markForCheck()}toggle(){this.inline||(this.overlayVisible?this.hideOverlay():(this.showOverlay(),this.inputfieldViewChild?.nativeElement.focus()))}onOverlayAnimationStart(e){switch(e.toState){case"visible":case"visibleTouchUI":if(!this.inline){this.overlay=e.element,this.overlay?.setAttribute(this.attributeSelector,"");let t=this.inline?void 0:{position:"absolute",top:"0",left:"0"};Ne(this.overlay,t),this.appendOverlay(),this.updateFocus(),this.autoZIndex&&(this.touchUI?oe.set("modal",this.overlay,this.baseZIndex||this.config.zIndex.modal):oe.set("overlay",this.overlay,this.baseZIndex||this.config.zIndex.overlay)),this.alignOverlay(),this.onShow.emit(e)}break;case"void":this.onOverlayHide(),this.onClose.emit(e);break}}onOverlayAnimationDone(e){switch(e.toState){case"visible":case"visibleTouchUI":this.inline||(this.bindDocumentClickListener(),this.bindDocumentResizeListener(),this.bindScrollListener());break;case"void":this.autoZIndex&&oe.clear(e.element);break}}appendOverlay(){this.appendTo&&(this.appendTo==="body"?this.document.body.appendChild(this.overlay):Ke(this.appendTo,this.overlay))}restoreOverlayAppend(){this.overlay&&this.appendTo&&this.el.nativeElement.appendChild(this.overlay)}alignOverlay(){this.touchUI?this.enableModality(this.overlay):this.overlay&&(this.appendTo?(this.view==="date"?(this.overlay.style.width=ne(this.overlay)+"px",this.overlay.style.minWidth=ne(this.inputfieldViewChild?.nativeElement)+"px"):this.overlay.style.width=ne(this.inputfieldViewChild?.nativeElement)+"px",Re(this.overlay,this.inputfieldViewChild?.nativeElement)):Ue(this.overlay,this.inputfieldViewChild?.nativeElement))}enableModality(e){!this.mask&&this.touchUI&&(this.mask=this.renderer.createElement("div"),this.renderer.setStyle(this.mask,"zIndex",String(parseInt(e.style.zIndex)-1)),he(this.mask,"p-overlay-mask p-datepicker-mask p-datepicker-mask-scrollblocker p-overlay-mask p-overlay-mask-enter"),this.maskClickListener=this.renderer.listen(this.mask,"click",i=>{this.disableModality(),this.overlayVisible=!1}),this.renderer.appendChild(this.document.body,this.mask),Le())}disableModality(){this.mask&&(he(this.mask,"p-overlay-mask-leave"),this.animationEndListener||(this.animationEndListener=this.renderer.listen(this.mask,"animationend",this.destroyMask.bind(this))))}destroyMask(){if(!this.mask)return;this.renderer.removeChild(this.document.body,this.mask);let e=this.document.body.children,t;for(let i=0;i<e.length;i++){let r=e[i];if(R(r,"p-datepicker-mask-scrollblocker")){t=!0;break}}t||Be(),this.unbindAnimationEndListener(),this.unbindMaskClickListener(),this.mask=null}unbindMaskClickListener(){this.maskClickListener&&(this.maskClickListener(),this.maskClickListener=null)}unbindAnimationEndListener(){this.animationEndListener&&this.mask&&(this.animationEndListener(),this.animationEndListener=null)}writeValue(e){if(this.value=e,this.value&&typeof this.value=="string")try{this.value=this.parseValueFromString(this.value)}catch{this.keepInvalid&&(this.value=e)}this.updateInputfield(),this.updateUI(),this.cd.markForCheck()}registerOnChange(e){this.onModelChange=e}registerOnTouched(e){this.onModelTouched=e}setDisabledState(e){this.disabled=e,this.cd.markForCheck()}getDateFormat(){return this.dateFormat||this.getTranslation("dateFormat")}getFirstDateOfWeek(){return this._firstDayOfWeek||this.getTranslation(N.FIRST_DAY_OF_WEEK)}formatDate(e,t){if(!e)return"";let i,r=y=>{let b=i+1<t.length&&t.charAt(i+1)===y;return b&&i++,b},o=(y,b,O)=>{let w=""+b;if(r(y))for(;w.length<O;)w="0"+w;return w},u=(y,b,O,w)=>r(y)?w[b]:O[b],k="",v=!1;if(e)for(i=0;i<t.length;i++)if(v)t.charAt(i)==="'"&&!r("'")?v=!1:k+=t.charAt(i);else switch(t.charAt(i)){case"d":k+=o("d",e.getDate(),2);break;case"D":k+=u("D",e.getDay(),this.getTranslation(N.DAY_NAMES_SHORT),this.getTranslation(N.DAY_NAMES));break;case"o":k+=o("o",Math.round((new Date(e.getFullYear(),e.getMonth(),e.getDate()).getTime()-new Date(e.getFullYear(),0,0).getTime())/864e5),3);break;case"m":k+=o("m",e.getMonth()+1,2);break;case"M":k+=u("M",e.getMonth(),this.getTranslation(N.MONTH_NAMES_SHORT),this.getTranslation(N.MONTH_NAMES));break;case"y":k+=r("y")?e.getFullYear():(e.getFullYear()%100<10?"0":"")+e.getFullYear()%100;break;case"@":k+=e.getTime();break;case"!":k+=e.getTime()*1e4+this.ticksTo1970;break;case"'":r("'")?k+="'":v=!0;break;default:k+=t.charAt(i)}return k}formatTime(e){if(!e)return"";let t="",i=e.getHours(),r=e.getMinutes(),o=e.getSeconds();return this.hourFormat=="12"&&i>11&&i!=12&&(i-=12),this.hourFormat=="12"?t+=i===0?12:i<10?"0"+i:i:t+=i<10?"0"+i:i,t+=":",t+=r<10?"0"+r:r,this.showSeconds&&(t+=":",t+=o<10?"0"+o:o),this.hourFormat=="12"&&(t+=e.getHours()>11?" PM":" AM"),t}parseTime(e){let t=e.split(":"),i=this.showSeconds?3:2;if(t.length!==i)throw"Invalid time";let r=parseInt(t[0]),o=parseInt(t[1]),u=this.showSeconds?parseInt(t[2]):null;if(isNaN(r)||isNaN(o)||r>23||o>59||this.hourFormat=="12"&&r>12||this.showSeconds&&(isNaN(u)||u>59))throw"Invalid time";return this.hourFormat=="12"&&(r!==12&&this.pm?r+=12:!this.pm&&r===12&&(r-=12)),{hour:r,minute:o,second:u}}parseDate(e,t){if(t==null||e==null)throw"Invalid arguments";if(e=typeof e=="object"?e.toString():e+"",e==="")return null;let i,r,o,u=0,k=typeof this.shortYearCutoff!="string"?this.shortYearCutoff:new Date().getFullYear()%100+parseInt(this.shortYearCutoff,10),v=-1,y=-1,b=-1,O=-1,w=!1,f,g=U=>{let Z=i+1<t.length&&t.charAt(i+1)===U;return Z&&i++,Z},D=U=>{let Z=g(U),se=U==="@"?14:U==="!"?20:U==="y"&&Z?4:U==="o"?3:2,G=U==="y"?se:1,le=new RegExp("^\\d{"+G+","+se+"}"),K=e.substring(u).match(le);if(!K)throw"Missing number at position "+u;return u+=K[0].length,parseInt(K[0],10)},ge=(U,Z,se)=>{let G=-1,le=g(U)?se:Z,K=[];for(let A=0;A<le.length;A++)K.push([A,le[A]]);K.sort((A,J)=>-(A[1].length-J[1].length));for(let A=0;A<K.length;A++){let J=K[A][1];if(e.substr(u,J.length).toLowerCase()===J.toLowerCase()){G=K[A][0],u+=J.length;break}}if(G!==-1)return G+1;throw"Unknown name at position "+u},pe=()=>{if(e.charAt(u)!==t.charAt(i))throw"Unexpected literal at position "+u;u++};for(this.view==="month"&&(b=1),i=0;i<t.length;i++)if(w)t.charAt(i)==="'"&&!g("'")?w=!1:pe();else switch(t.charAt(i)){case"d":b=D("d");break;case"D":ge("D",this.getTranslation(N.DAY_NAMES_SHORT),this.getTranslation(N.DAY_NAMES));break;case"o":O=D("o");break;case"m":y=D("m");break;case"M":y=ge("M",this.getTranslation(N.MONTH_NAMES_SHORT),this.getTranslation(N.MONTH_NAMES));break;case"y":v=D("y");break;case"@":f=new Date(D("@")),v=f.getFullYear(),y=f.getMonth()+1,b=f.getDate();break;case"!":f=new Date((D("!")-this.ticksTo1970)/1e4),v=f.getFullYear(),y=f.getMonth()+1,b=f.getDate();break;case"'":g("'")?pe():w=!0;break;default:pe()}if(u<e.length&&(o=e.substr(u),!/^\s+/.test(o)))throw"Extra/unparsed characters found in date: "+o;if(v===-1?v=new Date().getFullYear():v<100&&(v+=new Date().getFullYear()-new Date().getFullYear()%100+(v<=k?0:-100)),O>-1){y=1,b=O;do{if(r=this.getDaysCountInMonth(v,y-1),b<=r)break;y++,b-=r}while(!0)}if(this.view==="year"&&(y=y===-1?1:y,b=b===-1?1:b),f=this.daylightSavingAdjust(new Date(v,y-1,b)),f.getFullYear()!==v||f.getMonth()+1!==y||f.getDate()!==b)throw"Invalid date";return f}daylightSavingAdjust(e){return e?(e.setHours(e.getHours()>12?e.getHours()+2:0),e):null}updateFilledState(){this.filled=this.inputFieldValue&&this.inputFieldValue!=""}isValidDateForTimeConstraints(e){return this.keepInvalid?!0:(!this.minDate||e>=this.minDate)&&(!this.maxDate||e<=this.maxDate)}onTodayButtonClick(e){let t=new Date,i={day:t.getDate(),month:t.getMonth(),year:t.getFullYear(),otherMonth:t.getMonth()!==this.currentMonth||t.getFullYear()!==this.currentYear,today:!0,selectable:!0};this.createMonths(t.getMonth(),t.getFullYear()),this.onDateSelect(e,i),this.onTodayClick.emit(t)}onClearButtonClick(e){this.updateModel(null),this.updateInputfield(),this.hideOverlay(),this.onClearClick.emit(e)}createResponsiveStyle(){if(this.numberOfMonths>1&&this.responsiveOptions){this.responsiveStyleElement||(this.responsiveStyleElement=this.renderer.createElement("style"),this.responsiveStyleElement.type="text/css",this.renderer.appendChild(this.document.body,this.responsiveStyleElement));let e="";if(this.responsiveOptions){let t=[...this.responsiveOptions].filter(i=>!!(i.breakpoint&&i.numMonths)).sort((i,r)=>-1*i.breakpoint.localeCompare(r.breakpoint,void 0,{numeric:!0}));for(let i=0;i<t.length;i++){let{breakpoint:r,numMonths:o}=t[i],u=`
                        .p-datepicker[${this.attributeSelector}] .p-datepicker-group:nth-child(${o}) .p-datepicker-next {
                            display: inline-flex !important;
                        }
                    `;for(let k=o;k<this.numberOfMonths;k++)u+=`
                            .p-datepicker[${this.attributeSelector}] .p-datepicker-group:nth-child(${k+1}) {
                                display: none !important;
                            }
                        `;e+=`
                        @media screen and (max-width: ${r}) {
                            ${u}
                        }
                    `}}this.responsiveStyleElement.innerHTML=e,We(this.responsiveStyleElement,"nonce",this.config?.csp()?.nonce)}}destroyResponsiveStyleElement(){this.responsiveStyleElement&&(this.responsiveStyleElement.remove(),this.responsiveStyleElement=null)}bindDocumentClickListener(){this.documentClickListener||this.zone.runOutsideAngular(()=>{let e=this.el?this.el.nativeElement.ownerDocument:this.document;this.documentClickListener=this.renderer.listen(e,"mousedown",t=>{this.isOutsideClicked(t)&&this.overlayVisible&&this.zone.run(()=>{this.hideOverlay(),this.onClickOutside.emit(t),this.cd.markForCheck()})})})}unbindDocumentClickListener(){this.documentClickListener&&(this.documentClickListener(),this.documentClickListener=null)}bindDocumentResizeListener(){!this.documentResizeListener&&!this.touchUI&&(this.documentResizeListener=this.renderer.listen(this.window,"resize",this.onWindowResize.bind(this)))}unbindDocumentResizeListener(){this.documentResizeListener&&(this.documentResizeListener(),this.documentResizeListener=null)}bindScrollListener(){this.scrollHandler||(this.scrollHandler=new st(this.containerViewChild?.nativeElement,()=>{this.overlayVisible&&this.hideOverlay()})),this.scrollHandler.bindScrollListener()}unbindScrollListener(){this.scrollHandler&&this.scrollHandler.unbindScrollListener()}isOutsideClicked(e){return!(this.el.nativeElement.isSameNode(e.target)||this.isNavIconClicked(e)||this.el.nativeElement.contains(e.target)||this.overlay&&this.overlay.contains(e.target))}isNavIconClicked(e){return R(e.target,"p-datepicker-prev")||R(e.target,"p-datepicker-prev-icon")||R(e.target,"p-datepicker-next")||R(e.target,"p-datepicker-next-icon")}onWindowResize(){this.overlayVisible&&!ze()&&this.hideOverlay()}onOverlayHide(){this.currentView=this.view,this.mask&&this.destroyMask(),this.unbindDocumentClickListener(),this.unbindDocumentResizeListener(),this.unbindScrollListener(),this.overlay=null}ngOnDestroy(){this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.translationSubscription&&this.translationSubscription.unsubscribe(),this.overlay&&this.autoZIndex&&oe.clear(this.overlay),this.destroyResponsiveStyleElement(),this.clearTimePickerTimer(),this.restoreOverlayAppend(),this.onOverlayHide(),super.ngOnDestroy()}static \u0275fac=function(t){return new(t||n)(ue(Ce),ue(je))};static \u0275cmp=xe({type:n,selectors:[["p-calendar"]],contentQueries:function(t,i,r){if(t&1&&(Y(r,mt,4),Y(r,ft,4),Y(r,gt,4),Y(r,vt,4),Y(r,kt,4),Y(r,yt,4),Y(r,bt,4),Y(r,wt,4),Y(r,Ct,4),Y(r,xt,4),Y(r,Tt,4),Y(r,Dt,4),Y(r,Ze,4)),t&2){let o;I(o=V())&&(i.dateTemplate=o.first),I(o=V())&&(i.headerTemplate=o.first),I(o=V())&&(i.footerTemplate=o.first),I(o=V())&&(i.disabledDateTemplate=o.first),I(o=V())&&(i.decadeTemplate=o.first),I(o=V())&&(i.previousIconTemplate=o.first),I(o=V())&&(i.nextIconTemplate=o.first),I(o=V())&&(i.triggerIconTemplate=o.first),I(o=V())&&(i.clearIconTemplate=o.first),I(o=V())&&(i.decrementIconTemplate=o.first),I(o=V())&&(i.incrementIconTemplate=o.first),I(o=V())&&(i.inputIconTemplate=o.first),I(o=V())&&(i.templates=o)}},viewQuery:function(t,i){if(t&1&&(ce(St,5),ce(Mt,5),ce(It,5)),t&2){let r;I(r=V())&&(i.containerViewChild=r.first),I(r=V())&&(i.inputfieldViewChild=r.first),I(r=V())&&(i.content=r.first)}},inputs:{iconDisplay:"iconDisplay",style:"style",styleClass:"styleClass",inputStyle:"inputStyle",inputId:"inputId",name:"name",inputStyleClass:"inputStyleClass",placeholder:"placeholder",ariaLabelledBy:"ariaLabelledBy",ariaLabel:"ariaLabel",iconAriaLabel:"iconAriaLabel",disabled:[2,"disabled","disabled",T],dateFormat:"dateFormat",multipleSeparator:"multipleSeparator",rangeSeparator:"rangeSeparator",inline:[2,"inline","inline",T],showOtherMonths:[2,"showOtherMonths","showOtherMonths",T],selectOtherMonths:[2,"selectOtherMonths","selectOtherMonths",T],showIcon:[2,"showIcon","showIcon",T],fluid:[2,"fluid","fluid",T],icon:"icon",appendTo:"appendTo",readonlyInput:[2,"readonlyInput","readonlyInput",T],shortYearCutoff:"shortYearCutoff",monthNavigator:[2,"monthNavigator","monthNavigator",T],yearNavigator:[2,"yearNavigator","yearNavigator",T],hourFormat:"hourFormat",timeOnly:[2,"timeOnly","timeOnly",T],stepHour:[2,"stepHour","stepHour",q],stepMinute:[2,"stepMinute","stepMinute",q],stepSecond:[2,"stepSecond","stepSecond",q],showSeconds:[2,"showSeconds","showSeconds",T],required:[2,"required","required",T],showOnFocus:[2,"showOnFocus","showOnFocus",T],showWeek:[2,"showWeek","showWeek",T],startWeekFromFirstDayOfYear:"startWeekFromFirstDayOfYear",showClear:[2,"showClear","showClear",T],dataType:"dataType",selectionMode:"selectionMode",maxDateCount:[2,"maxDateCount","maxDateCount",q],showButtonBar:[2,"showButtonBar","showButtonBar",T],todayButtonStyleClass:"todayButtonStyleClass",clearButtonStyleClass:"clearButtonStyleClass",autofocus:[2,"autofocus","autofocus",T],autoZIndex:[2,"autoZIndex","autoZIndex",T],baseZIndex:[2,"baseZIndex","baseZIndex",q],panelStyleClass:"panelStyleClass",panelStyle:"panelStyle",keepInvalid:[2,"keepInvalid","keepInvalid",T],hideOnDateTimeSelect:[2,"hideOnDateTimeSelect","hideOnDateTimeSelect",T],touchUI:[2,"touchUI","touchUI",T],timeSeparator:"timeSeparator",focusTrap:[2,"focusTrap","focusTrap",T],showTransitionOptions:"showTransitionOptions",hideTransitionOptions:"hideTransitionOptions",tabindex:[2,"tabindex","tabindex",q],variant:"variant",minDate:"minDate",maxDate:"maxDate",disabledDates:"disabledDates",disabledDays:"disabledDays",yearRange:"yearRange",showTime:"showTime",responsiveOptions:"responsiveOptions",numberOfMonths:"numberOfMonths",firstDayOfWeek:"firstDayOfWeek",locale:"locale",view:"view",defaultDate:"defaultDate"},outputs:{onFocus:"onFocus",onBlur:"onBlur",onClose:"onClose",onSelect:"onSelect",onClear:"onClear",onInput:"onInput",onTodayClick:"onTodayClick",onClearClick:"onClearClick",onMonthChange:"onMonthChange",onYearChange:"onYearChange",onClickOutside:"onClickOutside",onShow:"onShow"},features:[Ve([bn,_t]),De],ngContentSelectors:Et,decls:4,vars:6,consts:[["container",""],["inputfield",""],["contentWrapper",""],[3,"ngClass","ngStyle"],[3,"ngIf"],[3,"class","ngStyle","ngClass","click",4,"ngIf"],["pInputText","","type","text","role","combobox","aria-autocomplete","none","aria-haspopup","dialog","autocomplete","off",3,"focus","keydown","click","blur","input","value","readonly","ngStyle","ngClass","placeholder","disabled","pAutoFocus","variant","fluid"],[4,"ngIf"],["type","button","aria-haspopup","dialog","class","p-datepicker-dropdown","tabindex","0",3,"disabled","click",4,"ngIf"],[3,"class","click",4,"ngIf"],["class","p-datepicker-clear-icon",3,"click",4,"ngIf"],[3,"click"],[1,"p-datepicker-clear-icon",3,"click"],[4,"ngTemplateOutlet"],["type","button","aria-haspopup","dialog","tabindex","0",1,"p-datepicker-dropdown",3,"click","disabled"],[3,"ngClass",4,"ngIf"],[3,"ngClass"],[1,"p-datepicker-input-icon-container"],[3,"ngClass","click",4,"ngIf"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"click","ngClass"],[3,"click","ngStyle","ngClass"],["class","p-datepicker-time-picker",4,"ngIf"],["class","p-datepicker-buttonbar",4,"ngIf"],[1,"p-datepicker-calendar-container"],["class","p-datepicker-calendar",4,"ngFor","ngForOf"],["class","p-datepicker-month-view",4,"ngIf"],["class","p-datepicker-year-view",4,"ngIf"],[1,"p-datepicker-calendar"],[1,"p-datepicker-header"],["size","small","rounded","","text","","styleClass","p-datepicker-prev-button p-button-icon-only","type","button",3,"keydown","onClick","ngStyle"],[1,"p-datepicker-title"],["type","button","class","p-datepicker-select-month","pRipple","",3,"disabled","click","keydown",4,"ngIf"],["type","button","class","p-datepicker-select-year","pRipple","",3,"disabled","click","keydown",4,"ngIf"],["class","p-datepicker-decade",4,"ngIf"],["rounded","","text","","size","small","styleClass","p-datepicker-next-button p-button-icon-only",3,"keydown","onClick","ngStyle"],["class","p-datepicker-day-view","role","grid",4,"ngIf"],["type","button","pRipple","",1,"p-datepicker-select-month",3,"click","keydown","disabled"],["type","button","pRipple","",1,"p-datepicker-select-year",3,"click","keydown","disabled"],[1,"p-datepicker-decade"],["role","grid",1,"p-datepicker-day-view"],["class","p-datepicker-weekheader p-disabled",4,"ngIf"],["class","p-datepicker-weekday-cell","scope","col",4,"ngFor","ngForOf"],[4,"ngFor","ngForOf"],[1,"p-datepicker-weekheader","p-disabled"],["scope","col",1,"p-datepicker-weekday-cell"],[1,"p-datepicker-weekday"],["class","p-datepicker-weeknumber",4,"ngIf"],[3,"ngClass",4,"ngFor","ngForOf"],[1,"p-datepicker-weeknumber"],[1,"p-datepicker-weeklabel-container","p-disabled"],["draggable","false","pRipple","",3,"click","keydown","ngClass"],["class","p-hidden-accessible","aria-live","polite",4,"ngIf"],["aria-live","polite",1,"p-hidden-accessible"],[1,"p-datepicker-month-view"],["pRipple","",3,"ngClass","click","keydown",4,"ngFor","ngForOf"],["pRipple","",3,"click","keydown","ngClass"],[1,"p-datepicker-year-view"],[1,"p-datepicker-time-picker"],[1,"p-datepicker-hour-picker"],["rounded","","text","","size","small","styleClass","p-datepicker-increment-button p-button-icon-only",3,"keydown","keydown.enter","keydown.space","mousedown","mouseup","keyup.enter","keyup.space","mouseleave"],[1,"p-datepicker-separator"],[1,"p-datepicker-minute-picker"],["class","p-datepicker-separator",4,"ngIf"],["class","p-datepicker-second-picker",4,"ngIf"],["class","p-datepicker-ampm-picker",4,"ngIf"],[1,"p-datepicker-second-picker"],[1,"p-datepicker-ampm-picker"],["size","small","text","","rounded","","styleClass","p-datepicker-increment-button p-button-icon-only",3,"keydown","onClick","keydown.enter"],["size","small","text","","rounded","","styleClass","p-datepicker-increment-button p-button-icon-only",3,"keydown","click","keydown.enter"],[1,"p-datepicker-buttonbar"],["size","small","styleClass","p-datepicker-today-button",3,"keydown","onClick","label","ngClass"],["size","small","styleClass","p-datepicker-clear-button",3,"keydown","onClick","label","ngClass"]],template:function(t,i){t&1&&(Se(Vt),m(0,"span",3,0),_(2,Xt,5,24,"ng-template",4)(3,gn,9,20,"div",5),h()),t&2&&(X(i.styleClass),c("ngClass",i.rootClass)("ngStyle",i.style),l(2),c("ngIf",!i.inline),l(),c("ngIf",i.inline||i.overlayVisible))},dependencies:[He,Ee,Oe,Fe,Ye,$e,dt,at,tt,it,nt,et,rt,Xe,lt,ct,de],encapsulation:2,data:{animation:[Pe("overlayAnimation",[Ae("visibleTouchUI",j({transform:"translate(-50%,-50%)",opacity:1})),ie("void => visible",[j({opacity:0,transform:"scaleY(0.8)"}),te("{{showTransitionParams}}",j({opacity:1,transform:"*"}))]),ie("visible => void",[te("{{hideTransitionParams}}",j({opacity:0}))]),ie("void => visibleTouchUI",[j({opacity:0,transform:"translate3d(-50%, -40%, 0) scale(0.9)"}),te("{{showTransitionParams}}")]),ie("visibleTouchUI => void",[te("{{hideTransitionParams}}",j({opacity:0,transform:"translate3d(-50%, -40%, 0) scale(0.9)"}))])])]},changeDetection:0})}return n})(),Zn=(()=>{class n{static \u0275fac=function(t){return new(t||n)};static \u0275mod=Te({type:n});static \u0275inj=ye({imports:[ht,de,de]})}return n})();export{ht as a,Zn as b};
