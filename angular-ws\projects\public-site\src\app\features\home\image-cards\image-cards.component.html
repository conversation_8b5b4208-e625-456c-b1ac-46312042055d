<div class="image-cards-container p-6">
  <!-- 筛选和操作工具栏 -->
  <div
    class="filters-toolbar flex justify-end items-center gap-4 mb-6 rounded-lg"
  >
    <!-- 日期选择器 -->
    <div class="date-picker-container">
      <span class="p-float-label">
        <p-calendar
          placeholder="{{ i18nService.translate('common.selectDate') }}"
          [(ngModel)]="selectedDate"
          view="month"
          dateFormat="yy-mm"
          [readonlyInput]="true"
          (onSelect)="onDateChange($event)"
          showClear
          (onClear)="onDateChange(null)"
        >
        </p-calendar>
      </span>
    </div>

    <!-- 播放按钮 -->
    <p-button
      icon="pi pi-play"
      [label]="i18nService.translate('common.play')"
      [outlined]="true"
      severity="primary"
      (onClick)="playCurrentPage()"
    >
    </p-button>
  </div>

  <!-- 卡片网格容器 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
    @for (item of cardItems; track item.id) {
      <p-card styleClass="card-item" [id]="'card-' + item.id">
        <ng-template pTemplate="header">
          <img
            [src]="item.thumbnailUrl"
            [alt]="item.title + ' 封面图片'"
            class="card-image rounded-t-xl"
          />
        </ng-template>
        <p
          class="p-card-title cursor-pointer"
          (click)="navigateToArticle(item.id)"
          [innerHTML]="item.title"
        >
        </p>
        <p class="mt-2 text-gray-500 text-sm">
          {{ item.description }}
        </p>
        <ng-template pTemplate="footer">
          <div class="text-gray-500 text-sm flex items-center">
            <i class="pi pi-clock mr-2"></i>
            {{ item.creationTime | date: "yyyy-MM-dd HH:mm:ss" }}
          </div>
        </ng-template>
      </p-card>
    }
  </div>

  <!-- 分页组件 -->
  <div class="pagination-container">
    <p-paginator
      [first]="first"
      [rows]="rows"
      [totalRecords]="totalRecords"
      [rowsPerPageOptions]="rowsPerPageOptions"
      [showPageLinks]="!isMobile"
      [showCurrentPageReport]="isMobile"
      (onPageChange)="onPageChange($event)"
      styleClass="custom-paginator"
    >
    </p-paginator>
  </div>
</div>
