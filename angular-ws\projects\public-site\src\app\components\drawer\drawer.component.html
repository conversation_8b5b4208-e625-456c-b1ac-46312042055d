<p-drawer
  [visible]="drawerService.isVisible()"
  [header]="drawerService.config().title"
  [position]="drawerService.config().position || 'right'"
  [style]="{ width: drawerService.config().width }"
  [modal]="drawerService.config().modal"
  [blockScroll]="true"
  (onHide)="drawerService.close()"
>
  <!-- 设置内容 -->
  <div *ngIf="drawerService.isType('settings')" class="drawer-content">
    <h4>音频设置</h4>
    <p-divider></p-divider>
    <div class="setting-item flex justify-between items-center">
      <label>音频语言设置</label>
      <div class="language-selector flex items-center">
        <p-menu
          #menu1
          [model]="audioLanguages()"
          [popup]="true"
          appendTo="body"
        />
        <p-button
          (click)="menu1.toggle($event)"
          [label]="i18nService.currentAudioDeviceInfo().label"
          [text]="true"
          size="small"
        />
      </div>
    </div>
    <div class="setting-item">
      <label>界面设置</label>
      <div class="language-selector flex items-center gap-2">
        @for (item of themeService.themes; track $index) {
          <div class="w-6 h-6 rounded-full cursor-pointer" [style]="{'background-color': item['color']}" (click)="themeService.setTheme(item.mode)"></div>
        }
      </div>
    </div>
  </div>

  <!-- 播放列表内容 -->
  <div *ngIf="drawerService.isType('playlist')" class="drawer-content">
    <h4>当前播放</h4>
    <p-divider></p-divider>
    <div class="playlist-item current">
      <i class="pi pi-play text-green-500"></i>
      <span>正在播放的音频</span>
      <div class="progress-info">
        <small>02:35 / 05:42</small>
      </div>
    </div>

    <h4>播放队列</h4>
    <p-divider></p-divider>
    <div class="playlist-items">
      <div class="playlist-item">
        <i class="pi pi-music"></i>
        <div class="item-info">
          <span class="title">示例音频 1</span>
          <small class="duration">03:20</small>
        </div>
        <i class="pi pi-times remove-btn"></i>
      </div>
      <div class="playlist-item">
        <i class="pi pi-music"></i>
        <div class="item-info">
          <span class="title">示例音频 2</span>
          <small class="duration">04:15</small>
        </div>
        <i class="pi pi-times remove-btn"></i>
      </div>
      <div class="playlist-item">
        <i class="pi pi-music"></i>
        <div class="item-info">
          <span class="title">示例音频 3</span>
          <small class="duration">02:58</small>
        </div>
        <i class="pi pi-times remove-btn"></i>
      </div>
    </div>

    <div class="playlist-controls">
      <button type="button" class="clear-all-btn">
        <i class="pi pi-trash"></i>
        清空播放列表
      </button>
    </div>
  </div>

  <!-- 自定义内容 -->
  <div *ngIf="drawerService.isType('custom')" class="drawer-content">
    <h4>自定义功能</h4>
    <p-divider></p-divider>
    <p>这里可以放置任何自定义内容</p>
  </div>
</p-drawer>
