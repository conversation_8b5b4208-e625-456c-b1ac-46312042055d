﻿using HolyBless.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.Buckets
{
    public class BucketFileUrl : FullAuditedAggregateRoot<int>
    {
        public int BucketFileId { get; set; } //Reference to BucketFile
        public BucketFile BucketFile { get; set; } = default!;
        public string ComputeUrl { get; set; } = "";
        public string ProviderCode { get; set; } = ProviderCodeConstants.CloudFlare;

        public BucketFileUrl()
        {
        }

        public BucketFileUrl(int id) : base(id)
        {
        }
    }
}