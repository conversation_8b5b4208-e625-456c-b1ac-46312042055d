import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface VirtualFolderDto extends AuditedEntityDto<number> {
  parentFolderId?: number;
  folderName?: string;
  contentCode?: string;
  languageCode?: string;
  views: number;
  weight: number;
  channelId?: number;
}

export interface VirtualFolderFileDto {
  fileId: number;
  fileName?: string;
  title?: string;
  fileUrl?: string;
}

export interface VirtualFolderFileSearchDto extends PagedAndSortedResultRequestDto {
  folderId?: number;
  contentCode?: string;
}

export interface VirtualFolderSearchDto extends PagedAndSortedResultRequestDto {
  channelId?: number;
  contentCode?: string;
}

export interface VirtualFolderTreeDto {
  id: number;
  folderName?: string;
  contentCode?: string;
  parentFolderId?: number;
  views: number;
  weight: number;
  channelId?: number;
  isRoot: boolean;
  children: VirtualFolderTreeDto[];
}
