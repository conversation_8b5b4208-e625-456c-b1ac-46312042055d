using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Domain.Interfaces;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Books;
using HolyBless.Entities.Collections;
using HolyBless.Entities.Tags;
using HolyBless.Enums;
using HolyBless.Results;
using HolyBless.Tags.Dtos;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using HolyBless.Services;
using HolyBless.Buckets;
using Microsoft.AspNetCore.Authorization;
using System;

namespace HolyBless.Articles
{
    [AllowAnonymous]
    public class ReadOnlyArticleAppService(
        IArticleRepository articleRepository,
        IRepository<Article, int> repository,
        IRepository<ArticleToTag> articleToTagRepository,
        IRepository<Tag, int> tagRepository,
        IRepository<TeacherArticleLink> teacherArticleLinkRepository,
        IRepository<CollectionToArticle> collectionToArticleRepository,
        IRepository<ChapterToArticle> chapterToArticleRepository,
        IRequestContextService requestContextService,
        ICachedFileUrlAppService cachedFileUrlAppService
            ) : HolyBlessAppService(cachedFileUrlAppService, requestContextService), IReadOnlyArticleAppService
    {
        protected readonly IArticleRepository articleRepository = articleRepository;
        protected readonly IRepository<Article, int> _repository = repository;
        protected readonly IRepository<ArticleToTag> _articleToTagRepository = articleToTagRepository;
        protected readonly IRepository<Tag, int> _tagRepository = tagRepository;
        protected readonly IRepository<TeacherArticleLink> _teacherArticleLinkRepository = teacherArticleLinkRepository;
        protected readonly IRepository<CollectionToArticle> _collectionToArticleRepository = collectionToArticleRepository;
        protected readonly IRepository<ChapterToArticle> _chapterToArticleRepository = chapterToArticleRepository;

        [RemoteService(false)]
        public async Task<ArticleDto> GetAsync(int id)
        {
            var queryable = await _repository.GetQueryableAsync();
            var article = await queryable
                .Include(x => x.ArticleToTags)
                .Include(x => x.ArticleFiles)
                    .ThenInclude(af => af.BucketFile)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), id);
            }

            var rt = ObjectMapper.Map<Article, ArticleDto>(article);
            await FillThumbnailUrl(rt);
            return rt;
        }

        [RemoteService(false)]
        public async Task<PagedResultDto<ArticleDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Include(x => x.ArticleToTags)
                // ArticleFiles are not included when retrieving a list to optimize performance
                .OrderBy(input.Sorting ?? "Title")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var articles = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            var rtList = ObjectMapper.Map<List<Article>, List<ArticleDto>>(articles);
            await FillThumbnailUrls(rtList);
            return new PagedResultDto<ArticleDto>(
                totalCount,
                rtList
            );
        }

        [RemoteService(false)]
        public async Task<List<TagDto>> GetTagsAsync(int articleId)
        {
            // Verify article exists
            var article = await _repository.GetAsync(articleId);
            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), articleId);
            }

            // Get article tags using a join
            var articleTagsQueryable = await _articleToTagRepository.GetQueryableAsync();
            var tagsQueryable = await _tagRepository.GetQueryableAsync();

            var query = from articleTag in articleTagsQueryable
                        join tag in tagsQueryable on articleTag.TagId equals tag.Id
                        where articleTag.ArticleId == articleId
                        select tag;

            // Include ContentCode to properly map to TagDto
            query = query.Include(t => t.ContentCode);

            var tags = await AsyncExecuter.ToListAsync(query);

            return ObjectMapper.Map<List<Tag>, List<TagDto>>(tags);
        }

        [RemoteService(false)]
        public async Task<PagedResultDto<TeacherArticleLinkDto>> GetTeacherArticleLinksAsync(int studentArticleId, int skipCount, int maxResultCount, string? sorting = null)
        {
            var queryable = await _teacherArticleLinkRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.StudentArticleId == studentArticleId)
                .OrderBy(sorting ?? nameof(TeacherArticleLink.Weight));
            var items = await AsyncExecuter.ToListAsync(
                query.Skip(skipCount).Take(maxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);
            var dtos = ObjectMapper.Map<List<TeacherArticleLink>, List<TeacherArticleLinkDto>>(items);
            return new PagedResultDto<TeacherArticleLinkDto>(totalCount, dtos);
        }

        /// <summary>
        /// Usage: On Album detail page, on the right nav bar, show Article links of current selected File
        /// </summary>
        /// <param name="fileId"></param>
        /// <returns></returns>
        public async Task<List<ArticleDto>> GetRelatedArticlesByFileId(int fileId)
        {
            var query = await _repository.GetQueryableAsync();
            var list = await query.Include(x => x.ArticleFiles)
                .Where(x => x.ArticleFiles.All(x => x.FileId == fileId))
                .Distinct()
                .ToListAsync();

            var rtList = ObjectMapper.Map<List<Article>, List<ArticleDto>>(list);
            await FillThumbnailUrls(rtList);
            return rtList;
        }

        /// <summary>
        /// Article aggregate details (include all Article FileInfo) for a given article ID
        /// Usage1: For ArticleDetail page
        /// Usage2: ListStyle: ArticleTree/CollectionArticleTree's right content panel
        /// </summary>
        /// <param name="articleId"></param>
        /// <returns></returns>
        public async Task<ArticleAggregateResult?> GetArticleAggregateAsync(int articleId)
        {
            var rt = await articleRepository.GetArticleAggregateAsync(articleId);
            if (rt != null)
            {
                await FillThumbnailUrl(rt);
                await FillFileUrls(rt.ArticleFiles);
            }
            return rt;
        }

        /// <summary>
        /// Get article aggregate details by a given delivery date and language code (in request header)
        /// Usage1: For ArticleDetail page
        /// Usage2: ListStyle: ArticleTree/CollectionArticleTree's right content panel
        /// </summary>
        /// <param name="deliveryDate"></param>
        /// <returns></returns>
        public async Task<ArticleAggregateResult?> GetArticleAggregateAsync(DateTime deliveryDate)
        {
            var articleId = await GetMatchedId(deliveryDate);
            if (articleId == 0)
            {
                return null; // No article found for the given delivery date
            }
            return await GetArticleAggregateAsync(articleId);
        }

        /// <summary>
        /// List all article details for a given collection ID
        /// Usage: For ListStyle CollectionTree
        /// </summary>
        /// <param name="collectionId"></param>
        /// <returns></returns>
        public async Task<List<ArticleAggregateResult>> GetArticleAggregatesByCollectionIdAsync(int collectionId)
        {
            // Get all article IDs for the given collection
            var collectionToArticleQueryable = await _collectionToArticleRepository.GetQueryableAsync();
            var articleIds = await AsyncExecuter.ToListAsync(
                collectionToArticleQueryable
                    .Where(x => x.CollectionId == collectionId)
                    .OrderByDescending(x => x.Weight)
                    .Select(x => x.ArticleId)
            );

            if (!articleIds.Any())
            {
                return new List<ArticleAggregateResult>();
            }

            // Use the repository method to get aggregates for all articles
            var rtList = await articleRepository.GetArticleAggregatesAsync(articleIds);
            await FillThumbnailUrls(rtList);
            foreach (var article in rtList)
            {
                await FillFileUrls(article.ArticleFiles);
            }
            return rtList;
        }

        /// <summary>
        /// List all article details for a given collection content code and language code (in request header)
        /// Usage: For ListStyle CollectionTree
        /// </summary>
        /// <param name="collectionContentCode"></param>
        /// <returns></returns>
        public async Task<List<ArticleAggregateResult>> GetArticleAggregatesByCollectionContentCodeAsync(string collectionContentCode)
        {
            var collectionToArticleQueryable = await _collectionToArticleRepository.GetQueryableAsync();
            var lang = _requestContextService!.GetLanguageCode();
            var collection = await collectionToArticleQueryable
                .FirstOrDefaultAsync(x => x.Collection.ContentCode == collectionContentCode && x.Collection.LanguageCode == lang);
            if (collection == null) return new List<ArticleAggregateResult>();
            return await GetArticleAggregatesByCollectionIdAsync(collection.CollectionId);
        }

        /// <summary>
        /// Usage: For EBook detail page, to show all articles in the chapter
        /// </summary>
        /// <param name="chapterId"></param>
        /// <param name="skipCount">Number of items to skip for pagination.</param>
        /// <param name="maxResultCount">Maximum number of items to return for pagination.</param>
        /// <returns></returns>
        public async Task<List<ArticleAggregateResult>> GetArticleAggregatesByChapterIdAsync(int chapterId, int skipCount = 0, int maxResultCount = 20)
        {
            // Get all article IDs for the given chapter
            var chapterToArticleQueryable = await _chapterToArticleRepository.GetQueryableAsync();

            var query = chapterToArticleQueryable
                .Where(x => x.ChapterId == chapterId)
                .OrderBy(x => x.Weight)
                .Skip(skipCount)
                .Take(maxResultCount)
                .Select(x => x.ArticleId);

            var articleIds = await AsyncExecuter.ToListAsync(query);

            if (!articleIds.Any())
            {
                return new List<ArticleAggregateResult>();
            }

            // Use the repository method to get aggregates for all articles
            var rtList = await articleRepository.GetArticleAggregatesAsync(articleIds);
            await FillThumbnailUrls(rtList);
            foreach (var article in rtList)
            {
                await FillFileUrls(article.ArticleFiles);
            }
            return rtList;
        }

        /// <summary>
        /// Usage: Article advance search, return paging results of articles based on search criteria.
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<ArticleSearchResultDto>> SearchAsync(ArticleSearchDto input)
        {
            var queryable = await _repository.GetQueryableAsync();

            // Apply keyword search on specified fields
            if (!string.IsNullOrWhiteSpace(input.Keyword) && input.SearchFields != null && input.SearchFields.Count > 0)
            {
                var keywordConditions = new List<System.Linq.Expressions.Expression<Func<Article, bool>>>();

                foreach (var field in input.SearchFields)
                {
                    switch (field)
                    {
                        case SearchFieldEnum.Title:
                            keywordConditions.Add(x => x.Title.Contains(input.Keyword!));
                            break;

                        case SearchFieldEnum.Content:
                            keywordConditions.Add(x => x.Content != null && x.Content.Contains(input.Keyword!));
                            break;
                    }
                }

                // Combine conditions with OR logic
                if (keywordConditions.Count > 0)
                {
                    var combinedCondition = keywordConditions[0];
                    for (int i = 1; i < keywordConditions.Count; i++)
                    {
                        var condition = keywordConditions[i];
                        combinedCondition = CombineWithOr(combinedCondition, condition);
                    }
                    queryable = queryable.Where(combinedCondition);
                }
            }

            // Apply other filters
            queryable = queryable
                .WhereIf(input.DeliveryDateStart.HasValue, x => x.DeliveryDate >= input.DeliveryDateStart!.Value)
                .WhereIf(input.DeliveryDateEnd.HasValue, x => x.DeliveryDate <= input.DeliveryDateEnd!.Value)
                .WhereIf(input.ContentCategories != null && input.ContentCategories.Count > 0,
                    x => input.ContentCategories!.Contains(x.ArticleContentCategory));

            var totalCount = await AsyncExecuter.CountAsync(queryable);

            var query = queryable.OrderBy(input.Sorting ?? "Title")
                         .Skip(input.SkipCount)
                         .Take(input.MaxResultCount);

            var articles = await query.Select(x => new ArticleSearchResultDto
            {
                Id = x.Id,
                Title = x.Title,
                Description = x.Description,
                DeliveryDate = x.DeliveryDate,
                ArticleContentCategory = x.ArticleContentCategory,
                Content = x.Content,
                LastModificationTime = x.LastModificationTime ?? x.CreationTime,
            }).ToListAsync();

            return new PagedResultDto<ArticleSearchResultDto>(totalCount, articles);
        }

        private static System.Linq.Expressions.Expression<Func<Article, bool>> CombineWithOr(
            System.Linq.Expressions.Expression<Func<Article, bool>> expr1,
            System.Linq.Expressions.Expression<Func<Article, bool>> expr2)
        {
            var parameter = System.Linq.Expressions.Expression.Parameter(typeof(Article));
            var body = System.Linq.Expressions.Expression.OrElse(
                System.Linq.Expressions.Expression.Invoke(expr1, parameter),
                System.Linq.Expressions.Expression.Invoke(expr2, parameter));
            return System.Linq.Expressions.Expression.Lambda<Func<Article, bool>>(body, parameter);
        }

        private async Task<int> GetMatchedId(DateTime deliveryDate)
        {
            var lang = _requestContextService!.GetLanguageCode();
            var article = await _repository.FirstOrDefaultAsync(x => x.DeliveryDate == deliveryDate && x.LanguageCode == lang);
            if (article == null) return 0;
            return article.Id;
        }
    }
}