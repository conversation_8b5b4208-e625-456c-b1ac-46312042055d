import type { EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { MediaType } from '../../enums/media-type.enum';
import type { ContentCategory } from '../../enums/content-category.enum';

export interface BucketFileDto extends EntityDto<number> {
  fileName?: string;
  title?: string;
  relativePathInBucket?: string;
  languageCode?: string;
  spokenLangCode?: string;
  mediaType?: MediaType;
  contentCategory?: ContentCategory;
  deliveryDate?: string;
  bucketName?: string;
  size?: number;
  views: number;
  youtubeId?: string;
  environment?: string;
  exists: boolean;
}

export interface BucketFileSearchDto extends PagedAndSortedResultRequestDto {
  fileName?: string;
  deliveryDateStart?: string;
  deliveryDateEnd?: string;
  contentCategories?: ContentCategory[];
}

export interface FileSearchResultDto {
  fileName?: string;
  mediaType?: string;
  contentCategory?: ContentCategory;
  lastModificationTime?: string;
  fileId: number;
  fileUrl?: string;
}
