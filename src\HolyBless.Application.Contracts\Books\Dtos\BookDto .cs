﻿using System;
using HolyBless.Enums;
using HolyBless.Interfaces;
using Volo.Abp.Application.Dtos;
using static HolyBless.Permissions.HolyBlessPermissions;

namespace HolyBless.Books.Dtos;

public class EBookDto : AuditedEntityDto<int>, IHaveThumbnail
{
    public string Title { get; set; } = "";
    public string? Description { get; set; }
    public int Weight { get; set; } = 0;
    public int? ChannelId { get; set; }
    public BookType Type { get; set; }
    public int? ThumbnailFileId { get; set; }
    public string? ThumbnailUrl { get; set; }
    public DateTime DeliveryDate { get; set; }

    public int Views { get; set; }
    public int Likes { get; set; }

    public string? LanguageCode { get; set; }
}