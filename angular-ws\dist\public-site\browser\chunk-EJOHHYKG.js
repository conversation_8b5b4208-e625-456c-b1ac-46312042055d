import{a as l}from"./chunk-D6WDCTDG.js";import{R as c,X as g,ra as o,xc as i}from"./chunk-BL4EGCPV.js";import{d as u}from"./chunk-4CLCTAJ7.js";var d=class r{constructor(){this.document=g(l);this.supportedLanguages=[{code:"zh-<PERSON>",label:"\u7B80\u4F53\u4E2D\u6587"},{code:"zh-Hant",label:"\u7E41\u9AD4\u4E2D\u6587"},{code:"en",label:"English"}];this.supportedAudioDevices=[{code:"cmn",label:"\u666E\u901A\u8BDD"},{code:"yue",label:"\u7CA4\u8BED"},{code:"eng",label:"English"}];this.language=o(this.getStoredLanguage()||"zh-Hans");this.audioDevice=o(this.getStoredAudioDevice()||"cmn");this.translations=o({});this.currentLanguageInfo=i(()=>this.supportedLanguages.find(e=>e.code===this.language())||this.supportedLanguages[0]);this.currentAudioDeviceInfo=i(()=>this.supportedAudioDevices.find(e=>e.code===this.audioDevice())||this.supportedAudioDevices[0]);this.loadTranslations()}setLanguage(e){this.language.set(e),localStorage.setItem("lang",e),this.updateDocumentLanguage()}setAudioDevice(e){this.audioDevice.set(e),localStorage.setItem("audio",e)}getStoredLanguage(){return localStorage.getItem("lang")||"zh-Hans"}getStoredAudioDevice(){return localStorage.getItem("audio")||"cmn"}loadTranslations(){return u(this,null,function*(){try{let[e,t,n]=yield Promise.all([import("./chunk-75WHJW2L.js"),import("./chunk-JIH2CHZL.js"),import("./chunk-FZU5Q5R7.js")]);this.translations.set({"zh-Hans":e.default,"zh-Hant":t.default,en:n.default})}catch(e){console.error("Failed to load translations:",e)}})}updateDocumentLanguage(){this.document.documentElement.lang=this.language()}translate(e,t){let n=this.translations()[this.language()];if(!n)return e;let a=this.getNestedValue(n,e);return typeof a!="string"?e:t?Object.keys(t).reduce((h,s)=>h.replace(new RegExp(`{{${s}}}`,"g"),t[s]),a):a}getNestedValue(e,t){return t.split(".").reduce((n,a)=>n&&typeof n=="object"?n[a]:void 0,e)||t}static{this.\u0275fac=function(t){return new(t||r)}}static{this.\u0275prov=c({token:r,factory:r.\u0275fac,providedIn:"root"})}};export{d as a};
