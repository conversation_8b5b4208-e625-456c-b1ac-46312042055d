import { ReadOnlyVirtualFolderService } from '@/proxy/holy-bless/virtual-folders';
import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { TreeNode } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { TreeModule } from 'primeng/tree';

@Component({
  selector: 'app-net-disk',
  standalone: true,
  imports: [CommonModule, RouterModule, TreeModule, ButtonModule, TableModule],
  templateUrl: './folder-detail.html',
  styleUrls: ['./folder-detail.scss'],
})
export class FolderDetailComponent {
  #ReadOnlyVirtualFolderService = inject(ReadOnlyVirtualFolderService);
  route = inject(ActivatedRoute);

  files = signal<TreeNode[]>([]);

  selectedFile!: TreeNode;
  products = [
    {
      id: '1',
      name: 'Product 1',
      type: 'Type A',
      lastModified: new Date(),
    },
    {
      id: '2',
      name: 'Product 2',
      type: 'Type B',
      lastModified: new Date(),
    },
    {
      id: '3',
      name: 'Product 3',
      type: 'Type C',
      lastModified: new Date(),
    },
    {
      id: '4',
      name: 'Product 4',
      type: 'Type D',
      lastModified: new Date(),
    },
  ];
  ngOnInit() {
    this.route.params.subscribe((params) => {
      const folderId = params['folderId'];
      if (folderId) {
        this.loadFolderDetails(folderId);
      }
    });
  }

  loadFolderDetails(folderId: number) {
    this.#ReadOnlyVirtualFolderService
      .getVirtualFolderTree(folderId)
      .subscribe({
        next: (data) => {
          this.files.set(data.map((folder) => this.buildTreeNode(folder)));
        },
        error: (error) => {
          console.error('获取文件夹详情失败:', error);
        },
      });
  }

  buildTreeNode(folder: any): TreeNode {
    return {
      label: folder.folderName,
      data: folder,
      children: folder.children.map(this.buildTreeNode),
    };
  }
}
