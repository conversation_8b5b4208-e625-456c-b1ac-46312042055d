﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.Buckets
{
    public class BucketFileLanguage : FullAuditedAggregateRoot<int>
    {
        public int FileId { get; set; }
        public string? LanguageCode { get; set; }
        public string? SpokenLangCode { get; set; }
    }
}