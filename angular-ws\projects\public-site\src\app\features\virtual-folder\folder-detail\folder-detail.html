<div class="flex flex-1">
  <p-tree
    [value]="files()"
    styleClass="w-[20rem] h-full"
    selectionMode="single"
    [(selection)]="selectedFile"
    [virtualScroll]="true"
    virtualScrollItemSize="36"
    [filter]="true"
  />
  <div
    class="articaldetail-container prose max-w-none p-6 flex-1 overflow-y-auto"
    style="height: calc(100vh - 5rem)"
  >
    <p-table [value]="products" [tableStyle]="{ 'min-width': '60rem' }">
      <ng-template #caption>
        <div class="flex items-center gap-2">
          <p-button icon="pi pi-download" label="下载" [outlined]="true" />
          <p-button icon="pi pi-play" label="播放" [outlined]="true" />
        </div>
      </ng-template>
      <ng-template #header>
        <tr>
          <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
          <th>名称</th>
          <th>类型</th>
          <th>更新时间</th>
          <th></th>
        </tr>
      </ng-template>
      <ng-template #body let-product>
        <tr>
          <td>
            <p-tableCheckbox [value]="product" />
          </td>
          <td>{{ product.name }}</td>
          <td>{{ product.type }}</td>
          <td>{{ product.lastModified | date: 'yyyy-MM-dd HH:mm:ss' }}</td>
          <td>
            <div class="flex items-center gap-2">
              <p-button icon="pi pi-download" label="下载" [outlined]="true" />
              <p-button
                icon="pi pi-play"
                label="播放"
                [outlined]="true"
              />
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template #footer> </ng-template>
    </p-table>
  </div>
</div>
