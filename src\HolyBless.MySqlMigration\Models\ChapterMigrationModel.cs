using System;

namespace HolyBless.MySqlMigration.Models
{
    /// <summary>
    /// Model for chapter migration data from MySQL to PostgreSQL
    /// </summary>
    public class ChapterMigrationModel
    {
        public int BookId { get; set; }
        public int Views { get; set; }
        public int Likes { get; set; }
        public int Id { get; set; }
        public int? parent_id { get; set; }
        public string name { get; set; } = string.Empty;
        public string? description { get; set; }
        public int weigh { get; set; }
        public DateTime createtime { get; set; }
        public DateTime updatetime { get; set; }

        /// <summary>
        /// Transform parent_id: if value is 0, change it to null
        /// </summary>
        public int? ParentChapterId => parent_id == 0 ? null : parent_id;
    }
}
