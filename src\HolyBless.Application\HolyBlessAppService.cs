﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using HolyBless.Localization;
using Volo.Abp.Application.Services;
using HolyBless.Interfaces;
using HolyBless.Buckets;
using HolyBless.Services;

namespace HolyBless;

/* Inherit your application services from this class.
 */

public abstract class HolyBlessAppService : ApplicationService
{
    protected readonly ICachedFileUrlAppService? _cachedFileUrlAppService;
    protected readonly IRequestContextService? _requestContextService;

    protected HolyBlessAppService()
    {
        LocalizationResource = typeof(HolyBlessResource);
    }

    protected HolyBlessAppService(
        ICachedFileUrlAppService cachedFileUrlAppService
        )
    {
        _cachedFileUrlAppService = cachedFileUrlAppService;
        LocalizationResource = typeof(HolyBlessResource);
    }

    protected HolyBlessAppService(
        ICachedFileUrlAppService cachedFileUrlAppService
        , IRequestContextService requestContextService
        ) : this(cachedFileUrlAppService)
    {
        _requestContextService = requestContextService;
    }

    protected virtual string ChooseUrl(ICollection<BucketFileUrl>? urls, string providerCode)
    {
        if (urls == null || urls.Count == 0)
        {
            return string.Empty;
        }
        if (urls.Count == 1)
        {
            return urls.First().ComputeUrl;
        }
        var matchedUrl = urls.FirstOrDefault(x => x.ProviderCode == providerCode);
        if (matchedUrl != null)
        {
            return matchedUrl.ComputeUrl;
        }
        matchedUrl = urls.FirstOrDefault(x => x.ProviderCode == ProviderCodeConstants.CloudFlare);
        return matchedUrl?.ComputeUrl ?? string.Empty;
    }

    protected async Task FillThumbnailUrl<T>(T albumDto) where T : IHaveThumbnail
    {
        if (_cachedFileUrlAppService == null || albumDto.ThumbnailFileId == null)
        {
            return;
        }
        //Thumbnail always uses CloudFlare
        var preferProvider = ProviderCodeConstants.CloudFlare;
        /*if (_requestContextService != null)
        {
            preferProvider = _requestContextService.GetPreferProvider();
        }
        */
        albumDto.ThumbnailUrl = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(
            albumDto.ThumbnailFileId.Value, preferProvider);
    }

    protected async Task FillThumbnailUrls<T>(List<T> albumDtos) where T : IHaveThumbnail
    {
        var preferProvider = ProviderCodeConstants.CloudFlare;
        if (_cachedFileUrlAppService == null || albumDtos.Count == 0)
        {
            return;
        }

        var urls = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(
            albumDtos.Select(x => x.ThumbnailFileId).ToList(),
            preferProvider
        );
        foreach (var al in albumDtos)
        {
            if (al.ThumbnailFileId == null)
            {
                continue;
            }
            al.ThumbnailUrl = urls.TryGetValue(al.ThumbnailFileId.Value, out var url) ? url : null;
        }
    }

    protected async Task FillFileUrl<T>(T albumDto) where T : IHaveFileUrl
    {
        if (_cachedFileUrlAppService == null)
        {
            return;
        }
        var preferProvider = ProviderCodeConstants.CloudFlare;
        if (_requestContextService != null)
        {
            preferProvider = _requestContextService.GetPreferProvider();
        }
        albumDto.FileUrl = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(
            albumDto.FileId, preferProvider);
    }

    protected async Task FillFileUrls<T>(List<T> albumDtos) where T : IHaveFileUrl
    {
        var preferProvider = ProviderCodeConstants.CloudFlare;
        if (_cachedFileUrlAppService == null || albumDtos.Count == 0)
        {
            return;
        }
        if (_requestContextService != null)
        {
            preferProvider = _requestContextService.GetPreferProvider();
        }
        var ids = albumDtos.Select(x => x.FileId).ToList();
        var urls = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(
            ids,
            preferProvider
        );
        foreach (var al in albumDtos)
        {
            al.FileUrl = urls.TryGetValue(al.FileId, out var url) ? url : null;
        }
    }
}