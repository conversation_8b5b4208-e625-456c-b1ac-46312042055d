import{a as ee,b as te}from"./chunk-4CLCTAJ7.js";function Io(e,t){return Object.is(e,t)}var j=null,wn=!1,Eo=1,ne=Symbol("SIGNAL");function E(e){let t=j;return j=e,t}function Do(){return j}var ut={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function dt(e){if(wn)throw new Error("");if(j===null)return;j.consumerOnSignalRead(e);let t=j.nextProducerIndex++;if(_n(j),t<j.producerNode.length&&j.producerNode[t]!==e&&Bt(j)){let n=j.producerNode[t];xn(n,j.producerIndexOfThis[t])}j.producerNode[t]!==e&&(j.producerNode[t]=e,j.producerIndexOfThis[t]=Bt(j)?pa(e,j,t):0),j.producerLastReadVersion[t]=e.version}function fa(){Eo++}function wo(e){if(!(Bt(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Eo)){if(!e.producerMustRecompute(e)&&!Cn(e)){vo(e);return}e.producerRecomputeValue(e),vo(e)}}function bo(e){if(e.liveConsumerNode===void 0)return;let t=wn;wn=!0;try{for(let n of e.liveConsumerNode)n.dirty||$d(n)}finally{wn=t}}function Mo(){return j?.consumerAllowSignalWrites!==!1}function $d(e){e.dirty=!0,bo(e),e.consumerMarkedDirty?.(e)}function vo(e){e.dirty=!1,e.lastCleanEpoch=Eo}function $t(e){return e&&(e.nextProducerIndex=0),E(e)}function Mn(e,t){if(E(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Bt(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)xn(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Cn(e){_n(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(wo(n),r!==n.version))return!0}return!1}function Ut(e){if(_n(e),Bt(e))for(let t=0;t<e.producerNode.length;t++)xn(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function pa(e,t,n){if(ha(e),e.liveConsumerNode.length===0&&ga(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=pa(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function xn(e,t){if(ha(e),e.liveConsumerNode.length===1&&ga(e))for(let r=0;r<e.producerNode.length;r++)xn(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];_n(o),o.producerIndexOfThis[r]=t}}function Bt(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function _n(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function ha(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function ga(e){return e.producerNode!==void 0}function Co(e,t){let n=Object.create(Ud);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(wo(n),dt(n),n.value===bn)throw n.error;return n.value};return r[ne]=n,r}var mo=Symbol("UNSET"),yo=Symbol("COMPUTING"),bn=Symbol("ERRORED"),Ud=te(ee({},ut),{value:mo,dirty:!0,error:null,equal:Io,kind:"computed",producerMustRecompute(e){return e.value===mo||e.value===yo},producerRecomputeValue(e){if(e.value===yo)throw new Error("Detected cycle in computations.");let t=e.value;e.value=yo;let n=$t(e),r,o=!1;try{r=e.computation(),E(null),o=t!==mo&&t!==bn&&r!==bn&&e.equal(t,r)}catch(i){r=bn,e.error=i}finally{Mn(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function qd(){throw new Error}var ma=qd;function ya(e){ma(e)}function xo(e){ma=e}var Wd=null;function _o(e,t){let n=Object.create(Tn);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(dt(n),n.value);return r[ne]=n,r}function ft(e,t){Mo()||ya(e),e.equal(e.value,t)||(e.value=t,zd(e))}function To(e,t){Mo()||ya(e),ft(e,t(e.value))}var Tn=te(ee({},ut),{equal:Io,value:void 0,kind:"signal"});function zd(e){e.version++,fa(),bo(e),Wd?.()}function No(e){let t=E(null);try{return e()}finally{E(t)}}var So;function qt(){return So}function ye(e){let t=So;return So=e,t}var Nn=Symbol("NotFound");function v(e){return typeof e=="function"}function pt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Sn=pt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function He(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var L=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(v(r))try{r()}catch(i){t=i instanceof Sn?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{va(i)}catch(s){t=t??[],s instanceof Sn?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Sn(t)}}add(t){var n;if(t&&t!==this)if(this.closed)va(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&He(n,t)}remove(t){let{_finalizers:n}=this;n&&He(n,t),t instanceof e&&t._removeParent(this)}};L.EMPTY=(()=>{let e=new L;return e.closed=!0,e})();var ko=L.EMPTY;function kn(e){return e instanceof L||e&&"closed"in e&&v(e.remove)&&v(e.add)&&v(e.unsubscribe)}function va(e){v(e)?e():e.unsubscribe()}var ae={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var ht={setTimeout(e,t,...n){let{delegate:r}=ht;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=ht;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Rn(e){ht.setTimeout(()=>{let{onUnhandledError:t}=ae;if(t)t(e);else throw e})}function ve(){}var Ia=Ro("C",void 0,void 0);function Ea(e){return Ro("E",void 0,e)}function Da(e){return Ro("N",e,void 0)}function Ro(e,t,n){return{kind:e,value:t,error:n}}var Be=null;function gt(e){if(ae.useDeprecatedSynchronousErrorHandling){let t=!Be;if(t&&(Be={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Be;if(Be=null,n)throw r}}else e()}function wa(e){ae.useDeprecatedSynchronousErrorHandling&&Be&&(Be.errorThrown=!0,Be.error=e)}var $e=class extends L{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,kn(t)&&t.add(this)):this.destination=Kd}static create(t,n,r){return new _e(t,n,r)}next(t){this.isStopped?Ao(Da(t),this):this._next(t)}error(t){this.isStopped?Ao(Ea(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Ao(Ia,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Yd=Function.prototype.bind;function Oo(e,t){return Yd.call(e,t)}var Po=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){On(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){On(r)}else On(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){On(n)}}},_e=class extends $e{constructor(t,n,r){super();let o;if(v(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&ae.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Oo(t.next,i),error:t.error&&Oo(t.error,i),complete:t.complete&&Oo(t.complete,i)}):o=t}this.destination=new Po(o)}};function On(e){ae.useDeprecatedSynchronousErrorHandling?wa(e):Rn(e)}function Jd(e){throw e}function Ao(e,t){let{onStoppedNotification:n}=ae;n&&ht.setTimeout(()=>n(e,t))}var Kd={closed:!0,next:ve,error:Jd,complete:ve};var mt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function z(e){return e}function Xd(...e){return Lo(e)}function Lo(e){return e.length===0?z:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var _=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=tf(n)?n:new _e(n,r,o);return gt(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=ba(r),new r((o,i)=>{let s=new _e({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[mt](){return this}pipe(...n){return Lo(n)(this)}toPromise(n){return n=ba(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function ba(e){var t;return(t=e??ae.Promise)!==null&&t!==void 0?t:Promise}function ef(e){return e&&v(e.next)&&v(e.error)&&v(e.complete)}function tf(e){return e&&e instanceof $e||ef(e)&&kn(e)}function Fo(e){return v(e?.lift)}function b(e){return t=>{if(Fo(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function D(e,t,n,r,o){return new jo(e,t,n,r,o)}var jo=class extends $e{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Vo(){return b((e,t)=>{let n=null;e._refCount++;let r=D(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Ho=class extends _{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Fo(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new L;let n=this.getSubject();t.add(this.source.subscribe(D(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=L.EMPTY)}return t}refCount(){return Vo()(this)}};var Ma=pt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Te=(()=>{class e extends _{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new An(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Ma}next(n){gt(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){gt(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){gt(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?ko:(this.currentObservers=null,i.push(n),new L(()=>{this.currentObservers=null,He(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new _;return n.source=this,n}}return e.create=(t,n)=>new An(t,n),e})(),An=class extends Te{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:ko}};var Wt=class extends Te{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Bo={now(){return(Bo.delegate||Date).now()},delegate:void 0};var Pn=class extends L{constructor(t,n){super()}schedule(t,n=0){return this}};var zt={setInterval(e,t,...n){let{delegate:r}=zt;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=zt;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Ln=class extends Pn{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return zt.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&zt.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,He(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var yt=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};yt.now=Bo.now;var Fn=class extends yt{constructor(t,n=yt.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var $o=new Fn(Ln),Ca=$o;var Gt=new _(e=>e.complete());function jn(e){return e&&v(e.schedule)}function xa(e){return e[e.length-1]}function Vn(e){return v(xa(e))?e.pop():void 0}function Ne(e){return jn(xa(e))?e.pop():void 0}function Ta(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(f){s(f)}}function c(u){try{l(r.throw(u))}catch(f){s(f)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function _a(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Ue(e){return this instanceof Ue?(this.v=e,this):new Ue(e)}function Na(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(h){return Promise.resolve(h).then(d,f)}}function a(d,h){r[d]&&(o[d]=function(y){return new Promise(function(A,T){i.push([d,y,A,T])>1||c(d,y)})},h&&(o[d]=h(o[d])))}function c(d,h){try{l(r[d](h))}catch(y){p(i[0][3],y)}}function l(d){d.value instanceof Ue?Promise.resolve(d.value.v).then(u,f):p(i[0][2],d)}function u(d){c("next",d)}function f(d){c("throw",d)}function p(d,h){d(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Sa(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof _a=="function"?_a(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var Hn=e=>e&&typeof e.length=="number"&&typeof e!="function";function Bn(e){return v(e?.then)}function $n(e){return v(e[mt])}function Un(e){return Symbol.asyncIterator&&v(e?.[Symbol.asyncIterator])}function qn(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function nf(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Wn=nf();function zn(e){return v(e?.[Wn])}function Gn(e){return Na(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Ue(n.read());if(o)return yield Ue(void 0);yield yield Ue(r)}}finally{n.releaseLock()}})}function Qn(e){return v(e?.getReader)}function P(e){if(e instanceof _)return e;if(e!=null){if($n(e))return rf(e);if(Hn(e))return of(e);if(Bn(e))return sf(e);if(Un(e))return ka(e);if(zn(e))return af(e);if(Qn(e))return cf(e)}throw qn(e)}function rf(e){return new _(t=>{let n=e[mt]();if(v(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function of(e){return new _(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function sf(e){return new _(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Rn)})}function af(e){return new _(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function ka(e){return new _(t=>{lf(e,t).catch(n=>t.error(n))})}function cf(e){return ka(Gn(e))}function lf(e,t){var n,r,o,i;return Ta(this,void 0,void 0,function*(){try{for(n=Sa(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function Q(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Zn(e,t=0){return b((n,r)=>{n.subscribe(D(r,o=>Q(r,e,()=>r.next(o),t),()=>Q(r,e,()=>r.complete(),t),o=>Q(r,e,()=>r.error(o),t)))})}function Yn(e,t=0){return b((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Ra(e,t){return P(e).pipe(Yn(t),Zn(t))}function Oa(e,t){return P(e).pipe(Yn(t),Zn(t))}function Aa(e,t){return new _(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Pa(e,t){return new _(n=>{let r;return Q(n,t,()=>{r=e[Wn](),Q(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>v(r?.return)&&r.return()})}function Jn(e,t){if(!e)throw new Error("Iterable cannot be null");return new _(n=>{Q(n,t,()=>{let r=e[Symbol.asyncIterator]();Q(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function La(e,t){return Jn(Gn(e),t)}function Fa(e,t){if(e!=null){if($n(e))return Ra(e,t);if(Hn(e))return Aa(e,t);if(Bn(e))return Oa(e,t);if(Un(e))return Jn(e,t);if(zn(e))return Pa(e,t);if(Qn(e))return La(e,t)}throw qn(e)}function Se(e,t){return t?Fa(e,t):P(e)}function uf(...e){let t=Ne(e);return Se(e,t)}function df(e,t){let n=v(e)?e:()=>e,r=o=>o.error(n());return new _(t?o=>t.schedule(r,0,o):r)}function ff(e){return!!e&&(e instanceof _||v(e.lift)&&v(e.subscribe))}var ce=pt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function pf(e,t){let n=typeof t=="object";return new Promise((r,o)=>{let i=!1,s;e.subscribe({next:a=>{s=a,i=!0},error:o,complete:()=>{i?r(s):n?r(t.defaultValue):o(new ce)}})})}function hf(e,t){let n=typeof t=="object";return new Promise((r,o)=>{let i=new _e({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{n?r(t.defaultValue):o(new ce)}});e.subscribe(i)})}function ja(e){return e instanceof Date&&!isNaN(e)}function ke(e,t){return b((n,r)=>{let o=0;n.subscribe(D(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:gf}=Array;function mf(e,t){return gf(t)?e(...t):e(t)}function Kn(e){return ke(t=>mf(e,t))}var{isArray:yf}=Array,{getPrototypeOf:vf,prototype:If,keys:Ef}=Object;function Xn(e){if(e.length===1){let t=e[0];if(yf(t))return{args:t,keys:null};if(Df(t)){let n=Ef(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Df(e){return e&&typeof e=="object"&&vf(e)===If}function er(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function wf(...e){let t=Ne(e),n=Vn(e),{args:r,keys:o}=Xn(e);if(r.length===0)return Se([],t);let i=new _(bf(r,t,o?s=>er(o,s):z));return n?i.pipe(Kn(n)):i}function bf(e,t,n=z){return r=>{Va(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Va(t,()=>{let l=Se(e[c],t),u=!1;l.subscribe(D(r,f=>{i[c]=f,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Va(e,t,n){e?Q(n,e,t):t()}function Ha(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,f=!1,p=()=>{f&&!c.length&&!l&&t.complete()},d=y=>l<r?h(y):c.push(y),h=y=>{i&&t.next(y),l++;let A=!1;P(n(y,u++)).subscribe(D(t,T=>{o?.(T),i?d(T):t.next(T)},()=>{A=!0},void 0,()=>{if(A)try{for(l--;c.length&&l<r;){let T=c.shift();s?Q(t,s,()=>h(T)):h(T)}p()}catch(T){t.error(T)}}))};return e.subscribe(D(t,d,()=>{f=!0,p()})),()=>{a?.()}}function qe(e,t,n=1/0){return v(t)?qe((r,o)=>ke((i,s)=>t(r,i,o,s))(P(e(r,o))),n):(typeof t=="number"&&(n=t),b((r,o)=>Ha(r,o,e,n)))}function Uo(e=1/0){return qe(z,e)}function Ba(){return Uo(1)}function tr(...e){return Ba()(Se(e,Ne(e)))}function Mf(e){return new _(t=>{P(e()).subscribe(t)})}function Cf(...e){let t=Vn(e),{args:n,keys:r}=Xn(e),o=new _(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let f=!1;P(n[u]).subscribe(D(i,p=>{f||(f=!0,l--),a[u]=p},()=>c--,void 0,()=>{(!c||!f)&&(l||i.next(r?er(r,a):a),i.complete())}))}});return t?o.pipe(Kn(t)):o}function $a(e=0,t,n=Ca){let r=-1;return t!=null&&(jn(t)?n=t:r=t),new _(o=>{let i=ja(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function xf(e=0,t=$o){return e<0&&(e=0),$a(e,e,t)}var _f=new _(ve);function Qt(e,t){return b((n,r)=>{let o=0;n.subscribe(D(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Ua(e){return b((t,n)=>{let r=null,o=!1,i;r=t.subscribe(D(n,void 0,void 0,s=>{i=P(e(s,Ua(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function qa(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(D(s,u=>{let f=l++;c=a?e(c,u,f):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Tf(e,t){return v(t)?qe(e,t,1):qe(e,1)}function Nf(e){return b((t,n)=>{let r=!1,o=null,i=null,s=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let a=o;o=null,n.next(a)}};t.subscribe(D(n,a=>{i?.unsubscribe(),r=!0,o=a,i=D(n,s,ve),P(e(a)).subscribe(i)},()=>{s(),n.complete()},void 0,()=>{o=i=null}))})}function Zt(e){return b((t,n)=>{let r=!1;t.subscribe(D(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function qo(e){return e<=0?()=>Gt:b((t,n)=>{let r=0;t.subscribe(D(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Sf(e,t=z){return e=e??kf,b((n,r)=>{let o,i=!0;n.subscribe(D(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function kf(e,t){return e===t}function nr(e=Rf){return b((t,n)=>{let r=!1;t.subscribe(D(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Rf(){return new ce}function Of(e){return b((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Af(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Qt((o,i)=>e(o,i,r)):z,qo(1),n?Zt(t):nr(()=>new ce))}function Wo(e){return e<=0?()=>Gt:b((t,n)=>{let r=[];t.subscribe(D(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Pf(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Qt((o,i)=>e(o,i,r)):z,Wo(1),n?Zt(t):nr(()=>new ce))}function Lf(e,t){return b(qa(e,t,arguments.length>=2,!0))}function Ff(...e){let t=Ne(e);return b((n,r)=>{(t?tr(e,n,t):tr(e,n)).subscribe(r)})}function jf(e,t){return b((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(D(r,c=>{o?.unsubscribe();let l=0,u=i++;P(e(c,u)).subscribe(o=D(r,f=>r.next(t?t(c,f,u,l++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Vf(e){return b((t,n)=>{P(e).subscribe(D(n,()=>n.complete(),ve)),!n.closed&&t.subscribe(n)})}function Hf(e,t,n){let r=v(e)||t||n?{next:e,error:t,complete:n}:e;return r?b((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(D(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):z}var Bc="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",C=class extends Error{code;constructor(t,n){super($c(t,n)),this.code=t}};function Bf(e){return`NG0${Math.abs(e)}`}function $c(e,t){return`${Bf(e)}${t?": "+t:""}`}var Ur=Symbol("InputSignalNode#UNSET"),Uc=te(ee({},Tn),{transformFn:void 0,applyValueToInputSignal(e,t){ft(e,t)}});function qc(e,t){let n=Object.create(Uc);n.value=e,n.transformFn=t?.transform;function r(){if(dt(n),n.value===Ur){let o=null;throw new C(-950,o)}return n.value}return r[ne]=n,r}function fn(e){return{toString:e}.toString()}var rr="__parameters__";function $f(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Wc(e,t,n){return fn(()=>{let r=$f(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let f=c.hasOwnProperty(rr)?c[rr]:Object.defineProperty(c,rr,{value:[]})[rr];for(;f.length<=u;)f.push(null);return(f[u]=f[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Re=globalThis;function S(e){for(let t in e)if(e[t]===S)return t;throw Error("Could not find renamed property on target object.")}function Uf(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function J(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(J).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function si(e,t){return e?t?`${e} ${t}`:e:t||""}var qf=S({__forward_ref__:S});function zc(e){return e.__forward_ref__=zc,e.toString=function(){return J(this())},e}function B(e){return Gc(e)?e():e}function Gc(e){return typeof e=="function"&&e.hasOwnProperty(qf)&&e.__forward_ref__===zc}function $(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ix(e){return{providers:e.providers||[],imports:e.imports||[]}}function qr(e){return Wa(e,Qc)||Wa(e,Zc)}function sx(e){return qr(e)!==null}function Wa(e,t){return e.hasOwnProperty(t)?e[t]:null}function Wf(e){let t=e&&(e[Qc]||e[Zc]);return t||null}function za(e){return e&&(e.hasOwnProperty(Ga)||e.hasOwnProperty(zf))?e[Ga]:null}var Qc=S({\u0275prov:S}),Ga=S({\u0275inj:S}),Zc=S({ngInjectableDef:S}),zf=S({ngInjectorDef:S}),k=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=$({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Yc(e){return e&&!!e.\u0275providers}var Gf=S({\u0275cmp:S}),Qf=S({\u0275dir:S}),Zf=S({\u0275pipe:S}),Yf=S({\u0275mod:S}),hr=S({\u0275fac:S}),Xt=S({__NG_ELEMENT_ID__:S}),Qa=S({__NG_ENV_ID__:S});function Qe(e){return typeof e=="string"?e:e==null?"":String(e)}function Jf(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Qe(e)}function Jc(e,t){throw new C(-200,e)}function ws(e,t){throw new C(-201,!1)}var M=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(M||{}),ai;function Kc(){return ai}function Z(e){let t=ai;return ai=e,t}function Xc(e,t,n){let r=qr(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&M.Optional)return null;if(t!==void 0)return t;ws(e,"Injector")}var Kf={},ze=Kf,ci="__NG_DI_FLAG__",gr=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?Nn:ze,r)}},mr="ngTempTokenPath",Xf="ngTokenPath",ep=/\n/gm,tp="\u0275",Za="__source";function np(e,t=M.Default){if(qt()===void 0)throw new C(-203,!1);if(qt()===null)return Xc(e,void 0,t);{let n=qt(),r;return n instanceof gr?r=n.injector:r=n,r.get(e,t&M.Optional?null:void 0,t)}}function Le(e,t=M.Default){return(Kc()||np)(B(e),t)}function x(e,t=M.Default){return Le(e,Wr(t))}function Wr(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function li(e){let t=[];for(let n=0;n<e.length;n++){let r=B(e[n]);if(Array.isArray(r)){if(r.length===0)throw new C(900,!1);let o,i=M.Default;for(let s=0;s<r.length;s++){let a=r[s],c=rp(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(Le(o,i))}else t.push(Le(r))}return t}function el(e,t){return e[ci]=t,e.prototype[ci]=t,e}function rp(e){return e[ci]}function op(e,t,n,r){let o=e[mr];throw t[Za]&&o.unshift(t[Za]),e.message=ip(`
`+e.message,o,n,r),e[Xf]=o,e[mr]=null,e}function ip(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==tp?e.slice(2):e;let o=J(t);if(Array.isArray(t))o=t.map(J).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):J(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(ep,`
  `)}`}var tl=el(Wc("Optional"),8);var nl=el(Wc("SkipSelf"),4);function Ze(e,t){let n=e.hasOwnProperty(hr);return n?e[hr]:null}function sp(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function ap(e){return e.flat(Number.POSITIVE_INFINITY)}function bs(e,t){e.forEach(n=>Array.isArray(n)?bs(n,t):t(n))}function rl(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function yr(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function cp(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function lp(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function zr(e,t,n){let r=pn(e,t);return r>=0?e[r|1]=n:(r=~r,lp(e,r,t,n)),r}function zo(e,t){let n=pn(e,t);if(n>=0)return e[n|1]}function pn(e,t){return up(e,t,1)}function up(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var fe={},Y=[],vr=new k(""),ol=new k("",-1),il=new k(""),Ir=class{get(t,n=ze){if(n===ze){let r=new Error(`NullInjectorError: No provider for ${J(t)}!`);throw r.name="NullInjectorError",r}return n}};function sl(e,t){let n=e[Yf]||null;if(!n&&t===!0)throw new Error(`Type ${J(e)} does not have '\u0275mod' property.`);return n}function Ye(e){return e[Gf]||null}function al(e){return e[Qf]||null}function dp(e){return e[Zf]||null}function cl(e){return{\u0275providers:e}}function fp(...e){return{\u0275providers:ll(!0,e),\u0275fromNgModule:!0}}function ll(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return bs(t,s=>{let a=s;ui(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&ul(o,i),n}function ul(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Ms(o,i=>{t(i,r)})}}function ui(e,t,n,r){if(e=B(e),!e)return!1;let o=null,i=za(e),s=!i&&Ye(e);if(!i&&!s){let c=e.ngModule;if(i=za(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)ui(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{bs(i.imports,u=>{ui(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&ul(l,t)}if(!a){let l=Ze(o)||(()=>new o);t({provide:o,useFactory:l,deps:Y},o),t({provide:il,useValue:o,multi:!0},o),t({provide:vr,useValue:()=>Le(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;Ms(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function Ms(e,t){for(let n of e)Yc(n)&&(n=n.\u0275providers),Array.isArray(n)?Ms(n,t):t(n)}var pp=S({provide:String,useValue:S});function dl(e){return e!==null&&typeof e=="object"&&pp in e}function hp(e){return!!(e&&e.useExisting)}function gp(e){return!!(e&&e.useFactory)}function Mt(e){return typeof e=="function"}function mp(e){return!!e.useClass}var fl=new k(""),cr={},Ya={},Go;function Cs(){return Go===void 0&&(Go=new Ir),Go}var Fe=class{},tn=class extends Fe{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,fi(t,s=>this.processProvider(s)),this.records.set(ol,vt(void 0,this)),o.has("environment")&&this.records.set(Fe,vt(void 0,this));let i=this.records.get(fl);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(il,Y,M.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?Nn:ze,r)}destroy(){Jt(this),this._destroyed=!0;let t=E(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),E(t)}}onDestroy(t){return Jt(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Jt(this);let n=ye(this),r=Z(void 0),o;try{return t()}finally{ye(n),Z(r)}}get(t,n=ze,r=M.Default){if(Jt(this),t.hasOwnProperty(Qa))return t[Qa](this);r=Wr(r);let o,i=ye(this),s=Z(void 0);try{if(!(r&M.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=Dp(t)&&qr(t);l&&this.injectableDefInScope(l)?c=vt(di(t),cr):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&M.Self?Cs():this.parent;return n=r&M.Optional&&n===ze?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[mr]=a[mr]||[]).unshift(J(t)),i)throw a;return op(a,t,"R3InjectorError",this.source)}else throw a}finally{Z(s),ye(i)}}resolveInjectorInitializers(){let t=E(null),n=ye(this),r=Z(void 0),o;try{let i=this.get(vr,Y,M.Self);for(let s of i)s()}finally{ye(n),Z(r),E(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(J(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=B(t);let n=Mt(t)?t:B(t&&t.provide),r=vp(t);if(!Mt(t)&&t.multi===!0){let o=this.records.get(n);o||(o=vt(void 0,cr,!0),o.factory=()=>li(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=E(null);try{return n.value===Ya?Jc(J(t)):n.value===cr&&(n.value=Ya,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&Ep(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{E(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=B(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function di(e){let t=qr(e),n=t!==null?t.factory:Ze(e);if(n!==null)return n;if(e instanceof k)throw new C(204,!1);if(e instanceof Function)return yp(e);throw new C(204,!1)}function yp(e){if(e.length>0)throw new C(204,!1);let n=Wf(e);return n!==null?()=>n.factory(e):()=>new e}function vp(e){if(dl(e))return vt(void 0,e.useValue);{let t=pl(e);return vt(t,cr)}}function pl(e,t,n){let r;if(Mt(e)){let o=B(e);return Ze(o)||di(o)}else if(dl(e))r=()=>B(e.useValue);else if(gp(e))r=()=>e.useFactory(...li(e.deps||[]));else if(hp(e))r=(o,i)=>Le(B(e.useExisting),i!==void 0&&i&M.Optional?M.Optional:void 0);else{let o=B(e&&(e.useClass||e.provide));if(Ip(e))r=()=>new o(...li(e.deps));else return Ze(o)||di(o)}return r}function Jt(e){if(e.destroyed)throw new C(205,!1)}function vt(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Ip(e){return!!e.deps}function Ep(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Dp(e){return typeof e=="function"||typeof e=="object"&&e instanceof k}function fi(e,t){for(let n of e)Array.isArray(n)?fi(n,t):n&&Yc(n)?fi(n.\u0275providers,t):t(n)}function hl(e,t){let n;e instanceof tn?(Jt(e),n=e):n=new gr(e);let r,o=ye(n),i=Z(void 0);try{return t()}finally{ye(o),Z(i)}}function gl(){return Kc()!==void 0||qt()!=null}function ml(e){if(!gl())throw new C(-203,!1)}function wp(e){return typeof e=="function"}var we=0,I=1,m=2,q=3,ue=4,K=5,Ct=6,Er=7,H=8,Je=9,Ie=10,R=11,nn=12,Ja=13,At=14,oe=15,Ke=16,It=17,Ee=18,Gr=19,yl=20,Ae=21,Qo=22,Xe=23,ie=24,wt=25,F=26,vl=1;var et=7,Dr=8,xt=9,U=10;function Pe(e){return Array.isArray(e)&&typeof e[vl]=="object"}function be(e){return Array.isArray(e)&&e[vl]===!0}function xs(e){return(e.flags&4)!==0}function Pt(e){return e.componentOffset>-1}function Qr(e){return(e.flags&1)===1}function pe(e){return!!e.template}function wr(e){return(e[m]&512)!==0}function Lt(e){return(e[m]&256)===256}var pi=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Il(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var ax=(()=>{let e=()=>El;return e.ngInherit=!0,e})();function El(e){return e.type.prototype.ngOnChanges&&(e.setInput=Mp),bp}function bp(){let e=wl(this),t=e?.current;if(t){let n=e.previous;if(n===fe)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Mp(e,t,n,r,o){let i=this.declaredInputs[r],s=wl(e)||Cp(e,{previous:fe,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new pi(l&&l.currentValue,n,c===fe),Il(e,t,o,n)}var Dl="__ngSimpleChanges__";function wl(e){return e[Dl]||null}function Cp(e,t){return e[Dl]=t}var Ka=null;var N=function(e,t=null,n){Ka?.(e,t,n)},bl="svg",xp="math";function he(e){for(;Array.isArray(e);)e=e[we];return e}function Ml(e,t){return he(t[e])}function me(e,t){return he(t[e.index])}function _s(e,t){return e.data[t]}function Zr(e,t){return e[t]}function ge(e,t){let n=t[e];return Pe(n)?n:n[we]}function _p(e){return(e[m]&4)===4}function Ts(e){return(e[m]&128)===128}function Tp(e){return be(e[q])}function je(e,t){return t==null?null:e[t]}function Cl(e){e[It]=0}function xl(e){e[m]&1024||(e[m]|=1024,Ts(e)&&Ft(e))}function Np(e,t){for(;e>0;)t=t[At],e--;return t}function Yr(e){return!!(e[m]&9216||e[ie]?.dirty)}function hi(e){e[Ie].changeDetectionScheduler?.notify(8),e[m]&64&&(e[m]|=1024),Yr(e)&&Ft(e)}function Ft(e){e[Ie].changeDetectionScheduler?.notify(0);let t=tt(e);for(;t!==null&&!(t[m]&8192||(t[m]|=8192,!Ts(t)));)t=tt(t)}function _l(e,t){if(Lt(e))throw new C(911,!1);e[Ae]===null&&(e[Ae]=[]),e[Ae].push(t)}function Sp(e,t){if(e[Ae]===null)return;let n=e[Ae].indexOf(t);n!==-1&&e[Ae].splice(n,1)}function tt(e){let t=e[q];return be(t)?t[q]:t}function Ns(e){return e[Er]??=[]}function Ss(e){return e.cleanup??=[]}function kp(e,t,n,r){let o=Ns(t);o.push(n),e.firstCreatePass&&Ss(e).push(r,o.length-1)}var w={lFrame:Al(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var gi=!1;function Rp(){return w.lFrame.elementDepthCount}function Op(){w.lFrame.elementDepthCount++}function Ap(){w.lFrame.elementDepthCount--}function ks(){return w.bindingsEnabled}function Tl(){return w.skipHydrationRootTNode!==null}function Pp(e){return w.skipHydrationRootTNode===e}function Lp(){w.skipHydrationRootTNode=null}function g(){return w.lFrame.lView}function O(){return w.lFrame.tView}function cx(e){return w.lFrame.contextLView=e,e[H]}function lx(e){return w.lFrame.contextLView=null,e}function W(){let e=Nl();for(;e!==null&&e.type===64;)e=e.parent;return e}function Nl(){return w.lFrame.currentTNode}function Fp(){let e=w.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function at(e,t){let n=w.lFrame;n.currentTNode=e,n.isParent=t}function Rs(){return w.lFrame.isParent}function Os(){w.lFrame.isParent=!1}function jp(){return w.lFrame.contextLView}function Sl(){return gi}function br(e){let t=gi;return gi=e,t}function se(){let e=w.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Vp(){return w.lFrame.bindingIndex}function Hp(e){return w.lFrame.bindingIndex=e}function ct(){return w.lFrame.bindingIndex++}function As(e){let t=w.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Bp(){return w.lFrame.inI18n}function $p(e,t){let n=w.lFrame;n.bindingIndex=n.bindingRootIndex=e,mi(t)}function Up(){return w.lFrame.currentDirectiveIndex}function mi(e){w.lFrame.currentDirectiveIndex=e}function qp(e){let t=w.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function kl(){return w.lFrame.currentQueryIndex}function Ps(e){w.lFrame.currentQueryIndex=e}function Wp(e){let t=e[I];return t.type===2?t.declTNode:t.type===1?e[K]:null}function Rl(e,t,n){if(n&M.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&M.Host);)if(o=Wp(i),o===null||(i=i[At],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=w.lFrame=Ol();return r.currentTNode=t,r.lView=e,!0}function Ls(e){let t=Ol(),n=e[I];w.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Ol(){let e=w.lFrame,t=e===null?null:e.child;return t===null?Al(e):t}function Al(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Pl(){let e=w.lFrame;return w.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Ll=Pl;function Fs(){let e=Pl();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function zp(e){return(w.lFrame.contextLView=Np(e,w.lFrame.contextLView))[H]}function Me(){return w.lFrame.selectedIndex}function nt(e){w.lFrame.selectedIndex=e}function hn(){let e=w.lFrame;return _s(e.tView,e.selectedIndex)}function ux(){w.lFrame.currentNamespace=bl}function Gp(){return w.lFrame.currentNamespace}var Fl=!0;function Jr(){return Fl}function Kr(e){Fl=e}function Qp(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=El(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function js(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function lr(e,t,n){jl(e,t,3,n)}function ur(e,t,n,r){(e[m]&3)===n&&jl(e,t,n,r)}function Zo(e,t){let n=e[m];(n&3)===t&&(n&=16383,n+=1,e[m]=n)}function jl(e,t,n,r){let o=r!==void 0?e[It]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[It]+=65536),(a<i||i==-1)&&(Zp(e,n,t,c),e[It]=(e[It]&**********)+c+2),c++}function Xa(e,t){N(4,e,t);let n=E(null);try{t.call(e)}finally{E(n),N(5,e,t)}}function Zp(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[m]>>14<e[It]>>16&&(e[m]&3)===t&&(e[m]+=16384,Xa(a,i)):Xa(a,i)}var bt=-1,rt=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function Yp(e){return(e.flags&8)!==0}function Jp(e){return(e.flags&16)!==0}function Kp(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];Xp(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Vl(e){return e===3||e===4||e===6}function Xp(e){return e.charCodeAt(0)===64}function _t(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?ec(e,n,o,null,t[++r]):ec(e,n,o,null,null))}}return e}function ec(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function Hl(e){return e!==bt}function Mr(e){return e&32767}function eh(e){return e>>16}function Cr(e,t){let n=eh(e),r=t;for(;n>0;)r=r[At],n--;return r}var yi=!0;function xr(e){let t=yi;return yi=e,t}var th=256,Bl=th-1,$l=5,nh=0,de={};function rh(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Xt)&&(r=n[Xt]),r==null&&(r=n[Xt]=nh++);let o=r&Bl,i=1<<o;t.data[e+(o>>$l)]|=i}function _r(e,t){let n=Ul(e,t);if(n!==-1)return n;let r=t[I];r.firstCreatePass&&(e.injectorIndex=t.length,Yo(r.data,e),Yo(t,null),Yo(r.blueprint,null));let o=Vs(e,t),i=e.injectorIndex;if(Hl(o)){let s=Mr(o),a=Cr(o,t),c=a[I].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Yo(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Ul(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Vs(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Ql(o),r===null)return bt;if(n++,o=o[At],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return bt}function vi(e,t,n){rh(e,t,n)}function oh(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Vl(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function ql(e,t,n){if(n&M.Optional||e!==void 0)return e;ws(t,"NodeInjector")}function Wl(e,t,n,r){if(n&M.Optional&&r===void 0&&(r=null),(n&(M.Self|M.Host))===0){let o=e[Je],i=Z(void 0);try{return o?o.get(t,r,n&M.Optional):Xc(t,r,n&M.Optional)}finally{Z(i)}}return ql(r,t,n)}function zl(e,t,n,r=M.Default,o){if(e!==null){if(t[m]&2048&&!(r&M.Self)){let s=ch(e,t,n,r,de);if(s!==de)return s}let i=Gl(e,t,n,r,de);if(i!==de)return i}return Wl(t,n,r,o)}function Gl(e,t,n,r,o){let i=sh(n);if(typeof i=="function"){if(!Rl(t,e,r))return r&M.Host?ql(o,n,r):Wl(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&M.Optional))ws(n);else return s}finally{Ll()}}else if(typeof i=="number"){let s=null,a=Ul(e,t),c=bt,l=r&M.Host?t[oe][K]:null;for((a===-1||r&M.SkipSelf)&&(c=a===-1?Vs(e,t):t[a+8],c===bt||!nc(r,!1)?a=-1:(s=t[I],a=Mr(c),t=Cr(c,t)));a!==-1;){let u=t[I];if(tc(i,a,u.data)){let f=ih(a,t,n,s,r,l);if(f!==de)return f}c=t[a+8],c!==bt&&nc(r,t[I].data[a+8]===l)&&tc(i,a,t)?(s=u,a=Mr(c),t=Cr(c,t)):a=-1}}return o}function ih(e,t,n,r,o,i){let s=t[I],a=s.data[e+8],c=r==null?Pt(a)&&yi:r!=s&&(a.type&3)!==0,l=o&M.Host&&i===a,u=dr(a,s,n,c,l);return u!==null?rn(t,s,u,a,o):de}function dr(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,f=r?a:a+u,p=o?a+u:l;for(let d=f;d<p;d++){let h=s[d];if(d<c&&n===h||d>=c&&h.type===n)return d}if(o){let d=s[c];if(d&&pe(d)&&d.type===n)return c}return null}function rn(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof rt){let a=i;a.resolving&&Jc(Jf(s[n]));let c=xr(a.canSeeViewProviders);a.resolving=!0;let l,u=a.injectImpl?Z(a.injectImpl):null,f=Rl(e,r,M.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&Qp(n,s[n],t)}finally{u!==null&&Z(u),xr(c),a.resolving=!1,Ll()}}return i}function sh(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Xt)?e[Xt]:void 0;return typeof t=="number"?t>=0?t&Bl:ah:t}function tc(e,t,n){let r=1<<e;return!!(n[t+(e>>$l)]&r)}function nc(e,t){return!(e&M.Self)&&!(e&M.Host&&t)}var Ge=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return zl(this._tNode,this._lView,t,Wr(r),n)}};function ah(){return new Ge(W(),g())}function dx(e){return fn(()=>{let t=e.prototype.constructor,n=t[hr]||Ii(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[hr]||Ii(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Ii(e){return Gc(e)?()=>{let t=Ii(B(e));return t&&t()}:Ze(e)}function ch(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[m]&2048&&!wr(s);){let a=Gl(i,s,n,r|M.Self,de);if(a!==de)return a;let c=i.parent;if(!c){let l=s[yl];if(l){let u=l.get(n,de,r);if(u!==de)return u}c=Ql(s),s=s[At]}i=c}return o}function Ql(e){let t=e[I],n=t.type;return n===2?t.declTNode:n===1?e[K]:null}function fx(e){return oh(W(),e)}function rc(e,t=null,n=null,r){let o=Zl(e,t,n,r);return o.resolveInjectorInitializers(),o}function Zl(e,t=null,n=null,r,o=new Set){let i=[n||Y,fp(e)];return r=r||(typeof e=="object"?void 0:J(e)),new tn(i,t||Cs(),r||null,o)}var ot=class e{static THROW_IF_NOT_FOUND=ze;static NULL=new Ir;static create(t,n){if(Array.isArray(t))return rc({name:""},n,t,"");{let r=t.name??"";return rc({name:r},t.parent,t.providers,r)}}static \u0275prov=$({token:e,providedIn:"any",factory:()=>Le(ol)});static __NG_ELEMENT_ID__=-1};var lh=new k("");lh.__NG_ELEMENT_ID__=e=>{let t=W();if(t===null)throw new C(204,!1);if(t.type&2)return t.value;if(e&M.Optional)return null;throw new C(204,!1)};var Yl=!1,gn=(()=>{class e{static __NG_ELEMENT_ID__=uh;static __NG_ENV_ID__=n=>n}return e})(),Tr=class extends gn{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return Lt(n)?(t(),()=>{}):(_l(n,t),()=>Sp(n,t))}};function uh(){return new Tr(g())}var it=class{},Hs=new k("",{providedIn:"root",factory:()=>!1});var Jl=new k(""),Kl=new k(""),Xr=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new Wt(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=$({token:e,providedIn:"root",factory:()=>new e})}return e})();var Ei=class extends Te{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,gl()&&(this.destroyRef=x(gn,{optional:!0})??void 0,this.pendingTasks=x(Xr,{optional:!0})??void 0)}emit(t){let n=E(null);try{super.next(t)}finally{E(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof L&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},Oe=Ei;function on(...e){}function Xl(e){let t,n;function r(){e=on;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function oc(e){return queueMicrotask(()=>e()),()=>{e=on}}var Bs="isAngularZone",Nr=Bs+"_ID",dh=0,re=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new Oe(!1);onMicrotaskEmpty=new Oe(!1);onStable=new Oe(!1);onError=new Oe(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Yl}=t;if(typeof Zone>"u")throw new C(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,hh(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Bs)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new C(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new C(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,fh,on,on);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},fh={};function $s(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function ph(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Xl(()=>{e.callbackScheduled=!1,Di(e),e.isCheckStableRunning=!0,$s(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Di(e)}function hh(e){let t=()=>{ph(e)},n=dh++;e._inner=e._inner.fork({name:"angular",properties:{[Bs]:!0,[Nr]:n,[Nr+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(gh(c))return r.invokeTask(i,s,a,c);try{return ic(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),sc(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return ic(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!mh(c)&&t(),sc(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Di(e),$s(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Di(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function ic(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function sc(e){e._nesting--,$s(e)}var wi=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new Oe;onMicrotaskEmpty=new Oe;onStable=new Oe;onError=new Oe;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function gh(e){return eu(e,"__ignore_ng_zone__")}function mh(e){return eu(e,"__scheduler_tick__")}function eu(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Ve=class{_console=console;handleError(t){this._console.error("ERROR",t)}},yh=new k("",{providedIn:"root",factory:()=>{let e=x(re),t=x(Ve);return n=>e.runOutsideAngular(()=>t.handleError(n))}}),bi=class{destroyed=!1;listeners=null;errorHandler=x(Ve,{optional:!0});destroyRef=x(gn);constructor(){this.destroyRef.onDestroy(()=>{this.destroyed=!0,this.listeners=null})}subscribe(t){if(this.destroyed)throw new C(953,!1);return(this.listeners??=[]).push(t),{unsubscribe:()=>{let n=this.listeners?.indexOf(t);n!==void 0&&n!==-1&&this.listeners?.splice(n,1)}}}emit(t){if(this.destroyed){console.warn($c(953,!1));return}if(this.listeners===null)return;let n=E(null);try{for(let r of this.listeners)try{r(t)}catch(o){this.errorHandler?.handleError(o)}}finally{E(n)}}};function ac(e,t){return qc(e,t)}function vh(e){return qc(Ur,e)}var px=(ac.required=vh,ac);function Ih(){return jt(W(),g())}function jt(e,t){return new eo(me(e,t))}var eo=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Ih}return e})();function Eh(e){return e instanceof eo?e.nativeElement:e}function Dh(e){return typeof e=="function"&&e[ne]!==void 0}function hx(e,t){let n=_o(e,t?.equal),r=n[ne];return n.set=o=>ft(r,o),n.update=o=>To(r,o),n.asReadonly=tu.bind(n),n}function tu(){let e=this[ne];if(e.readonlyFn===void 0){let t=()=>this();t[ne]=e,e.readonlyFn=t}return e.readonlyFn}function nu(e){return Dh(e)&&typeof e.set=="function"}function wh(){return this._results[Symbol.iterator]()}var Mi=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new Te}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=ap(t);(this._changesDetected=!sp(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=wh};function ru(e){return(e.flags&128)===128}var ou=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(ou||{}),iu=new Map,bh=0;function Mh(){return bh++}function Ch(e){iu.set(e[Gr],e)}function Ci(e){iu.delete(e[Gr])}var cc="__ngContext__";function Vt(e,t){Pe(t)?(e[cc]=t[Gr],Ch(t)):e[cc]=t}function su(e){return cu(e[nn])}function au(e){return cu(e[ue])}function cu(e){for(;e!==null&&!be(e);)e=e[ue];return e}var xi;function gx(e){xi=e}function lu(){if(xi!==void 0)return xi;if(typeof document<"u")return document;throw new C(210,!1)}var xh=new k("",{providedIn:"root",factory:()=>_h}),_h="ng",Th=new k(""),mx=new k("",{providedIn:"platform",factory:()=>"unknown"});var yx=new k(""),vx=new k("",{providedIn:"root",factory:()=>lu().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Nh="h",Sh="b";var uu=!1,kh=new k("",{providedIn:"root",factory:()=>uu});var Us=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Us||{}),to=new k(""),lc=new Set;function mn(e){lc.has(e)||(lc.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var qs=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Rh}return e})();function Rh(){return new qs(g(),W())}var Et=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Et||{}),du=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=$({token:e,providedIn:"root",factory:()=>new e})}return e})(),Oh=[Et.EarlyRead,Et.Write,Et.MixedReadWrite,Et.Read],Ah=(()=>{class e{ngZone=x(re);scheduler=x(it);errorHandler=x(Ve,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){x(to,{optional:!0})}execute(){let n=this.sequences.size>0;n&&N(16),this.executing=!0;for(let r of Oh)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&N(17)}register(n){let{view:r}=n;r!==void 0?((r[wt]??=[]).push(n),Ft(r),r[m]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Us.AFTER_NEXT_RENDER,n):n()}static \u0275prov=$({token:e,providedIn:"root",factory:()=>new e})}return e})(),_i=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[wt];t&&(this.view[wt]=t.filter(n=>n!==this))}};function Ph(e,t){!t?.injector&&ml(Ph);let n=t?.injector??x(ot);return mn("NgAfterNextRender"),Fh(e,n,t,!0)}function Lh(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Fh(e,t,n,r){let o=t.get(du);o.impl??=t.get(Ah);let i=t.get(to,null,{optional:!0}),s=n?.phase??Et.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(gn):null,c=t.get(qs,null,{optional:!0}),l=new _i(o.impl,Lh(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(l),l}var jh=()=>null;function fu(e,t,n=!1){return jh(e,t,n)}function pu(e,t){let n=e.contentQueries;if(n!==null){let r=E(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Ps(i),a.contentQueries(2,t[s],s)}}}finally{E(r)}}}function Ti(e,t,n){Ps(0);let r=E(null);try{t(e,n)}finally{E(r)}}function Ws(e,t,n){if(xs(t)){let r=E(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{E(r)}}}var sn=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(sn||{}),or;function Vh(){if(or===void 0&&(or=null,Re.trustedTypes))try{or=Re.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return or}function no(e){return Vh()?.createHTML(e)||e}var ir;function hu(){if(ir===void 0&&(ir=null,Re.trustedTypes))try{ir=Re.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ir}function uc(e){return hu()?.createHTML(e)||e}function dc(e){return hu()?.createScriptURL(e)||e}var De=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Bc})`}},Ni=class extends De{getTypeName(){return"HTML"}},Si=class extends De{getTypeName(){return"Style"}},ki=class extends De{getTypeName(){return"Script"}},Ri=class extends De{getTypeName(){return"URL"}},Oi=class extends De{getTypeName(){return"ResourceURL"}};function Ht(e){return e instanceof De?e.changingThisBreaksApplicationSecurity:e}function zs(e,t){let n=Hh(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Bc})`)}return n===t}function Hh(e){return e instanceof De&&e.getTypeName()||null}function Ix(e){return new Ni(e)}function Ex(e){return new Si(e)}function Dx(e){return new ki(e)}function wx(e){return new Ri(e)}function bx(e){return new Oi(e)}function Bh(e){let t=new Pi(e);return $h()?new Ai(t):t}var Ai=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(no(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},Pi=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=no(t),n}};function $h(){try{return!!new window.DOMParser().parseFromString(no(""),"text/html")}catch{return!1}}var Uh=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function gu(e){return e=String(e),e.match(Uh)?e:"unsafe:"+e}function Ce(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function yn(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var mu=Ce("area,br,col,hr,img,wbr"),yu=Ce("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),vu=Ce("rp,rt"),qh=yn(vu,yu),Wh=yn(yu,Ce("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),zh=yn(vu,Ce("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),fc=yn(mu,Wh,zh,qh),Iu=Ce("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Gh=Ce("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Qh=Ce("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Zh=yn(Iu,Gh,Qh),Yh=Ce("script,style,template"),Li=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=Xh(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=Kh(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=pc(t).toLowerCase();if(!fc.hasOwnProperty(n))return this.sanitizedSomething=!0,!Yh.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Zh.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Iu[a]&&(c=gu(c)),this.buf.push(" ",s,'="',hc(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=pc(t).toLowerCase();fc.hasOwnProperty(n)&&!mu.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(hc(t))}};function Jh(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Kh(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw Eu(t);return t}function Xh(e){let t=e.firstChild;if(t&&Jh(e,t))throw Eu(t);return t}function pc(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function Eu(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var eg=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,tg=/([^\#-~ |!])/g;function hc(e){return e.replace(/&/g,"&amp;").replace(eg,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(tg,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var sr;function ng(e,t){let n=null;try{sr=sr||Bh(e);let r=t?String(t):"";n=sr.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=sr.getInertBodyElement(r)}while(r!==i);let a=new Li().sanitizeChildren(gc(n)||n);return no(a)}finally{if(n){let r=gc(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function gc(e){return"content"in e&&rg(e)?e.content:null}function rg(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var ro=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(ro||{});function Mx(e){let t=Gs();return t?uc(t.sanitize(ro.HTML,e)||""):zs(e,"HTML")?uc(Ht(e)):ng(lu(),Qe(e))}function og(e){let t=Gs();return t?t.sanitize(ro.URL,e)||"":zs(e,"URL")?Ht(e):gu(Qe(e))}function ig(e){let t=Gs();if(t)return dc(t.sanitize(ro.RESOURCE_URL,e)||"");if(zs(e,"ResourceURL"))return dc(Ht(e));throw new C(904,!1)}function sg(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?ig:og}function Cx(e,t,n){return sg(t,n)(e)}function Gs(){let e=g();return e&&e[Ie].sanitizer}var ag=/^>|^->|<!--|-->|--!>|<!-$/g,cg=/(<|>)/g,lg="\u200B$1\u200B";function ug(e){return e.replace(ag,t=>t.replace(cg,lg))}function xx(e){return e.ownerDocument.defaultView}function _x(e){return e.ownerDocument}function Du(e){return e instanceof Function?e():e}function dg(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var wu="ng-template";function fg(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&dg(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Qs(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Qs(e){return e.type===4&&e.value!==wu}function pg(e,t,n){let r=e.type===4&&!n?wu:e.value;return t===r}function hg(e,t,n){let r=4,o=e.attrs,i=o!==null?yg(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!le(r)&&!le(c))return!1;if(s&&le(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!pg(e,c,n)||c===""&&t.length===1){if(le(r))return!1;s=!0}}else if(r&8){if(o===null||!fg(e,o,c,n)){if(le(r))return!1;s=!0}}else{let l=t[++a],u=gg(c,o,Qs(e),n);if(u===-1){if(le(r))return!1;s=!0;continue}if(l!==""){let f;if(u>i?f="":f=o[u+1].toLowerCase(),r&2&&l!==f){if(le(r))return!1;s=!0}}}}return le(r)||s}function le(e){return(e&1)===0}function gg(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return vg(t,e)}function bu(e,t,n=!1){for(let r=0;r<t.length;r++)if(hg(e,t[r],n))return!0;return!1}function mg(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function yg(e){for(let t=0;t<e.length;t++){let n=e[t];if(Vl(n))return t}return e.length}function vg(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Ig(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function mc(e,t){return e?":not("+t.trim()+")":t}function Eg(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!le(s)&&(t+=mc(i,o),o=""),r=s,i=i||!le(r);n++}return o!==""&&(t+=mc(i,o)),t}function Dg(e){return e.map(Eg).join(",")}function wg(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!le(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var X={};function bg(e,t){return e.createText(t)}function Mg(e,t,n){e.setValue(t,n)}function Cg(e,t){return e.createComment(ug(t))}function Mu(e,t,n){return e.createElement(t,n)}function Sr(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Cu(e,t,n){e.appendChild(t,n)}function yc(e,t,n,r,o){r!==null?Sr(e,t,n,r,o):Cu(e,t,n)}function xg(e,t,n){e.removeChild(null,t,n)}function _g(e,t,n){e.setAttribute(t,"style",n)}function Tg(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function xu(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Kp(e,t,r),o!==null&&Tg(e,t,o),i!==null&&_g(e,t,i)}function Zs(e,t,n,r,o,i,s,a,c,l,u){let f=F+r,p=f+o,d=Ng(f,p),h=typeof l=="function"?l():l;return d[I]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:u}}function Ng(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:X);return n}function Sg(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Zs(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Ys(e,t,n,r,o,i,s,a,c,l,u){let f=t.blueprint.slice();return f[we]=o,f[m]=r|4|128|8|64|1024,(l!==null||e&&e[m]&2048)&&(f[m]|=2048),Cl(f),f[q]=f[At]=e,f[H]=n,f[Ie]=s||e&&e[Ie],f[R]=a||e&&e[R],f[Je]=c||e&&e[Je]||null,f[K]=i,f[Gr]=Mh(),f[Ct]=u,f[yl]=l,f[oe]=t.type==2?e[oe]:f,f}function kg(e,t,n){let r=me(t,e),o=Sg(n),i=e[Ie].rendererFactory,s=Js(e,Ys(e,o,null,_u(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function _u(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Tu(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Js(e,t){return e[nn]?e[Ja][ue]=t:e[nn]=t,e[Ja]=t,t}function Tx(e=1){Nu(O(),g(),Me()+e,!1)}function Nu(e,t,n,r){if(!r)if((t[m]&3)===3){let i=e.preOrderCheckHooks;i!==null&&lr(t,i,n)}else{let i=e.preOrderHooks;i!==null&&ur(t,i,0,n)}nt(n)}var oo=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(oo||{});function Fi(e,t,n,r){let o=E(null);try{let[i,s,a]=e.inputs[n],c=null;(s&oo.SignalBased)!==0&&(c=t[i][ne]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Il(t,c,i,r)}finally{E(o)}}function Su(e,t,n,r,o){let i=Me(),s=r&2;try{nt(-1),s&&t.length>F&&Nu(e,t,F,!1),N(s?2:0,o),n(r,o)}finally{nt(i),N(s?3:1,o)}}function io(e,t,n){Fg(e,t,n),(n.flags&64)===64&&jg(e,t,n)}function Ks(e,t,n=me){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Rg(e,t,n,r){let i=r.get(kh,uu)||n===sn.ShadowDom,s=e.selectRootElement(t,i);return Og(s),s}function Og(e){Ag(e)}var Ag=()=>null;function Pg(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function so(e,t,n,r,o,i,s,a){if(!a&&ea(t,e,n,r,o)){Pt(t)&&Lg(n,t.index);return}if(t.type&3){let c=me(t,n);r=Pg(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function Lg(e,t){let n=ge(t,e);n[m]&16||(n[m]|=64)}function Fg(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Pt(n)&&kg(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||_r(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=rn(t,e,s,n);if(Vt(c,t),i!==null&&$g(t,s-r,c,a,n,i),pe(a)){let l=ge(n.index,t);l[H]=rn(t,e,s,n)}}}function jg(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Up();try{nt(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];mi(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Vg(c,l)}}finally{nt(-1),mi(s)}}function Vg(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Xs(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];bu(t,i.selectors,!1)&&(r??=[],pe(i)?r.unshift(i):r.push(i))}return r}function Hg(e,t,n,r,o,i){let s=me(e,t);Bg(t[R],s,i,e.value,n,r,o)}function Bg(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Qe(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function $g(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];Fi(r,n,c,l)}}function Ug(e,t){let n=e[Je],r=n?n.get(Ve,null):null;r&&r.handleError(t)}function ea(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],f=t.data[l];Fi(f,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];Fi(u,l,r,o),a=!0}return a}function qg(e,t){let n=ge(t,e),r=n[I];Wg(r,n);let o=n[we];o!==null&&n[Ct]===null&&(n[Ct]=fu(o,n[Je])),N(18),ta(r,n,n[H]),N(19,n[H])}function Wg(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function ta(e,t,n){Ls(t);try{let r=e.viewQuery;r!==null&&Ti(1,r,n);let o=e.template;o!==null&&Su(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Ee]?.finishViewCreation(e),e.staticContentQueries&&pu(e,t),e.staticViewQueries&&Ti(2,e.viewQuery,n);let i=e.components;i!==null&&zg(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[m]&=-5,Fs()}}function zg(e,t){for(let n=0;n<t.length;n++)qg(e,t[n])}function vn(e,t,n,r){let o=E(null);try{let i=t.tView,a=e[m]&4096?4096:16,c=Ys(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[Ke]=l;let u=e[Ee];return u!==null&&(c[Ee]=u.createEmbeddedView(i)),ta(i,c,n),c}finally{E(o)}}function Tt(e,t){return!t||t.firstChild===null||ru(e)}var Gg;function na(e,t){return Gg(e,t)}var ji=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(ji||{});function ra(e){return(e.flags&32)===32}function Dt(e,t,n,r,o){if(r!=null){let i,s=!1;be(r)?i=r:Pe(r)&&(s=!0,r=r[we]);let a=he(r);e===0&&n!==null?o==null?Cu(t,n,a):Sr(t,n,a,o||null,!0):e===1&&n!==null?Sr(t,n,a,o||null,!0):e===2?xg(t,a,s):e===3&&t.destroyNode(a),i!=null&&rm(t,e,i,n,o)}}function Qg(e,t){ku(e,t),t[we]=null,t[K]=null}function Zg(e,t,n,r,o,i){r[we]=o,r[K]=t,lo(e,r,n,1,o,i)}function ku(e,t){t[Ie].changeDetectionScheduler?.notify(9),lo(e,t,t[R],2,null,null)}function Yg(e){let t=e[nn];if(!t)return Jo(e[I],e);for(;t;){let n=null;if(Pe(t))n=t[nn];else{let r=t[U];r&&(n=r)}if(!n){for(;t&&!t[ue]&&t!==e;)Pe(t)&&Jo(t[I],t),t=t[q];t===null&&(t=e),Pe(t)&&Jo(t[I],t),n=t&&t[ue]}t=n}}function oa(e,t){let n=e[xt],r=n.indexOf(t);n.splice(r,1)}function ao(e,t){if(Lt(t))return;let n=t[R];n.destroyNode&&lo(e,t,n,3,null,null),Yg(t)}function Jo(e,t){if(Lt(t))return;let n=E(null);try{t[m]&=-129,t[m]|=256,t[ie]&&Ut(t[ie]),Kg(e,t),Jg(e,t),t[I].type===1&&t[R].destroy();let r=t[Ke];if(r!==null&&be(t[q])){r!==t[q]&&oa(r,t);let o=t[Ee];o!==null&&o.detachView(e)}Ci(t)}finally{E(n)}}function Jg(e,t){let n=e.cleanup,r=t[Er];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Er]=null);let o=t[Ae];if(o!==null){t[Ae]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Xe];if(i!==null){t[Xe]=null;for(let s of i)s.destroy()}}function Kg(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof rt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];N(4,a,c);try{c.call(a)}finally{N(5,a,c)}}else{N(4,o,i);try{i.call(o)}finally{N(5,o,i)}}}}}function Ru(e,t,n){return Xg(e,t.parent,n)}function Xg(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[we];if(Pt(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===sn.None||o===sn.Emulated)return null}return me(r,n)}function Ou(e,t,n){return tm(e,t,n)}function em(e,t,n){return e.type&40?me(e,n):null}var tm=em,vc;function co(e,t,n,r){let o=Ru(e,r,t),i=t[R],s=r.parent||t[K],a=Ou(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)yc(i,o,n[c],a,!1);else yc(i,o,n,a,!1);vc!==void 0&&vc(i,r,t,n,o)}function Kt(e,t){if(t!==null){let n=t.type;if(n&3)return me(t,e);if(n&4)return Vi(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Kt(e,r);{let o=e[t.index];return be(o)?Vi(-1,o):he(o)}}else{if(n&128)return Kt(e,t.next);if(n&32)return na(t,e)()||he(e[t.index]);{let r=Au(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=tt(e[oe]);return Kt(o,r)}else return Kt(e,t.next)}}}return null}function Au(e,t){if(t!==null){let r=e[oe][K],o=t.projection;return r.projection[o]}return null}function Vi(e,t){let n=U+e+1;if(n<t.length){let r=t[n],o=r[I].firstChild;if(o!==null)return Kt(r,o)}return t[et]}function ia(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Vt(he(a),r),n.flags|=2),!ra(n))if(c&8)ia(e,t,n.child,r,o,i,!1),Dt(t,e,o,a,i);else if(c&32){let l=na(n,r),u;for(;u=l();)Dt(t,e,o,u,i);Dt(t,e,o,a,i)}else c&16?Pu(e,t,r,n,o,i):Dt(t,e,o,a,i);n=s?n.projectionNext:n.next}}function lo(e,t,n,r,o,i){ia(n,r,e.firstChild,t,o,i,!1)}function nm(e,t,n){let r=t[R],o=Ru(e,n,t),i=n.parent||t[K],s=Ou(i,n,t);Pu(r,0,t,n,o,s)}function Pu(e,t,n,r,o,i){let s=n[oe],c=s[K].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];Dt(t,e,o,u,i)}else{let l=c,u=s[q];ru(r)&&(l.flags|=128),ia(e,t,l,u,o,i,!0)}}function rm(e,t,n,r,o){let i=n[et],s=he(n);i!==s&&Dt(t,e,r,i,o);for(let a=U;a<n.length;a++){let c=n[a];lo(c[I],c,e,t,r,i)}}function om(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:ji.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=ji.Important),e.setStyle(n,r,o,i))}}function kr(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(he(i)),be(i)&&im(i,r);let s=n.type;if(s&8)kr(e,t,n.child,r);else if(s&32){let a=na(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Au(t,n);if(Array.isArray(a))r.push(...a);else{let c=tt(t[oe]);kr(c[I],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function im(e,t){for(let n=U;n<e.length;n++){let r=e[n],o=r[I].firstChild;o!==null&&kr(r[I],r,o,t)}e[et]!==e[we]&&t.push(e[et])}function Lu(e){if(e[wt]!==null){for(let t of e[wt])t.impl.addSequence(t);e[wt].length=0}}var Fu=[];function sm(e){return e[ie]??am(e)}function am(e){let t=Fu.pop()??Object.create(lm);return t.lView=e,t}function cm(e){e.lView[ie]!==e&&(e.lView=null,Fu.push(e))}var lm=te(ee({},ut),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Ft(e.lView)},consumerOnSignalRead(){this.lView[ie]=this}});function um(e){let t=e[ie]??Object.create(dm);return t.lView=e,t}var dm=te(ee({},ut),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=tt(e.lView);for(;t&&!ju(t[I]);)t=tt(t);t&&xl(t)},consumerOnSignalRead(){this.lView[ie]=this}});function ju(e){return e.type!==2}function Vu(e){if(e[Xe]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Xe])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[m]&8192)}}var fm=100;function Hu(e,t=!0,n=0){let o=e[Ie].rendererFactory,i=!1;i||o.begin?.();try{pm(e,n)}catch(s){throw t&&Ug(e,s),s}finally{i||o.end?.()}}function pm(e,t){let n=Sl();try{br(!0),Hi(e,t);let r=0;for(;Yr(e);){if(r===fm)throw new C(103,!1);r++,Hi(e,1)}}finally{br(n)}}function hm(e,t,n,r){if(Lt(t))return;let o=t[m],i=!1,s=!1;Ls(t);let a=!0,c=null,l=null;i||(ju(e)?(l=sm(t),c=$t(l)):Do()===null?(a=!1,l=um(t),c=$t(l)):t[ie]&&(Ut(t[ie]),t[ie]=null));try{Cl(t),Hp(e.bindingStartIndex),n!==null&&Su(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let d=e.preOrderCheckHooks;d!==null&&lr(t,d,null)}else{let d=e.preOrderHooks;d!==null&&ur(t,d,0,null),Zo(t,0)}if(s||gm(t),Vu(t),Bu(t,0),e.contentQueries!==null&&pu(e,t),!i)if(u){let d=e.contentCheckHooks;d!==null&&lr(t,d)}else{let d=e.contentHooks;d!==null&&ur(t,d,1),Zo(t,1)}ym(e,t);let f=e.components;f!==null&&Uu(t,f,0);let p=e.viewQuery;if(p!==null&&Ti(2,p,r),!i)if(u){let d=e.viewCheckHooks;d!==null&&lr(t,d)}else{let d=e.viewHooks;d!==null&&ur(t,d,2),Zo(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Qo]){for(let d of t[Qo])d();t[Qo]=null}i||(Lu(t),t[m]&=-73)}catch(u){throw i||Ft(t),u}finally{l!==null&&(Mn(l,c),a&&cm(l)),Fs()}}function Bu(e,t){for(let n=su(e);n!==null;n=au(n))for(let r=U;r<n.length;r++){let o=n[r];$u(o,t)}}function gm(e){for(let t=su(e);t!==null;t=au(t)){if(!(t[m]&2))continue;let n=t[xt];for(let r=0;r<n.length;r++){let o=n[r];xl(o)}}}function mm(e,t,n){N(18);let r=ge(t,e);$u(r,n),N(19,r[H])}function $u(e,t){Ts(e)&&Hi(e,t)}function Hi(e,t){let r=e[I],o=e[m],i=e[ie],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Cn(i)),s||=!1,i&&(i.dirty=!1),e[m]&=-9217,s)hm(r,e,r.template,e[H]);else if(o&8192){Vu(e),Bu(e,1);let a=r.components;a!==null&&Uu(e,a,1),Lu(e)}}function Uu(e,t,n){for(let r=0;r<t.length;r++)mm(e,t[r],n)}function ym(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)nt(~o);else{let i=o,s=n[++r],a=n[++r];$p(s,i);let c=t[i];N(24,c),a(2,c),N(25,c)}}}finally{nt(-1)}}function sa(e,t){let n=Sl()?64:1088;for(e[Ie].changeDetectionScheduler?.notify(t);e;){e[m]|=n;let r=tt(e);if(wr(e)&&!r)return e;e=r}return null}function qu(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Wu(e,t){let n=U+t;if(n<e.length)return e[n]}function In(e,t,n,r=!0){let o=t[I];if(vm(o,t,e,n),r){let s=Vi(n,e),a=t[R],c=a.parentNode(e[et]);c!==null&&Zg(o,e[K],a,t,c,s)}let i=t[Ct];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function zu(e,t){let n=an(e,t);return n!==void 0&&ao(n[I],n),n}function an(e,t){if(e.length<=U)return;let n=U+t,r=e[n];if(r){let o=r[Ke];o!==null&&o!==e&&oa(o,r),t>0&&(e[n-1][ue]=r[ue]);let i=yr(e,U+t);Qg(r[I],r);let s=i[Ee];s!==null&&s.detachView(i[I]),r[q]=null,r[ue]=null,r[m]&=-129}return r}function vm(e,t,n,r){let o=U+r,i=n.length;r>0&&(n[o-1][ue]=t),r<i-U?(t[ue]=n[o],rl(n,U+r,t)):(n.push(t),t[ue]=null),t[q]=n;let s=t[Ke];s!==null&&n!==s&&Gu(s,t);let a=t[Ee];a!==null&&a.insertView(e),hi(t),t[m]|=128}function Gu(e,t){let n=e[xt],r=t[q];if(Pe(r))e[m]|=2;else{let o=r[q][oe];t[oe]!==o&&(e[m]|=2)}n===null?e[xt]=[t]:n.push(t)}var cn=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[I];return kr(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[H]}set context(t){this._lView[H]=t}get destroyed(){return Lt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[q];if(be(t)){let n=t[Dr],r=n?n.indexOf(this):-1;r>-1&&(an(t,r),yr(n,r))}this._attachedToViewContainer=!1}ao(this._lView[I],this._lView)}onDestroy(t){_l(this._lView,t)}markForCheck(){sa(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[m]&=-129}reattach(){hi(this._lView),this._lView[m]|=128}detectChanges(){this._lView[m]|=1024,Hu(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new C(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=wr(this._lView),n=this._lView[Ke];n!==null&&!t&&oa(n,this._lView),ku(this._lView[I],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new C(902,!1);this._appRef=t;let n=wr(this._lView),r=this._lView[Ke];r!==null&&!n&&Gu(r,this._lView),hi(this._lView)}};var Rr=(()=>{class e{static __NG_ELEMENT_ID__=Dm}return e})(),Im=Rr,Em=class extends Im{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=vn(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new cn(o)}};function Dm(){return uo(W(),g())}function uo(e,t){return e.type&4?new Em(t,e,jt(e,t)):null}function En(e,t,n,r,o){let i=e.data[t];if(i===null)i=wm(e,t,n,r,o),Bp()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Fp();i.injectorIndex=s===null?-1:s.injectorIndex}return at(i,!0),i}function wm(e,t,n,r,o){let i=Nl(),s=Rs(),a=s?i:i&&i.parent,c=e.data[t]=Mm(e,a,n,t,r,o);return bm(e,c,i,s),c}function bm(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Mm(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Tl()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var kx=new RegExp(`^(\\d+)*(${Sh}|${Nh})*(.*)`);var Cm=()=>null;function Nt(e,t){return Cm(e,t)}var xm=class{},Qu=class{},Bi=class{resolveComponentFactory(t){throw Error(`No component factory found for ${J(t)}.`)}},fo=class{static NULL=new Bi},Or=class{},Px=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>_m()}return e})();function _m(){let e=g(),t=W(),n=ge(t.index,e);return(Pe(n)?n:e)[R]}var Tm=(()=>{class e{static \u0275prov=$({token:e,providedIn:"root",factory:()=>null})}return e})();var Ko={},$i=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Wr(r);let o=this.injector.get(t,Ko,r);return o!==Ko||n===Ko?o:this.parentInjector.get(t,n,r)}};function Ui(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=si(o,a);else if(i==2){let c=a,l=t[++s];r=si(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function po(e,t=M.Default){let n=g();if(n===null)return Le(e,t);let r=W();return zl(r,n,B(e),t)}function Lx(){let e="invalid";throw new Error(e)}function aa(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,l=null,u=Sm(s);u===null?a=s:[a,c,l]=u,Om(e,t,n,a,i,c,l)}i!==null&&r!==null&&Nm(n,r,i)}function Nm(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new C(-301,!1);r.push(t[o],i)}}function Sm(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&pe(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,km(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function km(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function Rm(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Om(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let d=r[p];!c&&pe(d)&&(c=!0,Rm(e,n,p)),vi(_r(n,t),e,d.type)}Vm(n,e.data.length,a);for(let p=0;p<a;p++){let d=r[p];d.providersResolver&&d.providersResolver(d)}let l=!1,u=!1,f=Tu(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let d=r[p];if(n.mergedAttrs=_t(n.mergedAttrs,d.hostAttrs),Pm(e,n,t,f,d),jm(f,d,o),s!==null&&s.has(d)){let[y,A]=s.get(d);n.directiveToIndex.set(d.type,[f,y+n.directiveStart,A+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let h=d.type.prototype;!l&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),f++}Am(e,n,i)}function Am(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Ic(0,t,o,r),Ic(1,t,o,r),Dc(t,r,!1);else{let i=n.get(o);Ec(0,t,i,r),Ec(1,t,i,r),Dc(t,r,!0)}}}function Ic(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Zu(t,i)}}function Ec(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Zu(t,s)}}function Zu(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Dc(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Qs(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function Pm(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Ze(o.type,!0)),s=new rt(i,pe(o),po);e.blueprint[r]=s,n[r]=s,Lm(e,t,r,Tu(e,n,o.hostVars,X),o)}function Lm(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;Fm(s)!=a&&s.push(a),s.push(n,r,i)}}function Fm(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function jm(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;pe(t)&&(n[""]=e)}}function Vm(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Yu(e,t,n,r,o,i,s,a){let c=t.consts,l=je(c,s),u=En(t,e,2,r,l);return i&&aa(t,n,u,je(c,a),o),u.mergedAttrs=_t(u.mergedAttrs,u.attrs),u.attrs!==null&&Ui(u,u.attrs,!1),u.mergedAttrs!==null&&Ui(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function Ju(e,t){js(e,t),xs(t)&&e.queries.elementEnd(t)}var Ar=class extends fo{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Ye(t);return new St(n,this.ngModule)}};function Hm(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&oo.SignalBased)!==0};return o&&(i.transform=o),i})}function Bm(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function $m(e,t,n){let r=t instanceof Fe?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new $i(n,r):n}function Um(e){let t=e.get(Or,null);if(t===null)throw new C(407,!1);let n=e.get(Tm,null),r=e.get(it,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function qm(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Mu(t,n,n==="svg"?bl:n==="math"?xp:null)}var St=class extends Qu{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Hm(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=Bm(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Dg(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){N(22);let i=E(null);try{let s=this.componentDef,a=r?["ng-version","19.2.10"]:wg(this.componentDef.selectors[0]),c=Zs(0,null,null,1,0,null,null,null,null,[a],null),l=$m(s,o||this.ngModule,t),u=Um(l),f=u.rendererFactory.createRenderer(null,s),p=r?Rg(f,r,s.encapsulation,l):qm(s,f),d=Ys(null,c,null,512|_u(s),null,null,u,f,l,null,fu(p,l,!0));d[F]=p,Ls(d);let h=null;try{let y=Yu(F,c,d,"#host",()=>[this.componentDef],!0,0);p&&(xu(f,p,y),Vt(p,d)),io(c,d,y),Ws(c,y,d),Ju(c,y),n!==void 0&&Wm(y,this.ngContentSelectors,n),h=ge(y.index,d),d[H]=h[H],ta(c,d,null)}catch(y){throw h!==null&&Ci(h),Ci(d),y}finally{N(23),Fs()}return new qi(this.componentType,d)}finally{E(i)}}},qi=class extends xm{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=_s(n[I],F),this.location=jt(this._tNode,n),this.instance=ge(this._tNode.index,n)[H],this.hostView=this.changeDetectorRef=new cn(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=ea(r,o[I],o,t,n);this.previousInputValues.set(t,n);let s=ge(r.index,o);sa(s,1)}get injector(){return new Ge(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Wm(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var ca=(()=>{class e{static __NG_ELEMENT_ID__=zm}return e})();function zm(){let e=W();return Xu(e,g())}var Gm=ca,Ku=class extends Gm{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return jt(this._hostTNode,this._hostLView)}get injector(){return new Ge(this._hostTNode,this._hostLView)}get parentInjector(){let t=Vs(this._hostTNode,this._hostLView);if(Hl(t)){let n=Cr(t,this._hostLView),r=Mr(t),o=n[I].data[r+8];return new Ge(o,n)}else return new Ge(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=wc(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-U}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Nt(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Tt(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!wp(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let c=s?t:new St(Ye(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let y=(s?l:this.parentInjector).get(Fe,null);y&&(i=y)}let u=Ye(c.componentType??{}),f=Nt(this._lContainer,u?.id??null),p=f?.firstChild??null,d=c.create(l,o,p,i);return this.insertImpl(d.hostView,a,Tt(this._hostTNode,f)),d}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Tp(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[q],l=new Ku(c,c[K],c[q]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return In(s,o,i,r),t.attachToViewContainerRef(),rl(Xo(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=wc(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=an(this._lContainer,n);r&&(yr(Xo(this._lContainer),n),ao(r[I],r))}detach(t){let n=this._adjustIndex(t,-1),r=an(this._lContainer,n);return r&&yr(Xo(this._lContainer),n)!=null?new cn(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function wc(e){return e[Dr]}function Xo(e){return e[Dr]||(e[Dr]=[])}function Xu(e,t){let n,r=t[e.index];return be(r)?n=r:(n=qu(r,t,null,e),t[e.index]=n,Js(t,n)),Zm(n,t,e,r),new Ku(n,e,t)}function Qm(e,t){let n=e[R],r=n.createComment(""),o=me(t,e),i=n.parentNode(o);return Sr(n,i,r,n.nextSibling(o),!1),r}var Zm=Km,Ym=()=>!1;function Jm(e,t,n){return Ym(e,t,n)}function Km(e,t,n,r){if(e[et])return;let o;n.type&8?o=he(r):o=Qm(t,n),e[et]=o}var Wi=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},zi=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)la(t,n).matches!==null&&this.queries[n].setDirty()}},Pr=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=sy(t):this.predicate=t}},Gi=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Qi=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,Xm(n,i)),this.matchTNodeWithReadOption(t,n,dr(n,t,i,!1,!1))}else r===Rr?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,dr(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===eo||o===ca||o===Rr&&n.type&4)this.addMatch(n.index,-2);else{let i=dr(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function Xm(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function ey(e,t){return e.type&11?jt(e,t):e.type&4?uo(e,t):null}function ty(e,t,n,r){return n===-1?ey(t,e):n===-2?ny(e,t,r):rn(e,e[I],n,t)}function ny(e,t,n){if(n===eo)return jt(t,e);if(n===Rr)return uo(t,e);if(n===ca)return Xu(t,e)}function ed(e,t,n,r){let o=t[Ee].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(ty(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Zi(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=ed(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let f=U;f<u.length;f++){let p=u[f];p[Ke]===p[q]&&Zi(p[I],p,l,r)}if(u[xt]!==null){let f=u[xt];for(let p=0;p<f.length;p++){let d=f[p];Zi(d[I],d,l,r)}}}}}return r}function ry(e,t){return e[Ee].queries[t].queryList}function td(e,t,n){let r=new Mi((n&4)===4);return kp(e,t,r,r.destroy),(t[Ee]??=new zi).queries.push(new Wi(r))-1}function oy(e,t,n){let r=O();return r.firstCreatePass&&(nd(r,new Pr(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),td(r,g(),t)}function iy(e,t,n,r){let o=O();if(o.firstCreatePass){let i=W();nd(o,new Pr(t,n,r),i.index),ay(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return td(o,g(),n)}function sy(e){return e.split(",").map(t=>t.trim())}function nd(e,t,n){e.queries===null&&(e.queries=new Gi),e.queries.track(new Qi(t,n))}function ay(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function la(e,t){return e.queries.getByIndex(t)}function cy(e,t){let n=e[I],r=la(n,t);return r.crossesNgTemplate?Zi(n,e,t,[]):ed(n,e,r,t)}function rd(e,t){let n=Object.create(Uc),r=new bi;n.value=e;function o(){return dt(n),bc(n.value),n.value}return o[ne]=n,o.asReadonly=tu.bind(o),o.set=i=>{n.equal(n.value,i)||(ft(n,i),r.emit(i))},o.update=i=>{bc(n.value),o.set(i(n.value))},o.subscribe=r.subscribe.bind(r),o.destroyRef=r.destroyRef,o}function bc(e){if(e===Ur)throw new C(952,!1)}function Mc(e,t){return rd(e,t)}function ly(e){return rd(Ur,e)}var jx=(Mc.required=ly,Mc);var ln=class{},uy=class{};var Yi=class extends ln{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Ar(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=sl(t);this._bootstrapComponents=Du(i.bootstrap),this._r3Injector=Zl(t,n,[{provide:ln,useValue:this},{provide:fo,useValue:this.componentFactoryResolver},...r],J(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Ji=class extends uy{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Yi(this.moduleType,t,[])}};var Lr=class extends ln{injector;componentFactoryResolver=new Ar(this);instance=null;constructor(t){super();let n=new tn([...t.providers,{provide:ln,useValue:this},{provide:fo,useValue:this.componentFactoryResolver}],t.parent||Cs(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function dy(e,t,n=null){return new Lr({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var fy=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=ll(!1,n.type),o=r.length>0?dy([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=$({token:e,providedIn:"environment",factory:()=>new e(Le(Fe))})}return e})();function Bx(e){return fn(()=>{let t=od(e),n=te(ee({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===ou.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(fy).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||sn.Emulated,styles:e.styles||Y,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&mn("NgStandalone"),id(n);let r=e.dependencies;return n.directiveDefs=Cc(r,!1),n.pipeDefs=Cc(r,!0),n.id=yy(n),n})}function py(e){return Ye(e)||al(e)}function hy(e){return e!==null}function $x(e){return fn(()=>({type:e.type,bootstrap:e.bootstrap||Y,declarations:e.declarations||Y,imports:e.imports||Y,exports:e.exports||Y,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function gy(e,t){if(e==null)return fe;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=oo.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function my(e){if(e==null)return fe;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function Ux(e){return fn(()=>{let t=od(e);return id(t),t})}function qx(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function od(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||fe,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Y,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:gy(e.inputs,t),outputs:my(e.outputs),debugInfo:null}}function id(e){e.features?.forEach(t=>t(e))}function Cc(e,t){if(!e)return null;let n=t?dp:py;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(hy)}function yy(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function vy(e){return Object.getPrototypeOf(e.prototype).constructor}function Iy(e){let t=vy(e.type),n=!0,r=[e];for(;t;){let o;if(pe(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new C(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=ei(e.inputs),s.declaredInputs=ei(e.declaredInputs),s.outputs=ei(e.outputs);let a=o.hostBindings;a&&My(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&wy(e,c),l&&by(e,l),Ey(e,o),Uf(e.outputs,o.outputs),pe(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Iy&&(n=!1)}}t=Object.getPrototypeOf(t)}Dy(r)}function Ey(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function Dy(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=_t(o.hostAttrs,n=_t(n,o.hostAttrs))}}function ei(e){return e===fe?{}:e===Y?[]:e}function wy(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function by(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function My(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Wx(e){let t=n=>{let r=Array.isArray(e);n.hostDirectives===null?(n.findHostDirectiveDefs=sd,n.hostDirectives=r?e.map(Ki):[e]):r?n.hostDirectives.unshift(...e.map(Ki)):n.hostDirectives.unshift(e)};return t.ngInherit=!0,t}function sd(e,t,n){if(e.hostDirectives!==null)for(let r of e.hostDirectives)if(typeof r=="function"){let o=r();for(let i of o)xc(Ki(i),t,n)}else xc(r,t,n)}function xc(e,t,n){let r=al(e.directive);Cy(r.declaredInputs,e.inputs),sd(r,t,n),n.set(r,e),t.push(r)}function Ki(e){return typeof e=="function"?{directive:B(e),inputs:fe,outputs:fe}:{directive:B(e.directive),inputs:_c(e.inputs),outputs:_c(e.outputs)}}function _c(e){if(e===void 0||e.length===0)return fe;let t={};for(let n=0;n<e.length;n+=2)t[e[n]]=e[n+1];return t}function Cy(e,t){for(let n in t)if(t.hasOwnProperty(n)){let r=t[n],o=e[n];e[r]=o}}function ad(e){return ua(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function xy(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function ua(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function xe(e,t,n){return e[t]=n}function ho(e,t){return e[t]}function G(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function kt(e,t,n,r){let o=G(e,t,n);return G(e,t+1,r)||o}function cd(e,t,n,r,o){let i=kt(e,t,n,r);return G(e,t+2,o)||i}function go(e,t,n,r,o,i){let s=kt(e,t,n,r);return kt(e,t+2,o,i)||s}function _y(e,t,n,r,o,i,s,a,c){let l=t.consts,u=En(t,e,4,s||null,a||null);ks()&&aa(t,n,u,je(l,c),Xs),u.mergedAttrs=_t(u.mergedAttrs,u.attrs),js(t,u);let f=u.tView=Zs(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),f.queries=t.queries.embeddedTView(u)),u}function Fr(e,t,n,r,o,i,s,a,c,l){let u=n+F,f=t.firstCreatePass?_y(u,t,e,r,o,i,s,a,c):t.data[u];at(f,!1);let p=Ny(t,e,f,n);Jr()&&co(t,e,p,f),Vt(p,e);let d=qu(p,e,p,f);return e[u]=d,Js(e,d),Jm(d,f,e),Qr(f)&&io(t,e,f),c!=null&&Ks(e,f,l),f}function Ty(e,t,n,r,o,i,s,a){let c=g(),l=O(),u=je(l.consts,i);return Fr(c,l,e,t,n,r,o,u,s,a),Ty}var Ny=Sy;function Sy(e,t,n,r){return Kr(!0),t[R].createComment("")}var zx=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var ky=new k("");var ld=(()=>{class e{static \u0275prov=$({token:e,providedIn:"root",factory:()=>new Xi})}return e})(),Xi=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function ud(e){return!!e&&typeof e.then=="function"}function Ry(e){return!!e&&typeof e.subscribe=="function"}var dd=new k("");function Gx(e){return cl([{provide:dd,multi:!0,useValue:e}])}var fd=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=x(dd,{optional:!0})??[];injector=x(ot);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=hl(this.injector,o);if(ud(i))n.push(i);else if(Ry(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Oy=new k("");function Ay(){xo(()=>{throw new C(600,!1)})}function Py(e){return e.isBoundToModule}var Ly=10;var un=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=x(yh);afterRenderManager=x(du);zonelessEnabled=x(Hs);rootEffectScheduler=x(ld);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new Te;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=x(Xr).hasPendingTasks.pipe(ke(n=>!n));constructor(){x(to,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=x(Fe);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=ot.NULL){N(10);let i=n instanceof Qu;if(!this._injector.get(fd).done){let d="";throw new C(405,d)}let a;i?a=n:a=this._injector.get(fo).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=Py(a)?void 0:this._injector.get(ln),l=r||a.selector,u=a.create(o,[],l,c),f=u.location.nativeElement,p=u.injector.get(ky,null);return p?.registerApplication(f),u.onDestroy(()=>{this.detachView(u.hostView),fr(this.components,u),p?.unregisterApplication(f)}),this._loadComponent(u),N(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){N(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Us.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new C(101,!1);let n=E(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,E(n),this.afterTick.next(),N(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Or,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<Ly;)N(14),this.synchronizeOnce(),N(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)Fy(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Yr(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;fr(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Oy,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>fr(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new C(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fr(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Fy(e,t,n,r){if(!n&&!Yr(e))return;Hu(e,t,n&&!r?0:1)}function jy(e,t,n,r){let o=g(),i=ct();if(G(o,i,t)){let s=O(),a=hn();Hg(a,o,e,t,n,r)}return jy}function pd(e,t,n,r){return G(e,ct(),n)?t+Qe(n)+r:X}function Vy(e,t,n,r,o,i){let s=Vp(),a=kt(e,s,n,o);return As(2),a?t+Qe(n)+r+Qe(o)+i:X}function ar(e,t){return e<<17|t<<2}function st(e){return e>>17&32767}function Hy(e){return(e&2)==2}function By(e,t){return e&131071|t<<17}function es(e){return e|2}function Rt(e){return(e&131068)>>2}function ti(e,t){return e&-131069|t<<2}function $y(e){return(e&1)===1}function ts(e){return e|1}function Uy(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=st(s),c=Rt(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let f=n;u=f[1],(u===null||pn(f,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let p=st(e[a+1]);e[r+1]=ar(p,a),p!==0&&(e[p+1]=ti(e[p+1],r)),e[a+1]=By(e[a+1],r)}else e[r+1]=ar(a,0),a!==0&&(e[a+1]=ti(e[a+1],r)),a=r;else e[r+1]=ar(c,0),a===0?a=r:e[c+1]=ti(e[c+1],r),c=r;l&&(e[r+1]=es(e[r+1])),Tc(e,u,r,!0),Tc(e,u,r,!1),qy(t,u,e,r,i),s=ar(a,c),i?t.classBindings=s:t.styleBindings=s}function qy(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&pn(i,t)>=0&&(n[r+1]=ts(n[r+1]))}function Tc(e,t,n,r){let o=e[n+1],i=t===null,s=r?st(o):Rt(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];Wy(c,t)&&(a=!0,e[s+1]=r?ts(l):es(l)),s=r?st(l):Rt(l)}a&&(e[n+1]=r?es(o):ts(o))}function Wy(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?pn(e,t)>=0:!1}var V={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function hd(e){return e.substring(V.key,V.keyEnd)}function zy(e){return e.substring(V.value,V.valueEnd)}function Gy(e){return yd(e),gd(e,Ot(e,0,V.textEnd))}function gd(e,t){let n=V.textEnd;return n===t?-1:(t=V.keyEnd=Zy(e,V.key=t,n),Ot(e,t,n))}function Qy(e){return yd(e),md(e,Ot(e,0,V.textEnd))}function md(e,t){let n=V.textEnd,r=V.key=Ot(e,t,n);return n===r?-1:(r=V.keyEnd=Yy(e,r,n),r=Nc(e,r,n,58),r=V.value=Ot(e,r,n),r=V.valueEnd=Jy(e,r,n),Nc(e,r,n,59))}function yd(e){V.key=0,V.keyEnd=0,V.value=0,V.valueEnd=0,V.textEnd=e.length}function Ot(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function Zy(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function Yy(e,t,n){let r;for(;t<n&&((r=e.charCodeAt(t))===45||r===95||(r&-33)>=65&&(r&-33)<=90||r>=48&&r<=57);)t++;return t}function Nc(e,t,n,r){return t=Ot(e,t,n),t<n&&t++,t}function Jy(e,t,n){let r=-1,o=-1,i=-1,s=t,a=s;for(;s<n;){let c=e.charCodeAt(s++);if(c===59)return a;c===34||c===39?a=s=Sc(e,c,s,n):t===s-4&&i===85&&o===82&&r===76&&c===40?a=s=Sc(e,41,s,n):c>32&&(a=s),i=o,o=r,r=c&-33}return a}function Sc(e,t,n,r){let o=-1,i=n;for(;i<r;){let s=e.charCodeAt(i++);if(s==t&&o!==92)return i;s==92&&o===92?o=0:o=s}throw new Error}function Ky(e,t,n){let r=g(),o=ct();if(G(r,o,t)){let i=O(),s=hn();so(i,s,r,e,t,r[R],n,!1)}return Ky}function ns(e,t,n,r,o){ea(t,e,n,o?"class":"style",r)}function Xy(e,t,n){return vd(e,t,n,!1),Xy}function ev(e,t){return vd(e,t,null,!0),ev}function Qx(e){Id(wd,tv,e,!1)}function tv(e,t){for(let n=Qy(t);n>=0;n=md(t,n))wd(e,hd(t),zy(t))}function Zx(e){Id(cv,nv,e,!0)}function nv(e,t){for(let n=Gy(t);n>=0;n=gd(t,n))zr(e,hd(t),!0)}function vd(e,t,n,r){let o=g(),i=O(),s=As(2);if(i.firstUpdatePass&&Dd(i,e,s,r),t!==X&&G(o,s,t)){let a=i.data[Me()];bd(i,a,o,o[R],e,o[s+1]=uv(t,n),r,s)}}function Id(e,t,n,r){let o=O(),i=As(2);o.firstUpdatePass&&Dd(o,null,i,r);let s=g();if(n!==X&&G(s,i,n)){let a=o.data[Me()];if(Md(a,r)&&!Ed(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=si(c,n||"")),ns(o,a,s,n,r)}else lv(o,a,s,s[R],s[i+1],s[i+1]=av(e,t,n),r,i)}}function Ed(e,t){return t>=e.expandoStartIndex}function Dd(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Me()],s=Ed(e,n);Md(i,r)&&t===null&&!s&&(t=!1),t=rv(o,i,t,r),Uy(o,i,t,n,s,r)}}function rv(e,t,n,r){let o=qp(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=ni(null,e,t,n,r),n=dn(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=ni(o,e,t,n,r),i===null){let c=ov(e,t,r);c!==void 0&&Array.isArray(c)&&(c=ni(null,e,t,c[1],r),c=dn(c,t.attrs,r),iv(e,t,r,c))}else i=sv(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function ov(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Rt(r)!==0)return e[st(r)]}function iv(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[st(o)]=r}function sv(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=dn(r,s,n)}return dn(r,t.attrs,n)}function ni(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=dn(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function dn(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),zr(e,s,n?!0:t[++i]))}return e===void 0?null:e}function av(e,t,n){if(n==null||n==="")return Y;let r=[],o=Ht(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function wd(e,t,n){zr(e,t,Ht(n))}function cv(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&zr(e,r,n)}function lv(e,t,n,r,o,i,s,a){o===X&&(o=Y);let c=0,l=0,u=0<o.length?o[0]:null,f=0<i.length?i[0]:null;for(;u!==null||f!==null;){let p=c<o.length?o[c+1]:void 0,d=l<i.length?i[l+1]:void 0,h=null,y;u===f?(c+=2,l+=2,p!==d&&(h=f,y=d)):f===null||u!==null&&u<f?(c+=2,h=u):(l+=2,h=f,y=d),h!==null&&bd(e,t,n,r,h,y,s,a),u=c<o.length?o[c]:null,f=l<i.length?i[l]:null}}function bd(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=$y(l)?kc(c,t,n,o,Rt(l),s):void 0;if(!jr(u)){jr(i)||Hy(l)&&(i=kc(c,null,n,o,a,s));let f=Ml(Me(),n);om(r,s,f,o,i)}}function kc(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,f=u===null,p=n[o+1];p===X&&(p=f?Y:void 0);let d=f?zo(p,r):u===r?p:void 0;if(l&&!jr(d)&&(d=zo(c,r)),jr(d)&&(a=d,s))return a;let h=e[o+1];o=s?st(h):Rt(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=zo(c,r))}return a}function jr(e){return e!==void 0}function uv(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=J(Ht(e)))),e}function Md(e,t){return(e.flags&(t?8:16))!==0}var rs=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function ri(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function dv(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let l=e.at(i),u=t[i],f=ri(i,l,i,u,n);if(f!==0){f<0&&e.updateValue(i,u),i++;continue}let p=e.at(s),d=t[c],h=ri(s,p,c,d,n);if(h!==0){h<0&&e.updateValue(s,d),s--,c--;continue}let y=n(i,l),A=n(s,p),T=n(i,u);if(Object.is(T,A)){let lt=n(c,d);Object.is(lt,y)?(e.swap(i,s),e.updateValue(s,d),c--,s--):e.move(s,i),e.updateValue(i,u),i++;continue}if(r??=new Vr,o??=Oc(e,i,s,n),os(e,r,i,T))e.updateValue(i,u),i++,s++;else if(o.has(T))r.set(y,e.detach(i)),s--;else{let lt=e.create(i,t[i]);e.attach(i,lt),i++,s++}}for(;i<=c;)Rc(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),l=c.next();for(;!l.done&&i<=s;){let u=e.at(i),f=l.value,p=ri(i,u,i,f,n);if(p!==0)p<0&&e.updateValue(i,f),i++,l=c.next();else{r??=new Vr,o??=Oc(e,i,s,n);let d=n(i,f);if(os(e,r,i,d))e.updateValue(i,f),i++,s++,l=c.next();else if(!o.has(d))e.attach(i,e.create(i,f)),i++,s++,l=c.next();else{let h=n(i,u);r.set(h,e.detach(i)),s--}}}for(;!l.done;)Rc(e,r,n,e.length,l.value),l=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function os(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function Rc(e,t,n,r,o){if(os(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function Oc(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Vr=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function Yx(e,t){mn("NgControlFlow");let n=g(),r=ct(),o=n[r]!==X?n[r]:-1,i=o!==-1?Hr(n,F+o):void 0,s=0;if(G(n,r,e)){let a=E(null);try{if(i!==void 0&&zu(i,s),e!==-1){let c=F+e,l=Hr(n,c),u=cs(n[I],c),f=Nt(l,u.tView.ssrId),p=vn(n,u,t,{dehydratedView:f});In(l,p,s,Tt(u,f))}}finally{E(a)}}else if(i!==void 0){let a=Wu(i,s);a!==void 0&&(a[H]=t)}}var is=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-U}};function Jx(e){return e}var ss=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function Kx(e,t,n,r,o,i,s,a,c,l,u,f,p){mn("NgControlFlow");let d=g(),h=O(),y=c!==void 0,A=g(),T=a?s.bind(A[oe][H]):s,lt=new ss(y,T);A[F+e]=lt,Fr(d,h,e+1,t,n,r,o,je(h.consts,i)),y&&Fr(d,h,e+2,c,l,u,f,je(h.consts,p))}var as=class extends rs{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-U}at(t){return this.getLView(t)[H].$implicit}attach(t,n){let r=n[Ct];this.needsIndexUpdate||=t!==this.length,In(this.lContainer,n,t,Tt(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,fv(this.lContainer,t)}create(t,n){let r=Nt(this.lContainer,this.templateTNode.tView.ssrId),o=vn(this.hostLView,this.templateTNode,new is(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){ao(t[I],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[H].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[H].$index=t}getLView(t){return pv(this.lContainer,t)}};function Xx(e){let t=E(null),n=Me();try{let r=g(),o=r[I],i=r[n],s=n+1,a=Hr(r,s);if(i.liveCollection===void 0){let l=cs(o,s);i.liveCollection=new as(a,r,l)}else i.liveCollection.reset();let c=i.liveCollection;if(dv(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let l=ct(),u=c.length===0;if(G(r,l,u)){let f=n+2,p=Hr(r,f);if(u){let d=cs(o,f),h=Nt(p,d.tView.ssrId),y=vn(r,d,void 0,{dehydratedView:h});In(p,y,0,Tt(d,h))}else zu(p,0)}}}finally{E(t)}}function Hr(e,t){return e[t]}function fv(e,t){return an(e,t)}function pv(e,t){return Wu(e,t)}function cs(e,t){return _s(e,t)}function Cd(e,t,n,r){let o=g(),i=O(),s=F+e,a=o[R],c=i.firstCreatePass?Yu(s,i,o,t,Xs,ks(),n,r):i.data[s],l=gv(i,o,c,a,t,e);o[s]=l;let u=Qr(c);return at(c,!0),xu(a,l,c),!ra(c)&&Jr()&&co(i,o,l,c),(Rp()===0||u)&&Vt(l,o),Op(),u&&(io(i,o,c),Ws(i,c,o)),r!==null&&Ks(o,c),Cd}function xd(){let e=W();Rs()?Os():(e=e.parent,at(e,!1));let t=e;Pp(t)&&Lp(),Ap();let n=O();return n.firstCreatePass&&Ju(n,t),t.classesWithoutHost!=null&&Yp(t)&&ns(n,t,g(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&Jp(t)&&ns(n,t,g(),t.stylesWithoutHost,!1),xd}function hv(e,t,n,r){return Cd(e,t,n,r),xd(),hv}var gv=(e,t,n,r,o,i)=>(Kr(!0),Mu(r,o,Gp()));function mv(e,t,n,r,o){let i=t.consts,s=je(i,r),a=En(t,e,8,"ng-container",s);s!==null&&Ui(a,s,!0);let c=je(i,o);return ks()&&aa(t,n,a,c,Xs),a.mergedAttrs=_t(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function _d(e,t,n){let r=g(),o=O(),i=e+F,s=o.firstCreatePass?mv(i,o,r,t,n):o.data[i];at(s,!0);let a=vv(o,r,s,e);return r[i]=a,Jr()&&co(o,r,a,s),Vt(a,r),Qr(s)&&(io(o,r,s),Ws(o,s,r)),n!=null&&Ks(r,s),_d}function Td(){let e=W(),t=O();return Rs()?Os():(e=e.parent,at(e,!1)),t.firstCreatePass&&(js(t,e),xs(e)&&t.queries.elementEnd(e)),Td}function yv(e,t,n){return _d(e,t,n),Td(),yv}var vv=(e,t,n,r)=>(Kr(!0),Cg(t[R],""));function e_(){return g()}function Iv(e,t,n){let r=g(),o=ct();if(G(r,o,t)){let i=O(),s=hn();so(i,s,r,e,t,r[R],n,!0)}return Iv}var We=void 0;function Ev(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var Dv=["en",[["a","p"],["AM","PM"],We],[["AM","PM"],We,We],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],We,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],We,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",We,"{1} 'at' {0}",We],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",Ev],en={};function t_(e,t,n){typeof t!="string"&&(n=t,t=e[ls.LocaleId]),t=t.toLowerCase().replace(/_/g,"-"),en[t]=e,n&&(en[t][ls.ExtraData]=n)}function n_(e){let t=wv(e),n=Ac(t);if(n)return n;let r=t.split("-")[0];if(n=Ac(r),n)return n;if(r==="en")return Dv;throw new C(701,!1)}function Ac(e){return e in en||(en[e]=Re.ng&&Re.ng.common&&Re.ng.common.locales&&Re.ng.common.locales[e]),en[e]}var ls=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ls||{});function wv(e){return e.toLowerCase().replace(/_/g,"-")}var Br="en-US";var bv=Br;function Mv(e){typeof e=="string"&&(bv=e.toLowerCase().replace(/_/g,"-"))}function Pc(e,t,n){return function r(o){if(o===Function)return n;let i=Pt(e)?ge(e.index,t):t;sa(i,5);let s=t[H],a=Lc(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=Lc(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Lc(e,t,n,r){let o=E(null);try{return N(6,t,n),n(r)!==!1}catch(i){return Cv(e,i),!1}finally{N(7,t,n),E(o)}}function Cv(e,t){let n=e[Je],r=n?n.get(Ve,null):null;r&&r.handleError(t)}function Fc(e,t,n,r,o,i){let s=t[n],a=t[I],l=a.data[n].outputs[r],u=s[l],f=a.firstCreatePass?Ss(a):null,p=Ns(t),d=u.subscribe(i),h=p.length;p.push(i,d),f&&f.push(o,e.index,h,-(h+1))}var xv=new Map;function _v(e,t,n,r){let o=g(),i=O(),s=W();return Nd(i,o,o[R],s,e,t,r),_v}function Tv(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Er],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Nd(e,t,n,r,o,i,s){let a=Qr(r),l=e.firstCreatePass?Ss(e):null,u=Ns(t),f=!0;if(r.type&3||s){let p=me(r,t),d=s?s(p):p,h=u.length,y=s?T=>s(he(T[r.index])):r.index,A=null;if(!s&&a&&(A=Tv(e,t,o,r.index)),A!==null){let T=A.__ngLastListenerFn__||A;T.__ngNextListenerFn__=i,A.__ngLastListenerFn__=i,f=!1}else{i=Pc(r,t,i);let T=t[Je].get(xh);xv.get(T)?.(d,o,i);let Bd=n.listen(d,o,i);u.push(i,Bd),l&&l.push(o,y,h,h+1)}}else i=Pc(r,t,i);if(f){let p=r.outputs?.[o],d=r.hostDirectiveOutputs?.[o];if(d&&d.length)for(let h=0;h<d.length;h+=2){let y=d[h],A=d[h+1];Fc(r,t,y,A,o,i)}if(p&&p.length)for(let h of p)Fc(r,t,h,o,o,i)}}function r_(e=1){return zp(e)}function Nv(e,t){let n=null,r=mg(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?bu(e,i,!0):Ig(r,i))return o}return n}function o_(e){let t=g()[oe][K];if(!t.projection){let n=e?e.length:1,r=t.projection=cp(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?Nv(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function i_(e,t=0,n,r,o,i){let s=g(),a=O(),c=r?e+1:null;c!==null&&Fr(s,a,c,r,o,i,null,n);let l=En(a,F+e,16,null,n||null);l.projection===null&&(l.projection=t),Os();let f=!s[Ct]||Tl();s[oe][K].projection[l.projection]===null&&c!==null?Sv(s,a,c):f&&!ra(l)&&nm(a,s,l)}function Sv(e,t,n){let r=F+n,o=t.data[r],i=e[r],s=Nt(i,o.tView.ssrId),a=vn(e,o,void 0,{dehydratedView:s});In(i,a,0,Tt(o,s))}function kv(e,t,n){return Sd(e,"",t,"",n),kv}function Sd(e,t,n,r,o){let i=g(),s=pd(i,t,n,r);if(s!==X){let a=O(),c=hn();so(a,c,i,e,s,i[R],o,!1)}return Sd}function s_(e,t,n,r){iy(e,t,n,r)}function a_(e,t,n){oy(e,t,n)}function c_(e){let t=g(),n=O(),r=kl();Ps(r+1);let o=la(n,r);if(e.dirty&&_p(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=cy(t,r);e.reset(i,Eh),e.notifyOnChanges()}return!0}return!1}function l_(){return ry(g(),kl())}function Rv(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function u_(e){let t=jp();return Zr(t,F+e)}function d_(e,t=""){let n=g(),r=O(),o=e+F,i=r.firstCreatePass?En(r,o,1,t,null):r.data[o],s=Ov(r,n,i,t,e);n[o]=s,Jr()&&co(r,n,s,i),at(i,!1)}var Ov=(e,t,n,r,o)=>(Kr(!0),bg(t[R],r));function Av(e){return kd("",e,""),Av}function kd(e,t,n){let r=g(),o=pd(r,e,t,n);return o!==X&&Rd(r,Me(),o),kd}function Pv(e,t,n,r,o){let i=g(),s=Vy(i,e,t,n,r,o);return s!==X&&Rd(i,Me(),s),Pv}function Rd(e,t,n){let r=Ml(t,e);Mg(e[R],r,n)}function Lv(e,t,n){nu(t)&&(t=t());let r=g(),o=ct();if(G(r,o,t)){let i=O(),s=hn();so(i,s,r,e,t,r[R],n,!1)}return Lv}function f_(e,t){let n=nu(e);return n&&e.set(t),n}function Fv(e,t){let n=g(),r=O(),o=W();return Nd(r,n,n[R],o,e,t),Fv}function jv(e,t,n){let r=O();if(r.firstCreatePass){let o=pe(e);us(n,r.data,r.blueprint,o,!0),us(t,r.data,r.blueprint,o,!1)}}function us(e,t,n,r,o){if(e=B(e),Array.isArray(e))for(let i=0;i<e.length;i++)us(e[i],t,n,r,o);else{let i=O(),s=g(),a=W(),c=Mt(e)?e:B(e.provide),l=pl(e),u=a.providerIndexes&1048575,f=a.directiveStart,p=a.providerIndexes>>20;if(Mt(e)||!e.multi){let d=new rt(l,o,po),h=ii(c,t,o?u:u+p,f);h===-1?(vi(_r(a,s),i,c),oi(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(d),s.push(d)):(n[h]=d,s[h]=d)}else{let d=ii(c,t,u+p,f),h=ii(c,t,u,u+p),y=d>=0&&n[d],A=h>=0&&n[h];if(o&&!A||!o&&!y){vi(_r(a,s),i,c);let T=Bv(o?Hv:Vv,n.length,o,r,l);!o&&A&&(n[h].providerFactory=T),oi(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(T),s.push(T)}else{let T=Od(n[o?h:d],l,!o&&r);oi(i,e,d>-1?d:h,T)}!o&&r&&A&&n[h].componentProviders++}}}function oi(e,t,n,r){let o=Mt(t),i=mp(t);if(o||i){let c=(i?B(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function Od(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function ii(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function Vv(e,t,n,r,o){return ds(this.multi,[])}function Hv(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=rn(r,r[I],this.providerFactory.index,o);s=c.slice(0,a),ds(i,s);for(let l=a;l<c.length;l++)s.push(c[l])}else s=[],ds(i,s);return s}function ds(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function Bv(e,t,n,r,o){let i=new rt(e,n,po);return i.multi=[],i.index=t,i.componentProviders=0,Od(i,o,r&&!n),i}function p_(e,t=[]){return n=>{n.providersResolver=(r,o)=>jv(r,o?o(e):e,t)}}function h_(e,t,n){let r=se()+e,o=g();return o[r]===X?xe(o,r,n?t.call(n):t()):ho(o,r)}function g_(e,t,n,r){return Ad(g(),se(),e,t,n,r)}function m_(e,t,n,r,o){return Pd(g(),se(),e,t,n,r,o)}function y_(e,t,n,r,o,i){return Ld(g(),se(),e,t,n,r,o,i)}function v_(e,t,n,r,o,i,s){return $v(g(),se(),e,t,n,r,o,i,s)}function I_(e,t,n,r,o,i,s,a){let c=se()+e,l=g(),u=go(l,c,n,r,o,i);return G(l,c+4,s)||u?xe(l,c+5,a?t.call(a,n,r,o,i,s):t(n,r,o,i,s)):ho(l,c+5)}function E_(e,t,n,r,o,i,s,a,c){let l=se()+e,u=g(),f=go(u,l,n,r,o,i);return kt(u,l+4,s,a)||f?xe(u,l+6,c?t.call(c,n,r,o,i,s,a):t(n,r,o,i,s,a)):ho(u,l+6)}function D_(e,t,n,r,o,i,s,a,c,l){let u=se()+e,f=g(),p=go(f,u,n,r,o,i);return cd(f,u+4,s,a,c)||p?xe(f,u+7,l?t.call(l,n,r,o,i,s,a,c):t(n,r,o,i,s,a,c)):ho(f,u+7)}function w_(e,t,n,r){return Uv(g(),se(),e,t,n,r)}function Dn(e,t){let n=e[t];return n===X?void 0:n}function Ad(e,t,n,r,o,i){let s=t+n;return G(e,s,o)?xe(e,s+1,i?r.call(i,o):r(o)):Dn(e,s+1)}function Pd(e,t,n,r,o,i,s){let a=t+n;return kt(e,a,o,i)?xe(e,a+2,s?r.call(s,o,i):r(o,i)):Dn(e,a+2)}function Ld(e,t,n,r,o,i,s,a){let c=t+n;return cd(e,c,o,i,s)?xe(e,c+3,a?r.call(a,o,i,s):r(o,i,s)):Dn(e,c+3)}function $v(e,t,n,r,o,i,s,a,c){let l=t+n;return go(e,l,o,i,s,a)?xe(e,l+4,c?r.call(c,o,i,s,a):r(o,i,s,a)):Dn(e,l+4)}function Uv(e,t,n,r,o,i){let s=t+n,a=!1;for(let c=0;c<o.length;c++)G(e,s++,o[c])&&(a=!0);return a?xe(e,s,r.apply(i,o)):Dn(e,s)}function b_(e,t){let n=O(),r,o=e+F;n.firstCreatePass?(r=qv(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Ze(r.type,!0)),s,a=Z(po);try{let c=xr(!1),l=i();return xr(c),Rv(n,g(),o,l),l}finally{Z(a)}}function qv(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function M_(e,t,n){let r=e+F,o=g(),i=Zr(o,r);return da(o,r)?Ad(o,se(),t,i.transform,n,i):i.transform(n)}function C_(e,t,n,r){let o=e+F,i=g(),s=Zr(i,o);return da(i,o)?Pd(i,se(),t,s.transform,n,r,s):s.transform(n,r)}function x_(e,t,n,r,o){let i=e+F,s=g(),a=Zr(s,i);return da(s,i)?Ld(s,se(),t,a.transform,n,r,o,a):a.transform(n,r,o)}function da(e,t){return e[I].data[t].pure}function __(e,t){return uo(e,t)}var fs=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},T_=(()=>{class e{compileModuleSync(n){return new Ji(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=sl(n),i=Du(o.declarations).reduce((s,a)=>{let c=Ye(a);return c&&s.push(new St(c)),s},[]);return new fs(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Wv=(()=>{class e{zone=x(re);changeDetectionScheduler=x(it);applicationRef=x(un);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),zv=new k("",{factory:()=>!1});function Fd({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new re(te(ee({},jd()),{scheduleInRootZone:n})),[{provide:re,useFactory:e},{provide:vr,multi:!0,useFactory:()=>{let r=x(Wv,{optional:!0});return()=>r.initialize()}},{provide:vr,multi:!0,useFactory:()=>{let r=x(Gv);return()=>{r.initialize()}}},t===!0?{provide:Jl,useValue:!0}:[],{provide:Kl,useValue:n??Yl}]}function N_(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=Fd({ngZoneFactory:()=>{let o=jd(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&mn("NgZone_CoalesceEvent"),new re(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return cl([{provide:zv,useValue:!0},{provide:Hs,useValue:!1},r])}function jd(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Gv=(()=>{class e{subscription=new L;initialized=!1;zone=x(re);pendingTasks=x(Xr);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{re.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{re.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Qv=(()=>{class e{appRef=x(un);taskService=x(Xr);ngZone=x(re);zonelessEnabled=x(Hs);tracing=x(to,{optional:!0});disableScheduling=x(Jl,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new L;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Nr):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(x(Kl,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof wi||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?oc:Xl;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Nr+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,oc(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=$({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Zv(){return typeof $localize<"u"&&$localize.locale||Br}var Vd=new k("",{providedIn:"root",factory:()=>x(Vd,M.Optional|M.SkipSelf)||Zv()});var ps=new k(""),Yv=new k("");function Yt(e){return!e.moduleRef}function Jv(e){let t=Yt(e)?e.r3Injector:e.moduleRef.injector,n=t.get(re);return n.run(()=>{Yt(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Ve,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Yt(e)){let i=()=>t.destroy(),s=e.platformInjector.get(ps);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(ps);s.add(i),e.moduleRef.onDestroy(()=>{fr(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return Xv(r,n,()=>{let i=t.get(fd);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(Vd,Br);if(Mv(s||Br),!t.get(Yv,!0))return Yt(e)?t.get(un):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Yt(e)){let c=t.get(un);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return Kv(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function Kv(e,t){let n=e.injector.get(un);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new C(-403,!1);t.push(e)}function Xv(e,t,n){try{let r=n();return ud(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var pr=null;function eI(e=[],t){return ot.create({name:t,providers:[{provide:fl,useValue:"platform"},{provide:ps,useValue:new Set([()=>pr=null])},...e]})}function tI(e=[]){if(pr)return pr;let t=eI(e);return pr=t,Ay(),nI(t),t}function nI(e){let t=e.get(Th,null);hl(e,()=>{t?.forEach(n=>n())})}function S_(){return!1}var k_=(()=>{class e{static __NG_ELEMENT_ID__=rI}return e})();function rI(e){return oI(W(),g(),(e&16)===16)}function oI(e,t,n){if(Pt(e)&&!n){let r=ge(e.index,t);return new cn(r,r)}else if(e.type&175){let r=t[oe];return new cn(r,t)}return null}var hs=class{constructor(){}supports(t){return ad(t)}create(t){return new gs(t)}},iI=(e,t)=>t,gs=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||iI}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<jc(r,o,i)?n:r,a=jc(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let p=0;p<l;p++){let d=p<i.length?i[p]:i[p]=0,h=d+p;u<=h&&h<l&&(i[p]=d+1)}let f=s.previousIndex;i[f]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!ad(t))throw new C(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,xy(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new ms(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new $r),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new $r),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},ms=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},ys=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},$r=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new ys,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function jc(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}var vs=class{constructor(){}supports(t){return t instanceof Map||ua(t)}create(){return new Is}},Is=class{_records=new Map;_mapHead=null;_appendAfter=null;_previousMapHead=null;_changesHead=null;_changesTail=null;_additionsHead=null;_additionsTail=null;_removalsHead=null;_removalsTail=null;get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(t){let n;for(n=this._mapHead;n!==null;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}diff(t){if(!t)t=new Map;else if(!(t instanceof Map||ua(t)))throw new C(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{let i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){let r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){let o=this._records.get(t);this._maybeAddToChanges(o,n);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new Es(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;t!==null;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;t!=null;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){this._additionsHead===null?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){this._changesHead===null?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}},Es=class{key;previousValue=null;currentValue=null;_nextPrevious=null;_next=null;_prev=null;_nextAdded=null;_nextRemoved=null;_nextChanged=null;constructor(t){this.key=t}};function Vc(){return new sI([new hs])}var sI=(()=>{class e{factories;static \u0275prov=$({token:e,providedIn:"root",factory:Vc});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Vc()),deps:[[e,new nl,new tl]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new C(901,!1)}}return e})();function Hc(){return new aI([new vs])}var aI=(()=>{class e{static \u0275prov=$({token:e,providedIn:"root",factory:Hc});factories;constructor(n){this.factories=n}static create(n,r){if(r){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Hc()),deps:[[e,new nl,new tl]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r)return r;throw new C(901,!1)}}return e})();function R_(e){N(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=tI(r),i=[Fd({}),{provide:it,useExisting:Qv},...n||[]],s=new Lr({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return Jv({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{N(9)}}function O_(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function A_(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function P_(e){return No(e)}function L_(e,t){return Co(e,t?.equal)}var Ds=class{[ne];constructor(t){this[ne]=t}destroy(){this[ne].destroy()}};function cI(e,t){!t?.injector&&ml(cI);let n=t?.injector??x(ot),r=t?.manualCleanup!==!0?n.get(gn):null,o,i=n.get(qs,null,{optional:!0}),s=n.get(it);return i!==null&&!t?.forceRoot?(o=dI(i.view,s,e),r instanceof Tr&&r._lView===i.view&&(r=null)):o=fI(e,n.get(ld),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new Ds(o)}var Hd=te(ee({},ut),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:on,run(){if(this.dirty=!1,this.hasRun&&!Cn(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=$t(this),n=br(!1);try{this.maybeCleanup(),this.fn(e)}finally{br(n),Mn(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),lI=te(ee({},Hd),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){Ut(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),uI=te(ee({},Hd),{consumerMarkedDirty(){this.view[m]|=8192,Ft(this.view),this.notifier.notify(13)},destroy(){Ut(this),this.onDestroyFn(),this.maybeCleanup(),this.view[Xe]?.delete(this)}});function dI(e,t,n){let r=Object.create(uI);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[Xe]??=new Set,e[Xe].add(r),r.consumerMarkedDirty(r),r}function fI(e,t,n){let r=Object.create(lI);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function F_(e){let t=Ye(e);if(!t)return null;let n=new St(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}export{L as a,Xd as b,_ as c,Vo as d,Ho as e,Te as f,Wt as g,Gt as h,Se as i,uf as j,df as k,ff as l,ce as m,pf as n,hf as o,ke as p,wf as q,qe as r,Uo as s,tr as t,Mf as u,Cf as v,xf as w,_f as x,Qt as y,Ua as z,Tf as A,Nf as B,Zt as C,qo as D,Sf as E,Of as F,Af as G,Wo as H,Pf as I,Lf as J,Ff as K,jf as L,Vf as M,Hf as N,C as O,$c as P,zc as Q,$ as R,ix as S,sx as T,k as U,M as V,Le as W,x as X,cl as Y,fp as Z,fl as _,Fe as $,hl as aa,ml as ba,ax as ca,cx as da,lx as ea,ux as fa,dx as ga,fx as ha,ot as ia,gn as ja,it as ka,Xr as la,Oe as ma,re as na,Ve as oa,px as pa,eo as qa,hx as ra,gx as sa,xh as ta,Th as ua,mx as va,yx as wa,vx as xa,to as ya,mn as za,Ph as Aa,sn as Ba,Ht as Ca,zs as Da,Ix as Ea,Ex as Fa,Dx as Ga,wx as Ha,bx as Ia,gu as Ja,ng as Ka,ro as La,Mx as Ma,og as Na,Cx as Oa,xx as Pa,_x as Qa,Tx as Ra,ji as Sa,Rr as Ta,Or as Ua,Px as Va,po as Wa,Lx as Xa,ca as Ya,jx as Za,uy as _a,dy as $a,Bx as ab,$x as bb,Ux as cb,qx as db,Iy as eb,Wx as fb,Ty as gb,zx as hb,ud as ib,Gx as jb,Oy as kb,un as lb,jy as mb,Ky as nb,Xy as ob,ev as pb,Qx as qb,Zx as rb,Yx as sb,Jx as tb,Kx as ub,Xx as vb,Cd as wb,xd as xb,hv as yb,_d as zb,Td as Ab,yv as Bb,e_ as Cb,Iv as Db,t_ as Eb,n_ as Fb,ls as Gb,_v as Hb,r_ as Ib,o_ as Jb,i_ as Kb,kv as Lb,Sd as Mb,s_ as Nb,a_ as Ob,c_ as Pb,l_ as Qb,u_ as Rb,d_ as Sb,Av as Tb,kd as Ub,Pv as Vb,Lv as Wb,f_ as Xb,Fv as Yb,p_ as Zb,h_ as _b,g_ as $b,m_ as ac,y_ as bc,v_ as cc,I_ as dc,E_ as ec,D_ as fc,w_ as gc,b_ as hc,M_ as ic,C_ as jc,x_ as kc,__ as lc,T_ as mc,N_ as nc,Vd as oc,S_ as pc,k_ as qc,sI as rc,aI as sc,R_ as tc,O_ as uc,A_ as vc,P_ as wc,L_ as xc,cI as yc,F_ as zc};
