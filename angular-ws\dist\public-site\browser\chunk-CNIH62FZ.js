import{a as _,b as at,c as Ai,d as Di,e as Oi,f as ct,g as _i,h as Ae,i as Pi,r as Ft,s as Ni,u as Gr,v as $t,x as Wr}from"./chunk-D6WDCTDG.js";import{$ as ae,$a as kt,A as te,Aa as ii,Ba as nt,C as Nr,Ca as Ie,D as Ce,Da as Me,Ea as oi,F as me,Fa as si,G as se,Ga as ai,H as Ur,Ha as ci,Hb as Si,I as Hn,Ia as ui,J as Gn,Ja as li,K as Wn,Ka as di,L as x,La as ye,M as Xn,N as O,Nb as Ti,O as S,Oa as hi,Pb as Ei,Qb as bi,R as y,S as Ze,Sa as it,T as Jn,U as C,Ua as fi,V as xr,Va as zr,W as m,Wa as V,X as d,Xa as pi,Y as Ke,Ya as gi,_ as Yn,_a as mi,a as $n,aa as z,ab as vi,b as zn,bb as ot,c as Dr,ca as Qe,cb as st,d as Or,e as _r,f as Q,g as $,ga as kr,h as ee,ha as Zn,hb as Br,i as D,ia as et,ib as yi,j as f,ja as Kn,jb as Vr,k as Je,kb as qr,l as Bn,la as tt,lb as Hr,m as Vn,ma as ve,mb as Ri,mc as Ci,na as B,oa as Lr,p as T,pa as Qn,q as xt,qa as jr,qc as Lt,r as N,s as Ye,sa as ei,t as qn,ta as Fr,tc as Ii,u as Pr,ua as ti,uc as jt,va as rt,xa as $r,y as G,ya as ri,yb as wi,z as ge,za as ni,zc as Mi}from"./chunk-BL4EGCPV.js";import{a as h,b as P,d as Ar}from"./chunk-4CLCTAJ7.js";var Vt=new C(""),Zr=(()=>{class t{_zone;_plugins;_eventNameToPlugin=new Map;constructor(e,r){this._zone=r,e.forEach(i=>{i.manager=this}),this._plugins=e.slice().reverse()}addEventListener(e,r,i,o){return this._findPluginFor(r).addEventListener(e,r,i,o)}getZone(){return this._zone}_findPluginFor(e){let r=this._eventNameToPlugin.get(e);if(r)return r;if(r=this._plugins.find(o=>o.supports(e)),!r)throw new S(5101,!1);return this._eventNameToPlugin.set(e,r),r}static \u0275fac=function(r){return new(r||t)(m(Vt),m(B))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),ut=class{_doc;constructor(n){this._doc=n}manager},zt="ng-app-id";function Ui(t){for(let n of t)n.remove()}function xi(t,n){let e=n.createElement("style");return e.textContent=t,e}function as(t,n,e,r){let i=t.head?.querySelectorAll(`style[${zt}="${n}"],link[${zt}="${n}"]`);if(i)for(let o of i)o.removeAttribute(zt),o instanceof HTMLLinkElement?r.set(o.href.slice(o.href.lastIndexOf("/")+1),{usage:0,elements:[o]}):o.textContent&&e.set(o.textContent,{usage:0,elements:[o]})}function Jr(t,n){let e=n.createElement("link");return e.setAttribute("rel","stylesheet"),e.setAttribute("href",t),e}var Kr=(()=>{class t{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(e,r,i,o={}){this.doc=e,this.appId=r,this.nonce=i,this.isServer=Gr(o),as(e,r,this.inline,this.external),this.hosts.add(e.head)}addStyles(e,r){for(let i of e)this.addUsage(i,this.inline,xi);r?.forEach(i=>this.addUsage(i,this.external,Jr))}removeStyles(e,r){for(let i of e)this.removeUsage(i,this.inline);r?.forEach(i=>this.removeUsage(i,this.external))}addUsage(e,r,i){let o=r.get(e);o?o.usage++:r.set(e,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,i(e,this.doc)))})}removeUsage(e,r){let i=r.get(e);i&&(i.usage--,i.usage<=0&&(Ui(i.elements),r.delete(e)))}ngOnDestroy(){for(let[,{elements:e}]of[...this.inline,...this.external])Ui(e);this.hosts.clear()}addHost(e){this.hosts.add(e);for(let[r,{elements:i}]of this.inline)i.push(this.addElement(e,xi(r,this.doc)));for(let[r,{elements:i}]of this.external)i.push(this.addElement(e,Jr(r,this.doc)))}removeHost(e){this.hosts.delete(e)}addElement(e,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(zt,this.appId),e.appendChild(r)}static \u0275fac=function(r){return new(r||t)(m(_),m(Fr),m($r,8),m(rt))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),Xr={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Qr=/%COMP%/g;var Li="%COMP%",cs=`_nghost-${Li}`,us=`_ngcontent-${Li}`,ls=!0,ds=new C("",{providedIn:"root",factory:()=>ls});function hs(t){return us.replace(Qr,t)}function fs(t){return cs.replace(Qr,t)}function ji(t,n){return n.map(e=>e.replace(Qr,t))}var en=(()=>{class t{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(e,r,i,o,s,a,c,u=null,l=null){this.eventManager=e,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=o,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=Gr(a),this.defaultRenderer=new lt(e,s,c,this.platformIsServer,this.tracingService)}createRenderer(e,r){if(!e||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===nt.ShadowDom&&(r=P(h({},r),{encapsulation:nt.Emulated}));let i=this.getOrCreateRenderer(e,r);return i instanceof Bt?i.applyToHost(e):i instanceof dt&&i.applyStyles(),i}getOrCreateRenderer(e,r){let i=this.rendererByCompId,o=i.get(r.id);if(!o){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,g=this.platformIsServer,b=this.tracingService;switch(r.encapsulation){case nt.Emulated:o=new Bt(c,u,r,this.appId,l,s,a,g,b);break;case nt.ShadowDom:return new Yr(c,u,e,r,s,a,this.nonce,g,b);default:o=new dt(c,u,r,l,s,a,g,b);break}i.set(r.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(e){this.rendererByCompId.delete(e)}static \u0275fac=function(r){return new(r||t)(m(Zr),m(Kr),m(Fr),m(ds),m(_),m(rt),m(B),m($r),m(ri,8))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),lt=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,e,r,i,o){this.eventManager=n,this.doc=e,this.ngZone=r,this.platformIsServer=i,this.tracingService=o}destroy(){}destroyNode=null;createElement(n,e){return e?this.doc.createElementNS(Xr[e]||e,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,e){(ki(n)?n.content:n).appendChild(e)}insertBefore(n,e,r){n&&(ki(n)?n.content:n).insertBefore(e,r)}removeChild(n,e){e.remove()}selectRootElement(n,e){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new S(-5104,!1);return e||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,e,r,i){if(i){e=i+":"+e;let o=Xr[i];o?n.setAttributeNS(o,e,r):n.setAttribute(e,r)}else n.setAttribute(e,r)}removeAttribute(n,e,r){if(r){let i=Xr[r];i?n.removeAttributeNS(i,e):n.removeAttribute(`${r}:${e}`)}else n.removeAttribute(e)}addClass(n,e){n.classList.add(e)}removeClass(n,e){n.classList.remove(e)}setStyle(n,e,r,i){i&(it.DashCase|it.Important)?n.style.setProperty(e,r,i&it.Important?"important":""):n.style[e]=r}removeStyle(n,e,r){r&it.DashCase?n.style.removeProperty(e):n.style[e]=""}setProperty(n,e,r){n!=null&&(n[e]=r)}setValue(n,e){n.nodeValue=e}listen(n,e,r,i){if(typeof n=="string"&&(n=at().getGlobalEventTarget(this.doc,n),!n))throw new S(5102,!1);let o=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(o=this.tracingService.wrapEventListener(n,e,o)),this.eventManager.addEventListener(n,e,o,i)}decoratePreventDefault(n){return e=>{if(e==="__ngUnwrap__")return n;(this.platformIsServer?this.ngZone.runGuarded(()=>n(e)):n(e))===!1&&e.preventDefault()}}};function ki(t){return t.tagName==="TEMPLATE"&&t.content!==void 0}var Yr=class extends lt{sharedStylesHost;hostEl;shadowRoot;constructor(n,e,r,i,o,s,a,c,u){super(n,o,s,c,u),this.sharedStylesHost=e,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=i.styles;l=ji(i.id,l);for(let b of l){let v=document.createElement("style");a&&v.setAttribute("nonce",a),v.textContent=b,this.shadowRoot.appendChild(v)}let g=i.getExternalStyles?.();if(g)for(let b of g){let v=Jr(b,o);a&&v.setAttribute("nonce",a),this.shadowRoot.appendChild(v)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,e){return super.appendChild(this.nodeOrShadowRoot(n),e)}insertBefore(n,e,r){return super.insertBefore(this.nodeOrShadowRoot(n),e,r)}removeChild(n,e){return super.removeChild(null,e)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},dt=class extends lt{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,e,r,i,o,s,a,c,u){super(n,o,s,a,c),this.sharedStylesHost=e,this.removeStylesOnCompDestroy=i;let l=r.styles;this.styles=u?ji(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Bt=class extends dt{contentAttr;hostAttr;constructor(n,e,r,i,o,s,a,c,u){let l=i+"-"+r.id;super(n,e,r,o,s,a,c,u,l),this.contentAttr=hs(l),this.hostAttr=fs(l)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,e){let r=super.createElement(n,e);return super.setAttribute(r,this.contentAttr,""),r}};var qt=class t extends Di{supportsDOMEvents=!0;static makeCurrent(){Ai(new t)}onAndCancel(n,e,r,i){return n.addEventListener(e,r,i),()=>{n.removeEventListener(e,r,i)}}dispatchEvent(n,e){n.dispatchEvent(e)}remove(n){n.remove()}createElement(n,e){return e=e||this.getDefaultDocument(),e.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,e){return e==="window"?window:e==="document"?n:e==="body"?n.body:null}getBaseHref(n){let e=gs();return e==null?null:ms(e)}resetBaseElement(){ft=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return Ft(document.cookie,n)}},ft=null;function gs(){return ft=ft||document.querySelector("base"),ft?ft.getAttribute("href"):null}function ms(t){return new URL(t,document.baseURI).pathname}var vs=(()=>{class t{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),$i=(()=>{class t extends ut{constructor(e){super(e)}supports(e){return!0}addEventListener(e,r,i,o){return e.addEventListener(r,i,o),()=>this.removeEventListener(e,r,i,o)}removeEventListener(e,r,i,o){return e.removeEventListener(r,i,o)}static \u0275fac=function(r){return new(r||t)(m(_))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),Fi=["alt","control","meta","shift"],ys={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Rs={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey},zi=(()=>{class t extends ut{constructor(e){super(e)}supports(e){return t.parseEventName(e)!=null}addEventListener(e,r,i,o){let s=t.parseEventName(r),a=t.eventCallback(s.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>at().onAndCancel(e,s.domEventName,a,o))}static parseEventName(e){let r=e.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let o=t._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Fi.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=o,r.length!=0||o.length===0)return null;let c={};return c.domEventName=i,c.fullKey=s,c}static matchEventFullKeyCode(e,r){let i=ys[e.key]||e.key,o="";return r.indexOf("code.")>-1&&(i=e.code,o="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),Fi.forEach(s=>{if(s!==i){let a=Rs[s];a(e)&&(o+=s+".")}}),o+=i,o===r)}static eventCallback(e,r,i){return o=>{t.matchEventFullKeyCode(o,e)&&i.runGuarded(()=>r(o))}}static _normalizeKey(e){return e==="esc"?"escape":e}static \u0275fac=function(r){return new(r||t)(m(_))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})();function ws(t,n){return Ii(h({rootComponent:t},Ss(n)))}function Ss(t){return{appProviders:[...Is,...t?.providers??[]],platformProviders:Cs}}function Ts(){qt.makeCurrent()}function Es(){return new Lr}function bs(){return ei(document),document}var Cs=[{provide:rt,useValue:Ni},{provide:ti,useValue:Ts,multi:!0},{provide:_,useFactory:bs}];var Is=[{provide:Yn,useValue:"root"},{provide:Lr,useFactory:Es},{provide:Vt,useClass:$i,multi:!0,deps:[_]},{provide:Vt,useClass:zi,multi:!0,deps:[_]},en,Kr,Zr,{provide:fi,useExisting:en},{provide:$t,useClass:vs},[]];var Oe=class{},_e=class{},W=class t{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(n){n?typeof n=="string"?this.lazyInit=()=>{this.headers=new Map,n.split(`
`).forEach(e=>{let r=e.indexOf(":");if(r>0){let i=e.slice(0,r),o=e.slice(r+1).trim();this.addHeaderEntry(i,o)}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((e,r)=>{this.addHeaderEntry(r,e)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([e,r])=>{this.setHeaderEntries(e,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();let e=this.headers.get(n.toLowerCase());return e&&e.length>0?e[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,e){return this.clone({name:n,value:e,op:"a"})}set(n,e){return this.clone({name:n,value:e,op:"s"})}delete(n,e){return this.clone({name:n,value:e,op:"d"})}maybeSetNormalizedName(n,e){this.normalizedNames.has(e)||this.normalizedNames.set(e,n)}init(){this.lazyInit&&(this.lazyInit instanceof t?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(e=>{this.headers.set(e,n.headers.get(e)),this.normalizedNames.set(e,n.normalizedNames.get(e))})}clone(n){let e=new t;return e.lazyInit=this.lazyInit&&this.lazyInit instanceof t?this.lazyInit:this,e.lazyUpdate=(this.lazyUpdate||[]).concat([n]),e}applyUpdate(n){let e=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(n.name,e);let i=(n.op==="a"?this.headers.get(e):void 0)||[];i.push(...r),this.headers.set(e,i);break;case"d":let o=n.value;if(!o)this.headers.delete(e),this.normalizedNames.delete(e);else{let s=this.headers.get(e);if(!s)return;s=s.filter(a=>o.indexOf(a)===-1),s.length===0?(this.headers.delete(e),this.normalizedNames.delete(e)):this.headers.set(e,s)}break}}addHeaderEntry(n,e){let r=n.toLowerCase();this.maybeSetNormalizedName(n,r),this.headers.has(r)?this.headers.get(r).push(e):this.headers.set(r,[e])}setHeaderEntries(n,e){let r=(Array.isArray(e)?e:[e]).map(o=>o.toString()),i=n.toLowerCase();this.headers.set(i,r),this.maybeSetNormalizedName(n,i)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(e=>n(this.normalizedNames.get(e),this.headers.get(e)))}};var Wt=class{encodeKey(n){return Bi(n)}encodeValue(n){return Bi(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}};function Ms(t,n){let e=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(i=>{let o=i.indexOf("="),[s,a]=o==-1?[n.decodeKey(i),""]:[n.decodeKey(i.slice(0,o)),n.decodeValue(i.slice(o+1))],c=e.get(s)||[];c.push(a),e.set(s,c)}),e}var As=/%(\d[a-f0-9])/gi,Ds={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Bi(t){return encodeURIComponent(t).replace(As,(n,e)=>Ds[e]??n)}function Ht(t){return`${t}`}var ne=class t{map;encoder;updates=null;cloneFrom=null;constructor(n={}){if(this.encoder=n.encoder||new Wt,n.fromString){if(n.fromObject)throw new S(2805,!1);this.map=Ms(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(e=>{let r=n.fromObject[e],i=Array.isArray(r)?r.map(Ht):[Ht(r)];this.map.set(e,i)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();let e=this.map.get(n);return e?e[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,e){return this.clone({param:n,value:e,op:"a"})}appendAll(n){let e=[];return Object.keys(n).forEach(r=>{let i=n[r];Array.isArray(i)?i.forEach(o=>{e.push({param:r,value:o,op:"a"})}):e.push({param:r,value:i,op:"a"})}),this.clone(e)}set(n,e){return this.clone({param:n,value:e,op:"s"})}delete(n,e){return this.clone({param:n,value:e,op:"d"})}toString(){return this.init(),this.keys().map(n=>{let e=this.encoder.encodeKey(n);return this.map.get(n).map(r=>e+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>n!=="").join("&")}clone(n){let e=new t({encoder:this.encoder});return e.cloneFrom=this.cloneFrom||this,e.updates=(this.updates||[]).concat(n),e}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":let e=(n.op==="a"?this.map.get(n.param):void 0)||[];e.push(Ht(n.value)),this.map.set(n.param,e);break;case"d":if(n.value!==void 0){let r=this.map.get(n.param)||[],i=r.indexOf(Ht(n.value));i!==-1&&r.splice(i,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}else{this.map.delete(n.param);break}}}),this.cloneFrom=this.updates=null)}},rn=class{defaultValue;constructor(n){this.defaultValue=n}},Xt=class{map=new Map;set(n,e){return this.map.set(n,e),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}};function Os(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Vi(t){return typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer}function qi(t){return typeof Blob<"u"&&t instanceof Blob}function Hi(t){return typeof FormData<"u"&&t instanceof FormData}function _s(t){return typeof URLSearchParams<"u"&&t instanceof URLSearchParams}var pt="Content-Type",Jt="Accept",an="X-Request-URL",Xi="text/plain",Ji="application/json",Yi=`${Ji}, ${Xi}, */*`,De=class t{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(n,e,r,i){this.url=e,this.method=n.toUpperCase();let o;if(Os(this.method)||i?(this.body=r!==void 0?r:null,o=i):o=r,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params),this.transferCache=o.transferCache),this.headers??=new W,this.context??=new Xt,!this.params)this.params=new ne,this.urlWithParams=e;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=e;else{let a=e.indexOf("?"),c=a===-1?"?":a<e.length-1?"&":"";this.urlWithParams=e+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Vi(this.body)||qi(this.body)||Hi(this.body)||_s(this.body)?this.body:this.body instanceof ne?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Hi(this.body)?null:qi(this.body)?this.body.type||null:Vi(this.body)?null:typeof this.body=="string"?Xi:this.body instanceof ne?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?Ji:null}clone(n={}){let e=n.method||this.method,r=n.url||this.url,i=n.responseType||this.responseType,o=n.transferCache??this.transferCache,s=n.body!==void 0?n.body:this.body,a=n.withCredentials??this.withCredentials,c=n.reportProgress??this.reportProgress,u=n.headers||this.headers,l=n.params||this.params,g=n.context??this.context;return n.setHeaders!==void 0&&(u=Object.keys(n.setHeaders).reduce((b,v)=>b.set(v,n.setHeaders[v]),u)),n.setParams&&(l=Object.keys(n.setParams).reduce((b,v)=>b.set(v,n.setParams[v]),l)),new t(e,r,s,{params:l,headers:u,context:g,reportProgress:c,responseType:i,withCredentials:a,transferCache:o})}},ie=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}(ie||{}),Pe=class{headers;status;statusText;url;ok;type;constructor(n,e=200,r="OK"){this.headers=n.headers||new W,this.status=n.status!==void 0?n.status:e,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}},gt=class t extends Pe{constructor(n={}){super(n)}type=ie.ResponseHeader;clone(n={}){return new t({headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Ne=class t extends Pe{body;constructor(n={}){super(n),this.body=n.body!==void 0?n.body:null}type=ie.Response;clone(n={}){return new t({body:n.body!==void 0?n.body:this.body,headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},re=class extends Pe{name="HttpErrorResponse";message;error;ok=!1;constructor(n){super(n,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${n.url||"(unknown url)"}`:this.message=`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}},Zi=200,Ps=204;function tn(t,n){return{body:n,headers:t.headers,context:t.context,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials,transferCache:t.transferCache}}var Ki=(()=>{class t{handler;constructor(e){this.handler=e}request(e,r,i={}){let o;if(e instanceof De)o=e;else{let c;i.headers instanceof W?c=i.headers:c=new W(i.headers);let u;i.params&&(i.params instanceof ne?u=i.params:u=new ne({fromObject:i.params})),o=new De(e,r,i.body!==void 0?i.body:null,{headers:c,context:i.context,params:u,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let s=f(o).pipe(te(c=>this.handler.handle(c)));if(e instanceof De||i.observe==="events")return s;let a=s.pipe(G(c=>c instanceof Ne));switch(i.observe||"body"){case"body":switch(o.responseType){case"arraybuffer":return a.pipe(T(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new S(2806,!1);return c.body}));case"blob":return a.pipe(T(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new S(2807,!1);return c.body}));case"text":return a.pipe(T(c=>{if(c.body!==null&&typeof c.body!="string")throw new S(2808,!1);return c.body}));case"json":default:return a.pipe(T(c=>c.body))}case"response":return a;default:throw new S(2809,!1)}}delete(e,r={}){return this.request("DELETE",e,r)}get(e,r={}){return this.request("GET",e,r)}head(e,r={}){return this.request("HEAD",e,r)}jsonp(e,r){return this.request("JSONP",e,{params:new ne().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(e,r={}){return this.request("OPTIONS",e,r)}patch(e,r,i={}){return this.request("PATCH",e,tn(i,r))}post(e,r,i={}){return this.request("POST",e,tn(i,r))}put(e,r,i={}){return this.request("PUT",e,tn(i,r))}static \u0275fac=function(r){return new(r||t)(m(Oe))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),Ns=/^\)\]\}',?\n/;function Gi(t){if(t.url)return t.url;let n=an.toLocaleLowerCase();return t.headers.get(n)}var Qi=new C(""),Gt=(()=>{class t{fetchImpl=d(nn,{optional:!0})?.fetch??((...e)=>globalThis.fetch(...e));ngZone=d(B);handle(e){return new Dr(r=>{let i=new AbortController;return this.doRequest(e,i.signal,r).then(on,o=>r.error(new re({error:o}))),()=>i.abort()})}doRequest(e,r,i){return Ar(this,null,function*(){let o=this.createRequestInit(e),s;try{let v=this.ngZone.runOutsideAngular(()=>this.fetchImpl(e.urlWithParams,h({signal:r},o)));Us(v),i.next({type:ie.Sent}),s=yield v}catch(v){i.error(new re({error:v,status:v.status??0,statusText:v.statusText,url:e.urlWithParams,headers:v.headers}));return}let a=new W(s.headers),c=s.statusText,u=Gi(s)??e.urlWithParams,l=s.status,g=null;if(e.reportProgress&&i.next(new gt({headers:a,status:l,statusText:c,url:u})),s.body){let v=s.headers.get("content-length"),M=[],w=s.body.getReader(),R=0,F,oe,I=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>Ar(this,null,function*(){for(;;){let{done:pe,value:Xe}=yield w.read();if(pe)break;if(M.push(Xe),R+=Xe.length,e.reportProgress){oe=e.responseType==="text"?(oe??"")+(F??=new TextDecoder).decode(Xe,{stream:!0}):void 0;let Fn=()=>i.next({type:ie.DownloadProgress,total:v?+v:void 0,loaded:R,partialText:oe});I?I.run(Fn):Fn()}}}));let We=this.concatChunks(M,R);try{let pe=s.headers.get(pt)??"";g=this.parseBody(e,We,pe)}catch(pe){i.error(new re({error:pe,headers:new W(s.headers),status:s.status,statusText:s.statusText,url:Gi(s)??e.urlWithParams}));return}}l===0&&(l=g?Zi:0),l>=200&&l<300?(i.next(new Ne({body:g,headers:a,status:l,statusText:c,url:u})),i.complete()):i.error(new re({error:g,headers:a,status:l,statusText:c,url:u}))})}parseBody(e,r,i){switch(e.responseType){case"json":let o=new TextDecoder().decode(r).replace(Ns,"");return o===""?null:JSON.parse(o);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:i});case"arraybuffer":return r.buffer}}createRequestInit(e){let r={},i=e.withCredentials?"include":void 0;if(e.headers.forEach((o,s)=>r[o]=s.join(",")),e.headers.has(Jt)||(r[Jt]=Yi),!e.headers.has(pt)){let o=e.detectContentTypeHeader();o!==null&&(r[pt]=o)}return{body:e.serializeBody(),method:e.method,headers:r,credentials:i}}concatChunks(e,r){let i=new Uint8Array(r),o=0;for(let s of e)i.set(s,o),o+=s.length;return i}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),nn=class{};function on(){}function Us(t){t.then(on,on)}function eo(t,n){return n(t)}function xs(t,n){return(e,r)=>n.intercept(e,{handle:i=>t(i,r)})}function ks(t,n,e){return(r,i)=>z(e,()=>n(r,o=>t(o,i)))}var to=new C(""),Zt=new C(""),ro=new C(""),cn=new C("",{providedIn:"root",factory:()=>!0});function Ls(){let t=null;return(n,e)=>{t===null&&(t=(d(to,{optional:!0})??[]).reduceRight(xs,eo));let r=d(tt);if(d(cn)){let o=r.add();return t(n,e).pipe(me(()=>r.remove(o)))}else return t(n,e)}}var Yt=(()=>{class t extends Oe{backend;injector;chain=null;pendingTasks=d(tt);contributeToStability=d(cn);constructor(e,r){super(),this.backend=e,this.injector=r}handle(e){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Zt),...this.injector.get(ro,[])]));this.chain=r.reduceRight((i,o)=>ks(i,o,this.injector),eo)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(e,i=>this.backend.handle(i)).pipe(me(()=>this.pendingTasks.remove(r)))}else return this.chain(e,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||t)(m(_e),m(ae))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})();var js=/^\)\]\}',?\n/,Fs=RegExp(`^${an}:`,"m");function $s(t){return"responseURL"in t&&t.responseURL?t.responseURL:Fs.test(t.getAllResponseHeaders())?t.getResponseHeader(an):null}var sn=(()=>{class t{xhrFactory;constructor(e){this.xhrFactory=e}handle(e){if(e.method==="JSONP")throw new S(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?D(r.\u0275loadImpl()):f(null)).pipe(x(()=>new Dr(o=>{let s=r.build();if(s.open(e.method,e.urlWithParams),e.withCredentials&&(s.withCredentials=!0),e.headers.forEach((w,R)=>s.setRequestHeader(w,R.join(","))),e.headers.has(Jt)||s.setRequestHeader(Jt,Yi),!e.headers.has(pt)){let w=e.detectContentTypeHeader();w!==null&&s.setRequestHeader(pt,w)}if(e.responseType){let w=e.responseType.toLowerCase();s.responseType=w!=="json"?w:"text"}let a=e.serializeBody(),c=null,u=()=>{if(c!==null)return c;let w=s.statusText||"OK",R=new W(s.getAllResponseHeaders()),F=$s(s)||e.url;return c=new gt({headers:R,status:s.status,statusText:w,url:F}),c},l=()=>{let{headers:w,status:R,statusText:F,url:oe}=u(),I=null;R!==Ps&&(I=typeof s.response>"u"?s.responseText:s.response),R===0&&(R=I?Zi:0);let We=R>=200&&R<300;if(e.responseType==="json"&&typeof I=="string"){let pe=I;I=I.replace(js,"");try{I=I!==""?JSON.parse(I):null}catch(Xe){I=pe,We&&(We=!1,I={error:Xe,text:I})}}We?(o.next(new Ne({body:I,headers:w,status:R,statusText:F,url:oe||void 0})),o.complete()):o.error(new re({error:I,headers:w,status:R,statusText:F,url:oe||void 0}))},g=w=>{let{url:R}=u(),F=new re({error:w,status:s.status||0,statusText:s.statusText||"Unknown Error",url:R||void 0});o.error(F)},b=!1,v=w=>{b||(o.next(u()),b=!0);let R={type:ie.DownloadProgress,loaded:w.loaded};w.lengthComputable&&(R.total=w.total),e.responseType==="text"&&s.responseText&&(R.partialText=s.responseText),o.next(R)},M=w=>{let R={type:ie.UploadProgress,loaded:w.loaded};w.lengthComputable&&(R.total=w.total),o.next(R)};return s.addEventListener("load",l),s.addEventListener("error",g),s.addEventListener("timeout",g),s.addEventListener("abort",g),e.reportProgress&&(s.addEventListener("progress",v),a!==null&&s.upload&&s.upload.addEventListener("progress",M)),s.send(a),o.next({type:ie.Sent}),()=>{s.removeEventListener("error",g),s.removeEventListener("abort",g),s.removeEventListener("load",l),s.removeEventListener("timeout",g),e.reportProgress&&(s.removeEventListener("progress",v),a!==null&&s.upload&&s.upload.removeEventListener("progress",M)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||t)(m($t))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})(),no=new C(""),zs="XSRF-TOKEN",io=new C("",{providedIn:"root",factory:()=>zs}),Bs="X-XSRF-TOKEN",oo=new C("",{providedIn:"root",factory:()=>Bs}),mt=class{},Vs=(()=>{class t{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(e,r){this.doc=e,this.cookieName=r}getToken(){let e=this.doc.cookie||"";return e!==this.lastCookieString&&(this.parseCount++,this.lastToken=Ft(e,this.cookieName),this.lastCookieString=e),this.lastToken}static \u0275fac=function(r){return new(r||t)(m(_),m(io))};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})();function qs(t,n){let e=t.url.toLowerCase();if(!d(no)||t.method==="GET"||t.method==="HEAD"||e.startsWith("http://")||e.startsWith("https://"))return n(t);let r=d(mt).getToken(),i=d(oo);return r!=null&&!t.headers.has(i)&&(t=t.clone({headers:t.headers.set(i,r)})),n(t)}var Ue=function(t){return t[t.Interceptors=0]="Interceptors",t[t.LegacyInterceptors=1]="LegacyInterceptors",t[t.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",t[t.NoXsrfProtection=3]="NoXsrfProtection",t[t.JsonpSupport=4]="JsonpSupport",t[t.RequestsMadeViaParent=5]="RequestsMadeViaParent",t[t.Fetch=6]="Fetch",t}(Ue||{});function Kt(t,n){return{\u0275kind:t,\u0275providers:n}}function Hs(...t){let n=[Ki,sn,Yt,{provide:Oe,useExisting:Yt},{provide:_e,useFactory:()=>d(Qi,{optional:!0})??d(sn)},{provide:Zt,useValue:qs,multi:!0},{provide:no,useValue:!0},{provide:mt,useClass:Vs}];for(let e of t)n.push(...e.\u0275providers);return Ke(n)}function Gs(t){return Kt(Ue.Interceptors,t.map(n=>({provide:Zt,useValue:n,multi:!0})))}var Wi=new C("");function Ws(){return Kt(Ue.LegacyInterceptors,[{provide:Wi,useFactory:Ls},{provide:Zt,useExisting:Wi,multi:!0}])}function Xs({cookieName:t,headerName:n}){let e=[];return t!==void 0&&e.push({provide:io,useValue:t}),n!==void 0&&e.push({provide:oo,useValue:n}),Kt(Ue.CustomXsrfConfiguration,e)}function Js(){return Kt(Ue.Fetch,[Gt,{provide:Qi,useExisting:Gt},{provide:_e,useExisting:Gt}])}var so=(()=>{class t{_doc;constructor(e){this._doc=e}getTitle(){return this._doc.title}setTitle(e){this._doc.title=e||""}static \u0275fac=function(r){return new(r||t)(m(_))};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var Zs=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:function(r){let i=null;return r?i=new(r||t):i=m(Ks),i},providedIn:"root"})}return t})(),Ks=(()=>{class t extends Zs{_doc;constructor(e){super(),this._doc=e}sanitize(e,r){if(r==null)return null;switch(e){case ye.NONE:return r;case ye.HTML:return Me(r,"HTML")?Ie(r):di(this._doc,String(r)).toString();case ye.STYLE:return Me(r,"Style")?Ie(r):r;case ye.SCRIPT:if(Me(r,"Script"))return Ie(r);throw new S(5200,!1);case ye.URL:return Me(r,"URL")?Ie(r):li(String(r));case ye.RESOURCE_URL:if(Me(r,"ResourceURL"))return Ie(r);throw new S(5201,!1);default:throw new S(5202,!1)}}bypassSecurityTrustHtml(e){return oi(e)}bypassSecurityTrustStyle(e){return si(e)}bypassSecurityTrustScript(e){return ai(e)}bypassSecurityTrustUrl(e){return ci(e)}bypassSecurityTrustResourceUrl(e){return ui(e)}static \u0275fac=function(r){return new(r||t)(m(_))};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var p="primary",Dt=Symbol("RouteTitle"),fn=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e[0]:e}return null}getAll(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function Se(t){return new fn(t)}function go(t,n,e){let r=e.path.split("/");if(r.length>t.length||e.pathMatch==="full"&&(n.hasChildren()||r.length<t.length))return null;let i={};for(let o=0;o<r.length;o++){let s=r[o],a=t[o];if(s[0]===":")i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:t.slice(0,r.length),posParams:i}}function ea(t,n){if(t.length!==n.length)return!1;for(let e=0;e<t.length;++e)if(!X(t[e],n[e]))return!1;return!0}function X(t,n){let e=t?pn(t):void 0,r=n?pn(n):void 0;if(!e||!r||e.length!=r.length)return!1;let i;for(let o=0;o<e.length;o++)if(i=e[o],!mo(t[i],n[i]))return!1;return!0}function pn(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function mo(t,n){if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return!1;let e=[...t].sort(),r=[...n].sort();return e.every((i,o)=>r[o]===i)}else return t===n}function vo(t){return t.length>0?t[t.length-1]:null}function he(t){return Bn(t)?t:yi(t)?D(Promise.resolve(t)):f(t)}var ta={exact:Ro,subset:wo},yo={exact:ra,subset:na,ignored:()=>!0};function ao(t,n,e){return ta[e.paths](t.root,n.root,e.matrixParams)&&yo[e.queryParams](t.queryParams,n.queryParams)&&!(e.fragment==="exact"&&t.fragment!==n.fragment)}function ra(t,n){return X(t,n)}function Ro(t,n,e){if(!Re(t.segments,n.segments)||!tr(t.segments,n.segments,e)||t.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!t.children[r]||!Ro(t.children[r],n.children[r],e))return!1;return!0}function na(t,n){return Object.keys(n).length<=Object.keys(t).length&&Object.keys(n).every(e=>mo(t[e],n[e]))}function wo(t,n,e){return So(t,n,n.segments,e)}function So(t,n,e,r){if(t.segments.length>e.length){let i=t.segments.slice(0,e.length);return!(!Re(i,e)||n.hasChildren()||!tr(i,e,r))}else if(t.segments.length===e.length){if(!Re(t.segments,e)||!tr(t.segments,e,r))return!1;for(let i in n.children)if(!t.children[i]||!wo(t.children[i],n.children[i],r))return!1;return!0}else{let i=e.slice(0,t.segments.length),o=e.slice(t.segments.length);return!Re(t.segments,i)||!tr(t.segments,i,r)||!t.children[p]?!1:So(t.children[p],n,o,r)}}function tr(t,n,e){return n.every((r,i)=>yo[e](t[i].parameters,r.parameters))}var Y=class{root;queryParams;fragment;_queryParamMap;constructor(n=new E([],{}),e={},r=null){this.root=n,this.queryParams=e,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Se(this.queryParams),this._queryParamMap}toString(){return sa.serialize(this)}},E=class{segments;children;parent=null;constructor(n,e){this.segments=n,this.children=e,Object.values(e).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return rr(this)}},ce=class{path;parameters;_parameterMap;constructor(n,e){this.path=n,this.parameters=e}get parameterMap(){return this._parameterMap??=Se(this.parameters),this._parameterMap}toString(){return Eo(this)}};function ia(t,n){return Re(t,n)&&t.every((e,r)=>X(e.parameters,n[r].parameters))}function Re(t,n){return t.length!==n.length?!1:t.every((e,r)=>e.path===n[r].path)}function oa(t,n){let e=[];return Object.entries(t.children).forEach(([r,i])=>{r===p&&(e=e.concat(n(i,r)))}),Object.entries(t.children).forEach(([r,i])=>{r!==p&&(e=e.concat(n(i,r)))}),e}var fe=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:()=>new ue,providedIn:"root"})}return t})(),ue=class{parse(n){let e=new mn(n);return new Y(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(n){let e=`/${vt(n.root,!0)}`,r=ua(n.queryParams),i=typeof n.fragment=="string"?`#${aa(n.fragment)}`:"";return`${e}${r}${i}`}},sa=new ue;function rr(t){return t.segments.map(n=>Eo(n)).join("/")}function vt(t,n){if(!t.hasChildren())return rr(t);if(n){let e=t.children[p]?vt(t.children[p],!1):"",r=[];return Object.entries(t.children).forEach(([i,o])=>{i!==p&&r.push(`${i}:${vt(o,!1)}`)}),r.length>0?`${e}(${r.join("//")})`:e}else{let e=oa(t,(r,i)=>i===p?[vt(t.children[p],!1)]:[`${i}:${vt(r,!1)}`]);return Object.keys(t.children).length===1&&t.children[p]!=null?`${rr(t)}/${e[0]}`:`${rr(t)}/(${e.join("//")})`}}function To(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Qt(t){return To(t).replace(/%3B/gi,";")}function aa(t){return encodeURI(t)}function gn(t){return To(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function nr(t){return decodeURIComponent(t)}function co(t){return nr(t.replace(/\+/g,"%20"))}function Eo(t){return`${gn(t.path)}${ca(t.parameters)}`}function ca(t){return Object.entries(t).map(([n,e])=>`;${gn(n)}=${gn(e)}`).join("")}function ua(t){let n=Object.entries(t).map(([e,r])=>Array.isArray(r)?r.map(i=>`${Qt(e)}=${Qt(i)}`).join("&"):`${Qt(e)}=${Qt(r)}`).filter(e=>e);return n.length?`?${n.join("&")}`:""}var la=/^[^\/()?;#]+/;function un(t){let n=t.match(la);return n?n[0]:""}var da=/^[^\/()?;=#]+/;function ha(t){let n=t.match(da);return n?n[0]:""}var fa=/^[^=?&#]+/;function pa(t){let n=t.match(fa);return n?n[0]:""}var ga=/^[^&#]+/;function ma(t){let n=t.match(ga);return n?n[0]:""}var mn=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new E([],{}):new E([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(e).length>0)&&(r[p]=new E(n,e)),r}parseSegment(){let n=un(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new S(4009,!1);return this.capture(n),new ce(nr(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let e=ha(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let i=un(this.remaining);i&&(r=i,this.capture(r))}n[nr(e)]=nr(r)}parseQueryParam(n){let e=pa(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let s=ma(this.remaining);s&&(r=s,this.capture(r))}let i=co(e),o=co(r);if(n.hasOwnProperty(i)){let s=n[i];Array.isArray(s)||(s=[s],n[i]=s),s.push(o)}else n[i]=o}parseParens(n){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=un(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new S(4010,!1);let o;r.indexOf(":")>-1?(o=r.slice(0,r.indexOf(":")),this.capture(o),this.capture(":")):n&&(o=p);let s=this.parseChildren();e[o]=Object.keys(s).length===1?s[p]:new E([],s),this.consumeOptional("//")}return e}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new S(4011,!1)}};function bo(t){return t.segments.length>0?new E([],{[p]:t}):t}function Co(t){let n={};for(let[r,i]of Object.entries(t.children)){let o=Co(i);if(r===p&&o.segments.length===0&&o.hasChildren())for(let[s,a]of Object.entries(o.children))n[s]=a;else(o.segments.length>0||o.hasChildren())&&(n[r]=o)}let e=new E(t.segments,n);return va(e)}function va(t){if(t.numberOfChildren===1&&t.children[p]){let n=t.children[p];return new E(t.segments.concat(n.segments),n.children)}return t}function le(t){return t instanceof Y}function Io(t,n,e=null,r=null){let i=Mo(t);return Ao(i,n,e,r)}function Mo(t){let n;function e(o){let s={};for(let c of o.children){let u=e(c);s[c.outlet]=u}let a=new E(o.url,s);return o===t&&(n=a),a}let r=e(t.root),i=bo(r);return n??i}function Ao(t,n,e,r){let i=t;for(;i.parent;)i=i.parent;if(n.length===0)return ln(i,i,i,e,r);let o=ya(n);if(o.toRoot())return ln(i,i,new E([],{}),e,r);let s=Ra(o,i,t),a=s.processChildren?Rt(s.segmentGroup,s.index,o.commands):Oo(s.segmentGroup,s.index,o.commands);return ln(i,s.segmentGroup,a,e,r)}function or(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function St(t){return typeof t=="object"&&t!=null&&t.outlets}function ln(t,n,e,r,i){let o={};r&&Object.entries(r).forEach(([c,u])=>{o[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;t===n?s=e:s=Do(t,n,e);let a=bo(Co(s));return new Y(a,o,i)}function Do(t,n,e){let r={};return Object.entries(t.children).forEach(([i,o])=>{o===n?r[i]=e:r[i]=Do(o,n,e)}),new E(t.segments,r)}var sr=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,e,r){if(this.isAbsolute=n,this.numberOfDoubleDots=e,this.commands=r,n&&r.length>0&&or(r[0]))throw new S(4003,!1);let i=r.find(St);if(i&&i!==vo(r))throw new S(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function ya(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new sr(!0,0,t);let n=0,e=!1,r=t.reduce((i,o,s)=>{if(typeof o=="object"&&o!=null){if(o.outlets){let a={};return Object.entries(o.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...i,{outlets:a}]}if(o.segmentPath)return[...i,o.segmentPath]}return typeof o!="string"?[...i,o]:s===0?(o.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?e=!0:a===".."?n++:a!=""&&i.push(a))}),i):[...i,o]},[]);return new sr(e,n,r)}var Le=class{segmentGroup;processChildren;index;constructor(n,e,r){this.segmentGroup=n,this.processChildren=e,this.index=r}};function Ra(t,n,e){if(t.isAbsolute)return new Le(n,!0,0);if(!e)return new Le(n,!1,NaN);if(e.parent===null)return new Le(e,!0,0);let r=or(t.commands[0])?0:1,i=e.segments.length-1+r;return wa(e,i,t.numberOfDoubleDots)}function wa(t,n,e){let r=t,i=n,o=e;for(;o>i;){if(o-=i,r=r.parent,!r)throw new S(4005,!1);i=r.segments.length}return new Le(r,!1,i-o)}function Sa(t){return St(t[0])?t[0].outlets:{[p]:t}}function Oo(t,n,e){if(t??=new E([],{}),t.segments.length===0&&t.hasChildren())return Rt(t,n,e);let r=Ta(t,n,e),i=e.slice(r.commandIndex);if(r.match&&r.pathIndex<t.segments.length){let o=new E(t.segments.slice(0,r.pathIndex),{});return o.children[p]=new E(t.segments.slice(r.pathIndex),t.children),Rt(o,0,i)}else return r.match&&i.length===0?new E(t.segments,{}):r.match&&!t.hasChildren()?vn(t,n,e):r.match?Rt(t,0,i):vn(t,n,e)}function Rt(t,n,e){if(e.length===0)return new E(t.segments,{});{let r=Sa(e),i={};if(Object.keys(r).some(o=>o!==p)&&t.children[p]&&t.numberOfChildren===1&&t.children[p].segments.length===0){let o=Rt(t.children[p],n,e);return new E(t.segments,o.children)}return Object.entries(r).forEach(([o,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(i[o]=Oo(t.children[o],n,s))}),Object.entries(t.children).forEach(([o,s])=>{r[o]===void 0&&(i[o]=s)}),new E(t.segments,i)}}function Ta(t,n,e){let r=0,i=n,o={match:!1,pathIndex:0,commandIndex:0};for(;i<t.segments.length;){if(r>=e.length)return o;let s=t.segments[i],a=e[r];if(St(a))break;let c=`${a}`,u=r<e.length-1?e[r+1]:null;if(i>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!lo(c,u,s))return o;r+=2}else{if(!lo(c,{},s))return o;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function vn(t,n,e){let r=t.segments.slice(0,n),i=0;for(;i<e.length;){let o=e[i];if(St(o)){let c=Ea(o.outlets);return new E(r,c)}if(i===0&&or(e[0])){let c=t.segments[n];r.push(new ce(c.path,uo(e[0]))),i++;continue}let s=St(o)?o.outlets[p]:`${o}`,a=i<e.length-1?e[i+1]:null;s&&a&&or(a)?(r.push(new ce(s,uo(a))),i+=2):(r.push(new ce(s,{})),i++)}return new E(r,{})}function Ea(t){let n={};return Object.entries(t).forEach(([e,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[e]=vn(new E([],{}),0,r))}),n}function uo(t){let n={};return Object.entries(t).forEach(([e,r])=>n[e]=`${r}`),n}function lo(t,n,e){return t==e.path&&X(n,e.parameters)}var ir="imperative",A=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(A||{}),L=class{id;url;constructor(n,e){this.id=n,this.url=e}},de=class extends L{type=A.NavigationStart;navigationTrigger;restoredState;constructor(n,e,r="imperative",i=null){super(n,e),this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},j=class extends L{urlAfterRedirects;type=A.NavigationEnd;constructor(n,e,r){super(n,e),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},U=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t}(U||{}),Fe=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(Fe||{}),J=class extends L{reason;code;type=A.NavigationCancel;constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Z=class extends L{reason;code;type=A.NavigationSkipped;constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i}},$e=class extends L{error;target;type=A.NavigationError;constructor(n,e,r,i){super(n,e),this.error=r,this.target=i}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Tt=class extends L{urlAfterRedirects;state;type=A.RoutesRecognized;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ar=class extends L{urlAfterRedirects;state;type=A.GuardsCheckStart;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},cr=class extends L{urlAfterRedirects;state;shouldActivate;type=A.GuardsCheckEnd;constructor(n,e,r,i,o){super(n,e),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=o}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},ur=class extends L{urlAfterRedirects;state;type=A.ResolveStart;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},lr=class extends L{urlAfterRedirects;state;type=A.ResolveEnd;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},dr=class{route;type=A.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},hr=class{route;type=A.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},fr=class{snapshot;type=A.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},pr=class{snapshot;type=A.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},gr=class{snapshot;type=A.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},mr=class{snapshot;type=A.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ze=class{routerEvent;position;anchor;type=A.Scroll;constructor(n,e,r){this.routerEvent=n,this.position=e,this.anchor=r}toString(){let n=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${n}')`}},Et=class{},Be=class{url;navigationBehaviorOptions;constructor(n,e){this.url=n,this.navigationBehaviorOptions=e}};function ba(t,n){return t.providers&&!t._injector&&(t._injector=kt(t.providers,n,`Route: ${t.path}`)),t._injector??n}function q(t){return t.outlet||p}function Ca(t,n){let e=t.filter(r=>q(r)===n);return e.push(...t.filter(r=>q(r)!==n)),e}function Ot(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let n=t.parent;n;n=n.parent){let e=n.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var vr=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Ot(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new Te(this.rootInjector)}},Te=(()=>{class t{rootInjector;contexts=new Map;constructor(e){this.rootInjector=e}onChildOutletCreated(e,r){let i=this.getOrCreateContext(e);i.outlet=r,this.contexts.set(e,i)}onChildOutletDestroyed(e){let r=this.getContext(e);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let r=this.getContext(e);return r||(r=new vr(this.rootInjector),this.contexts.set(e,r)),r}getContext(e){return this.contexts.get(e)||null}static \u0275fac=function(r){return new(r||t)(m(ae))};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),yr=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let e=this.pathFromRoot(n);return e.length>1?e[e.length-2]:null}children(n){let e=yn(n,this._root);return e?e.children.map(r=>r.value):[]}firstChild(n){let e=yn(n,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(n){let e=Rn(n,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==n)}pathFromRoot(n){return Rn(n,this._root).map(e=>e.value)}};function yn(t,n){if(t===n.value)return n;for(let e of n.children){let r=yn(t,e);if(r)return r}return null}function Rn(t,n){if(t===n.value)return[n];for(let e of n.children){let r=Rn(t,e);if(r.length)return r.unshift(n),r}return[]}var k=class{value;children;constructor(n,e){this.value=n,this.children=e}toString(){return`TreeNode(${this.value})`}};function ke(t){let n={};return t&&t.children.forEach(e=>n[e.value.outlet]=e),n}var bt=class extends yr{snapshot;constructor(n,e){super(n),this.snapshot=e,Mn(this,n)}toString(){return this.snapshot.toString()}};function _o(t){let n=Ia(t),e=new $([new ce("",{})]),r=new $({}),i=new $({}),o=new $({}),s=new $(""),a=new K(e,r,o,s,i,p,t,n.root);return a.snapshot=n.root,new bt(new k(a,[]),n)}function Ia(t){let n={},e={},r={},i="",o=new we([],n,r,i,e,p,t,null,{});return new Ct("",new k(o,[]))}var K=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,e,r,i,o,s,a,c){this.urlSubject=n,this.paramsSubject=e,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=o,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(T(u=>u[Dt]))??f(void 0),this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=o}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(T(n=>Se(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(T(n=>Se(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Rr(t,n,e="emptyOnly"){let r,{routeConfig:i}=t;return n!==null&&(e==="always"||i?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:h(h({},n.params),t.params),data:h(h({},n.data),t.data),resolve:h(h(h(h({},t.data),n.data),i?.data),t._resolvedData)}:r={params:h({},t.params),data:h({},t.data),resolve:h(h({},t.data),t._resolvedData??{})},i&&No(i)&&(r.resolve[Dt]=i.title),r}var we=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Dt]}constructor(n,e,r,i,o,s,a,c,u){this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Se(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Se(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${e}')`}},Ct=class extends yr{url;constructor(n,e){super(e),this.url=n,Mn(this,e)}toString(){return Po(this._root)}};function Mn(t,n){n.value._routerState=t,n.children.forEach(e=>Mn(t,e))}function Po(t){let n=t.children.length>0?` { ${t.children.map(Po).join(", ")} } `:"";return`${t.value}${n}`}function dn(t){if(t.snapshot){let n=t.snapshot,e=t._futureSnapshot;t.snapshot=e,X(n.queryParams,e.queryParams)||t.queryParamsSubject.next(e.queryParams),n.fragment!==e.fragment&&t.fragmentSubject.next(e.fragment),X(n.params,e.params)||t.paramsSubject.next(e.params),ea(n.url,e.url)||t.urlSubject.next(e.url),X(n.data,e.data)||t.dataSubject.next(e.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function wn(t,n){let e=X(t.params,n.params)&&ia(t.url,n.url),r=!t.parent!=!n.parent;return e&&!r&&(!t.parent||wn(t.parent,n.parent))}function No(t){return typeof t.title=="string"||t.title===null}var Uo=new C(""),An=(()=>{class t{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=p;activateEvents=new ve;deactivateEvents=new ve;attachEvents=new ve;detachEvents=new ve;routerOutletData=Qn(void 0);parentContexts=d(Te);location=d(gi);changeDetector=d(Lt);inputBinder=d(_t,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(e){if(e.name){let{firstChange:r,previousValue:i}=e.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new S(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new S(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new S(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,r){this.activated=e,this._activatedRoute=r,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,r){if(this.isActivated)throw new S(4013,!1);this._activatedRoute=e;let i=this.location,s=e.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Sn(e,a,i.injector,this.routerOutletData);this.activated=i.createComponent(s,{index:i.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||t)};static \u0275dir=st({type:t,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Qe]})}return t})(),Sn=class{route;childContexts;parent;outletData;constructor(n,e,r,i){this.route=n,this.childContexts=e,this.parent=r,this.outletData=i}get(n,e){return n===K?this.route:n===Te?this.childContexts:n===Uo?this.outletData:this.parent.get(n,e)}},_t=new C(""),Dn=(()=>{class t{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(e){this.unsubscribeFromRouteData(e),this.subscribeToRouteData(e)}unsubscribeFromRouteData(e){this.outletDataSubscriptions.get(e)?.unsubscribe(),this.outletDataSubscriptions.delete(e)}subscribeToRouteData(e){let{activatedRoute:r}=e,i=xt([r.queryParams,r.params,r.data]).pipe(x(([o,s,a],c)=>(a=h(h(h({},o),s),a),c===0?f(a):Promise.resolve(a)))).subscribe(o=>{if(!e.isActivated||!e.activatedComponentRef||e.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(e);return}let s=Mi(r.component);if(!s){this.unsubscribeFromRouteData(e);return}for(let{templateName:a}of s.inputs)e.activatedComponentRef.setInput(a,o[a])});this.outletDataSubscriptions.set(e,i)}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})();function Ma(t,n,e){let r=It(t,n._root,e?e._root:void 0);return new bt(r,n)}function It(t,n,e){if(e&&t.shouldReuseRoute(n.value,e.value.snapshot)){let r=e.value;r._futureSnapshot=n.value;let i=Aa(t,n,e);return new k(r,i)}else{if(t.shouldAttach(n.value)){let o=t.retrieve(n.value);if(o!==null){let s=o.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>It(t,a)),s}}let r=Da(n.value),i=n.children.map(o=>It(t,o));return new k(r,i)}}function Aa(t,n,e){return n.children.map(r=>{for(let i of e.children)if(t.shouldReuseRoute(r.value,i.value.snapshot))return It(t,r,i);return It(t,r)})}function Da(t){return new K(new $(t.url),new $(t.params),new $(t.queryParams),new $(t.fragment),new $(t.data),t.outlet,t.component,t)}var Ve=class{redirectTo;navigationBehaviorOptions;constructor(n,e){this.redirectTo=n,this.navigationBehaviorOptions=e}},xo="ngNavigationCancelingError";function wr(t,n){let{redirectTo:e,navigationBehaviorOptions:r}=le(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,i=ko(!1,U.Redirect);return i.url=e,i.navigationBehaviorOptions=r,i}function ko(t,n){let e=new Error(`NavigationCancelingError: ${t||""}`);return e[xo]=!0,e.cancellationCode=n,e}function Oa(t){return Lo(t)&&le(t.url)}function Lo(t){return!!t&&t[xo]}var _a=(t,n,e,r)=>T(i=>(new Tn(n,i.targetRouterState,i.currentRouterState,e,r).activate(t),i)),Tn=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,e,r,i,o){this.routeReuseStrategy=n,this.futureState=e,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=o}activate(n){let e=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,r,n),dn(this.futureState.root),this.activateChildRoutes(e,r,n)}deactivateChildRoutes(n,e,r){let i=ke(e);n.children.forEach(o=>{let s=o.value.outlet;this.deactivateRoutes(o,i[s],r),delete i[s]}),Object.values(i).forEach(o=>{this.deactivateRouteAndItsChildren(o,r)})}deactivateRoutes(n,e,r){let i=n.value,o=e?e.value:null;if(i===o)if(i.component){let s=r.getContext(i.outlet);s&&this.deactivateChildRoutes(n,e,s.children)}else this.deactivateChildRoutes(n,e,r);else o&&this.deactivateRouteAndItsChildren(e,r)}deactivateRouteAndItsChildren(n,e){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,e):this.deactivateRouteAndOutlet(n,e)}detachAndStoreRouteSubtree(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,o=ke(n);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,o=ke(n);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,e,r){let i=ke(e);n.children.forEach(o=>{this.activateRoutes(o,i[o.value.outlet],r),this.forwardEvent(new mr(o.value.snapshot))}),n.children.length&&this.forwardEvent(new pr(n.value.snapshot))}activateRoutes(n,e,r){let i=n.value,o=e?e.value:null;if(dn(i),i===o)if(i.component){let s=r.getOrCreateContext(i.outlet);this.activateChildRoutes(n,e,s.children)}else this.activateChildRoutes(n,e,r);else if(i.component){let s=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),dn(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=i,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},Sr=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},je=class{component;route;constructor(n,e){this.component=n,this.route=e}};function Pa(t,n,e){let r=t._root,i=n?n._root:null;return yt(r,i,e,[r.value])}function Na(t){let n=t.routeConfig?t.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:t,guards:n}}function He(t,n){let e=Symbol(),r=n.get(t,e);return r===e?typeof t=="function"&&!Jn(t)?t:n.get(t):r}function yt(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=ke(n);return t.children.forEach(s=>{Ua(s,o[s.value.outlet],e,r.concat([s.value]),i),delete o[s.value.outlet]}),Object.entries(o).forEach(([s,a])=>wt(a,e.getContext(s),i)),i}function Ua(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=t.value,s=n?n.value:null,a=e?e.getContext(t.value.outlet):null;if(s&&o.routeConfig===s.routeConfig){let c=xa(s,o,o.routeConfig.runGuardsAndResolvers);c?i.canActivateChecks.push(new Sr(r)):(o.data=s.data,o._resolvedData=s._resolvedData),o.component?yt(t,n,a?a.children:null,r,i):yt(t,n,e,r,i),c&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new je(a.outlet.component,s))}else s&&wt(n,a,i),i.canActivateChecks.push(new Sr(r)),o.component?yt(t,null,a?a.children:null,r,i):yt(t,null,e,r,i);return i}function xa(t,n,e){if(typeof e=="function")return e(t,n);switch(e){case"pathParamsChange":return!Re(t.url,n.url);case"pathParamsOrQueryParamsChange":return!Re(t.url,n.url)||!X(t.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!wn(t,n)||!X(t.queryParams,n.queryParams);case"paramsChange":default:return!wn(t,n)}}function wt(t,n,e){let r=ke(t),i=t.value;Object.entries(r).forEach(([o,s])=>{i.component?n?wt(s,n.children.getContext(o),e):wt(s,null,e):wt(s,n,e)}),i.component?n&&n.outlet&&n.outlet.isActivated?e.canDeactivateChecks.push(new je(n.outlet.component,i)):e.canDeactivateChecks.push(new je(null,i)):e.canDeactivateChecks.push(new je(null,i))}function Pt(t){return typeof t=="function"}function ka(t){return typeof t=="boolean"}function La(t){return t&&Pt(t.canLoad)}function ja(t){return t&&Pt(t.canActivate)}function Fa(t){return t&&Pt(t.canActivateChild)}function $a(t){return t&&Pt(t.canDeactivate)}function za(t){return t&&Pt(t.canMatch)}function jo(t){return t instanceof Vn||t?.name==="EmptyError"}var er=Symbol("INITIAL_VALUE");function qe(){return x(t=>xt(t.map(n=>n.pipe(Ce(1),Wn(er)))).pipe(T(n=>{for(let e of n)if(e!==!0){if(e===er)return er;if(e===!1||Ba(e))return e}return!0}),G(n=>n!==er),Ce(1)))}function Ba(t){return le(t)||t instanceof Ve}function Va(t,n){return N(e=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:o,canDeactivateChecks:s}}=e;return s.length===0&&o.length===0?f(P(h({},e),{guardsResult:!0})):qa(s,r,i,t).pipe(N(a=>a&&ka(a)?Ha(r,o,t,n):f(a)),T(a=>P(h({},e),{guardsResult:a})))})}function qa(t,n,e,r){return D(t).pipe(N(i=>Ya(i.component,i.route,e,n,r)),se(i=>i!==!0,!0))}function Ha(t,n,e,r){return D(n).pipe(te(i=>qn(Wa(i.route.parent,r),Ga(i.route,r),Ja(t,i.path,e),Xa(t,i.route,e))),se(i=>i!==!0,!0))}function Ga(t,n){return t!==null&&n&&n(new gr(t)),f(!0)}function Wa(t,n){return t!==null&&n&&n(new fr(t)),f(!0)}function Xa(t,n,e){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return f(!0);let i=r.map(o=>Pr(()=>{let s=Ot(n)??e,a=He(o,s),c=ja(a)?a.canActivate(n,t):z(s,()=>a(n,t));return he(c).pipe(se())}));return f(i).pipe(qe())}function Ja(t,n,e){let r=n[n.length-1],o=n.slice(0,n.length-1).reverse().map(s=>Na(s)).filter(s=>s!==null).map(s=>Pr(()=>{let a=s.guards.map(c=>{let u=Ot(s.node)??e,l=He(c,u),g=Fa(l)?l.canActivateChild(r,t):z(u,()=>l(r,t));return he(g).pipe(se())});return f(a).pipe(qe())}));return f(o).pipe(qe())}function Ya(t,n,e,r,i){let o=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!o||o.length===0)return f(!0);let s=o.map(a=>{let c=Ot(n)??i,u=He(a,c),l=$a(u)?u.canDeactivate(t,n,e,r):z(c,()=>u(t,n,e,r));return he(l).pipe(se())});return f(s).pipe(qe())}function Za(t,n,e,r){let i=n.canLoad;if(i===void 0||i.length===0)return f(!0);let o=i.map(s=>{let a=He(s,t),c=La(a)?a.canLoad(n,e):z(t,()=>a(n,e));return he(c)});return f(o).pipe(qe(),Fo(r))}function Fo(t){return zn(O(n=>{if(typeof n!="boolean")throw wr(t,n)}),T(n=>n===!0))}function Ka(t,n,e,r){let i=n.canMatch;if(!i||i.length===0)return f(!0);let o=i.map(s=>{let a=He(s,t),c=za(a)?a.canMatch(n,e):z(t,()=>a(n,e));return he(c)});return f(o).pipe(qe(),Fo(r))}var Mt=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},At=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function xe(t){return Je(new Mt(t))}function Qa(t){return Je(new S(4e3,!1))}function ec(t){return Je(ko(!1,U.GuardRejected))}var En=class{urlSerializer;urlTree;constructor(n,e){this.urlSerializer=n,this.urlTree=e}lineralizeSegments(n,e){let r=[],i=e.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return f(r);if(i.numberOfChildren>1||!i.children[p])return Qa(`${n.redirectTo}`);i=i.children[p]}}applyRedirectCommands(n,e,r,i,o){if(typeof e!="string"){let a=e,{queryParams:c,fragment:u,routeConfig:l,url:g,outlet:b,params:v,data:M,title:w}=i,R=z(o,()=>a({params:v,data:M,queryParams:c,fragment:u,routeConfig:l,url:g,outlet:b,title:w}));if(R instanceof Y)throw new At(R);e=R}let s=this.applyRedirectCreateUrlTree(e,this.urlSerializer.parse(e),n,r);if(e[0]==="/")throw new At(s);return s}applyRedirectCreateUrlTree(n,e,r,i){let o=this.createSegmentGroup(n,e.root,r,i);return new Y(o,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(n,e){let r={};return Object.entries(n).forEach(([i,o])=>{if(typeof o=="string"&&o[0]===":"){let a=o.substring(1);r[i]=e[a]}else r[i]=o}),r}createSegmentGroup(n,e,r,i){let o=this.createSegments(n,e.segments,r,i),s={};return Object.entries(e.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,i)}),new E(o,s)}createSegments(n,e,r,i){return e.map(o=>o.path[0]===":"?this.findPosParam(n,o,i):this.findOrReturn(o,r))}findPosParam(n,e,r){let i=r[e.path.substring(1)];if(!i)throw new S(4001,!1);return i}findOrReturn(n,e){let r=0;for(let i of e){if(i.path===n.path)return e.splice(r),i;r++}return n}},bn={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function tc(t,n,e,r,i){let o=$o(t,n,e);return o.matched?(r=ba(n,r),Ka(r,n,e,i).pipe(T(s=>s===!0?o:h({},bn)))):f(o)}function $o(t,n,e){if(n.path==="**")return rc(e);if(n.path==="")return n.pathMatch==="full"&&(t.hasChildren()||e.length>0)?h({},bn):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let i=(n.matcher||go)(e,t,n);if(!i)return h({},bn);let o={};Object.entries(i.posParams??{}).forEach(([a,c])=>{o[a]=c.path});let s=i.consumed.length>0?h(h({},o),i.consumed[i.consumed.length-1].parameters):o;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function rc(t){return{matched:!0,parameters:t.length>0?vo(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function ho(t,n,e,r){return e.length>0&&oc(t,e,r)?{segmentGroup:new E(n,ic(r,new E(e,t.children))),slicedSegments:[]}:e.length===0&&sc(t,e,r)?{segmentGroup:new E(t.segments,nc(t,e,r,t.children)),slicedSegments:e}:{segmentGroup:new E(t.segments,t.children),slicedSegments:e}}function nc(t,n,e,r){let i={};for(let o of e)if(Er(t,n,o)&&!r[q(o)]){let s=new E([],{});i[q(o)]=s}return h(h({},r),i)}function ic(t,n){let e={};e[p]=n;for(let r of t)if(r.path===""&&q(r)!==p){let i=new E([],{});e[q(r)]=i}return e}function oc(t,n,e){return e.some(r=>Er(t,n,r)&&q(r)!==p)}function sc(t,n,e){return e.some(r=>Er(t,n,r))}function Er(t,n,e){return(t.hasChildren()||n.length>0)&&e.pathMatch==="full"?!1:e.path===""}function ac(t,n,e){return n.length===0&&!t.children[e]}var Cn=class{};function cc(t,n,e,r,i,o,s="emptyOnly"){return new In(t,n,e,r,i,s,o).recognize()}var uc=31,In=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,e,r,i,o,s,a){this.injector=n,this.configLoader=e,this.rootComponentType=r,this.config=i,this.urlTree=o,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new En(this.urlSerializer,this.urlTree)}noMatchError(n){return new S(4002,`'${n.segmentGroup}'`)}recognize(){let n=ho(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(T(({children:e,rootSnapshot:r})=>{let i=new k(r,e),o=new Ct("",i),s=Io(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,o.url=this.urlSerializer.serialize(s),{state:o,tree:s}}))}match(n){let e=new we([],Object.freeze({}),Object.freeze(h({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),p,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,p,e).pipe(T(r=>({children:r,rootSnapshot:e})),ge(r=>{if(r instanceof At)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Mt?this.noMatchError(r):r}))}processSegmentGroup(n,e,r,i,o){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,e,r,o):this.processSegment(n,e,r,r.segments,i,!0,o).pipe(T(s=>s instanceof k?[s]:[]))}processChildren(n,e,r,i){let o=[];for(let s of Object.keys(r.children))s==="primary"?o.unshift(s):o.push(s);return D(o).pipe(te(s=>{let a=r.children[s],c=Ca(e,s);return this.processSegmentGroup(n,c,a,s,i)}),Gn((s,a)=>(s.push(...a),s)),Nr(null),Hn(),N(s=>{if(s===null)return xe(r);let a=zo(s);return lc(a),f(a)}))}processSegment(n,e,r,i,o,s,a){return D(e).pipe(te(c=>this.processSegmentAgainstRoute(c._injector??n,e,c,r,i,o,s,a).pipe(ge(u=>{if(u instanceof Mt)return f(null);throw u}))),se(c=>!!c),ge(c=>{if(jo(c))return ac(r,i,o)?f(new Cn):xe(r);throw c}))}processSegmentAgainstRoute(n,e,r,i,o,s,a,c){return q(r)!==s&&(s===p||!Er(i,o,r))?xe(i):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,i,r,o,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,i,e,r,o,s,c):xe(i)}expandSegmentAgainstRouteUsingRedirect(n,e,r,i,o,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:g,remainingSegments:b}=$o(e,i,o);if(!c)return xe(e);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>uc&&(this.allowRedirects=!1));let v=new we(o,u,Object.freeze(h({},this.urlTree.queryParams)),this.urlTree.fragment,fo(i),q(i),i.component??i._loadedComponent??null,i,po(i)),M=Rr(v,a,this.paramsInheritanceStrategy);v.params=Object.freeze(M.params),v.data=Object.freeze(M.data);let w=this.applyRedirects.applyRedirectCommands(l,i.redirectTo,g,v,n);return this.applyRedirects.lineralizeSegments(i,w).pipe(N(R=>this.processSegment(n,r,e,R.concat(b),s,!1,a)))}matchSegmentAgainstRoute(n,e,r,i,o,s){let a=tc(e,r,i,n,this.urlSerializer);return r.path==="**"&&(e.children={}),a.pipe(x(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,i).pipe(x(({routes:u})=>{let l=r._loadedInjector??n,{parameters:g,consumedSegments:b,remainingSegments:v}=c,M=new we(b,g,Object.freeze(h({},this.urlTree.queryParams)),this.urlTree.fragment,fo(r),q(r),r.component??r._loadedComponent??null,r,po(r)),w=Rr(M,s,this.paramsInheritanceStrategy);M.params=Object.freeze(w.params),M.data=Object.freeze(w.data);let{segmentGroup:R,slicedSegments:F}=ho(e,b,v,u);if(F.length===0&&R.hasChildren())return this.processChildren(l,u,R,M).pipe(T(I=>new k(M,I)));if(u.length===0&&F.length===0)return f(new k(M,[]));let oe=q(r)===o;return this.processSegment(l,u,R,F,oe?p:o,!0,M).pipe(T(I=>new k(M,I instanceof k?[I]:[])))}))):xe(e)))}getChildConfig(n,e,r){return e.children?f({routes:e.children,injector:n}):e.loadChildren?e._loadedRoutes!==void 0?f({routes:e._loadedRoutes,injector:e._loadedInjector}):Za(n,e,r,this.urlSerializer).pipe(N(i=>i?this.configLoader.loadChildren(n,e).pipe(O(o=>{e._loadedRoutes=o.routes,e._loadedInjector=o.injector})):ec(e))):f({routes:[],injector:n})}};function lc(t){t.sort((n,e)=>n.value.outlet===p?-1:e.value.outlet===p?1:n.value.outlet.localeCompare(e.value.outlet))}function dc(t){let n=t.value.routeConfig;return n&&n.path===""}function zo(t){let n=[],e=new Set;for(let r of t){if(!dc(r)){n.push(r);continue}let i=n.find(o=>r.value.routeConfig===o.value.routeConfig);i!==void 0?(i.children.push(...r.children),e.add(i)):n.push(r)}for(let r of e){let i=zo(r.children);n.push(new k(r.value,i))}return n.filter(r=>!e.has(r))}function fo(t){return t.data||{}}function po(t){return t.resolve||{}}function hc(t,n,e,r,i,o){return N(s=>cc(t,n,e,r,s.extractedUrl,i,o).pipe(T(({state:a,tree:c})=>P(h({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function fc(t,n){return N(e=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=e;if(!i.length)return f(e);let o=new Set(i.map(c=>c.route)),s=new Set;for(let c of o)if(!s.has(c))for(let u of Bo(c))s.add(u);let a=0;return D(s).pipe(te(c=>o.has(c)?pc(c,r,t,n):(c.data=Rr(c,c.parent,t).resolve,f(void 0))),O(()=>a++),Ur(1),N(c=>a===s.size?f(e):ee))})}function Bo(t){let n=t.children.map(e=>Bo(e)).flat();return[t,...n]}function pc(t,n,e,r){let i=t.routeConfig,o=t._resolve;return i?.title!==void 0&&!No(i)&&(o[Dt]=i.title),gc(o,t,n,r).pipe(T(s=>(t._resolvedData=s,t.data=Rr(t,t.parent,e).resolve,null)))}function gc(t,n,e,r){let i=pn(t);if(i.length===0)return f({});let o={};return D(i).pipe(N(s=>mc(t[s],n,e,r).pipe(se(),O(a=>{if(a instanceof Ve)throw wr(new ue,a);o[s]=a}))),Ur(1),T(()=>o),ge(s=>jo(s)?ee:Je(s)))}function mc(t,n,e,r){let i=Ot(n)??r,o=He(t,i),s=o.resolve?o.resolve(n,e):z(i,()=>o(n,e));return he(s)}function hn(t){return x(n=>{let e=t(n);return e?D(e).pipe(T(()=>n)):f(n)})}var On=(()=>{class t{buildTitle(e){let r,i=e.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(o=>o.outlet===p);return r}getResolvedTitleForRoute(e){return e.data[Dt]}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:()=>d(Vo),providedIn:"root"})}return t})(),Vo=(()=>{class t extends On{title;constructor(e){super(),this.title=e}updateTitle(e){let r=this.buildTitle(e);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||t)(m(so))};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Ee=new C("",{providedIn:"root",factory:()=>({})}),_n=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275cmp=vi({type:t,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,i){r&1&&wi(0,"router-outlet")},dependencies:[An],encapsulation:2})}return t})();function Pn(t){let n=t.children&&t.children.map(Pn),e=n?P(h({},t),{children:n}):h({},t);return!e.component&&!e.loadComponent&&(n||e.loadChildren)&&e.outlet&&e.outlet!==p&&(e.component=_n),e}var be=new C(""),br=(()=>{class t{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=d(Ci);loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return f(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let r=he(e.loadComponent()).pipe(T(Ho),O(o=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=o}),me(()=>{this.componentLoaders.delete(e)})),i=new _r(r,()=>new Q).pipe(Or());return this.componentLoaders.set(e,i),i}loadChildren(e,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return f({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let o=qo(r,this.compiler,e,this.onLoadEndListener).pipe(me(()=>{this.childrenLoaders.delete(r)})),s=new _r(o,()=>new Q).pipe(Or());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function qo(t,n,e,r){return he(t.loadChildren()).pipe(T(Ho),N(i=>i instanceof mi||Array.isArray(i)?f(i):D(n.compileModuleAsync(i))),T(i=>{r&&r(t);let o,s,a=!1;return Array.isArray(i)?(s=i,a=!0):(o=i.create(e).injector,s=o.get(be,[],{optional:!0,self:!0}).flat()),{routes:s.map(Pn),injector:o}}))}function vc(t){return t&&typeof t=="object"&&"default"in t}function Ho(t){return vc(t)?t.default:t}var Cr=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:()=>d(yc),providedIn:"root"})}return t})(),yc=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,r){return e}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Nn=new C(""),Un=new C("");function Go(t,n,e){let r=t.get(Un),i=t.get(_);return t.get(B).runOutsideAngular(()=>{if(!i.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(u=>setTimeout(u));let o,s=new Promise(u=>{o=u}),a=i.startViewTransition(()=>(o(),Rc(t))),{onViewTransitionCreated:c}=r;return c&&z(t,()=>c({transition:a,from:n,to:e})),s})}function Rc(t){return new Promise(n=>{ii({read:()=>setTimeout(n)},{injector:t})})}var xn=new C(""),Nt=(()=>{class t{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new Q;transitionAbortSubject=new Q;configLoader=d(br);environmentInjector=d(ae);destroyRef=d(Kn);urlSerializer=d(fe);rootContexts=d(Te);location=d(Ae);inputBindingEnabled=d(_t,{optional:!0})!==null;titleStrategy=d(On);options=d(Ee,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=d(Cr);createViewTransition=d(Nn,{optional:!0});navigationErrorHandler=d(xn,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>f(void 0);rootComponentType=null;destroyed=!1;constructor(){let e=i=>this.events.next(new dr(i)),r=i=>this.events.next(new hr(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=e,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(e){let r=++this.navigationId;this.transitions?.next(P(h({},e),{extractedUrl:this.urlHandlingStrategy.extract(e.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(e){return this.transitions=new $(null),this.transitions.pipe(G(r=>r!==null),x(r=>{let i=!1,o=!1;return f(r).pipe(x(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",U.SupersededByNewNavigation),ee;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?P(h({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!a&&c!=="reload"){let u="";return this.events.next(new Z(s.id,this.urlSerializer.serialize(s.rawUrl),u,Fe.IgnoredSameUrlNavigation)),s.resolve(!1),ee}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return f(s).pipe(x(u=>(this.events.next(new de(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?ee:Promise.resolve(u))),hc(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),O(u=>{r.targetSnapshot=u.targetSnapshot,r.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=P(h({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let l=new Tt(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:u,extractedUrl:l,source:g,restoredState:b,extras:v}=s,M=new de(u,this.urlSerializer.serialize(l),g,b);this.events.next(M);let w=_o(this.rootComponentType).snapshot;return this.currentTransition=r=P(h({},s),{targetSnapshot:w,urlAfterRedirects:l,extras:P(h({},v),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,f(r)}else{let u="";return this.events.next(new Z(s.id,this.urlSerializer.serialize(s.extractedUrl),u,Fe.IgnoredByUrlHandlingStrategy)),s.resolve(!1),ee}}),O(s=>{let a=new ar(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),T(s=>(this.currentTransition=r=P(h({},s),{guards:Pa(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),Va(this.environmentInjector,s=>this.events.next(s)),O(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw wr(this.urlSerializer,s.guardsResult);let a=new cr(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),G(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",U.GuardRejected),!1)),hn(s=>{if(s.guards.canActivateChecks.length!==0)return f(s).pipe(O(a=>{let c=new ur(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),x(a=>{let c=!1;return f(a).pipe(fc(this.paramsInheritanceStrategy,this.environmentInjector),O({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",U.NoDataFromResolver)}}))}),O(a=>{let c=new lr(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),hn(s=>{let a=c=>{let u=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&u.push(this.configLoader.loadComponent(c.routeConfig).pipe(O(l=>{c.component=l}),T(()=>{})));for(let l of c.children)u.push(...a(l));return u};return xt(a(s.targetSnapshot.root)).pipe(Nr(null),Ce(1))}),hn(()=>this.afterPreactivation()),x(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?D(c).pipe(T(()=>r)):f(r)}),T(s=>{let a=Ma(e.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=P(h({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),O(()=>{this.events.next(new Et)}),_a(this.rootContexts,e.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),Ce(1),O({next:s=>{i=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new j(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{i=!0}}),Xn(this.transitionAbortSubject.pipe(O(s=>{throw s}))),me(()=>{!i&&!o&&this.cancelNavigationTransition(r,"",U.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),ge(s=>{if(this.destroyed)return r.resolve(!1),ee;if(o=!0,Lo(s))this.events.next(new J(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),Oa(s)?this.events.next(new Be(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new $e(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=z(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof Ve){let{message:u,cancellationCode:l}=wr(this.urlSerializer,c);this.events.next(new J(r.id,this.urlSerializer.serialize(r.extractedUrl),u,l)),this.events.next(new Be(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return ee}))}))}cancelNavigationTransition(e,r,i){let o=new J(e.id,this.urlSerializer.serialize(e.extractedUrl),r,i);this.events.next(o),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let e=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return e.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function wc(t){return t!==ir}var Wo=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:()=>d(Sc),providedIn:"root"})}return t})(),Tr=class{shouldDetach(n){return!1}store(n,e){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,e){return n.routeConfig===e.routeConfig}},Sc=(()=>{class t extends Tr{static \u0275fac=(()=>{let e;return function(i){return(e||(e=kr(t)))(i||t)}})();static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Xo=(()=>{class t{urlSerializer=d(fe);options=d(Ee,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=d(Ae);urlHandlingStrategy=d(Cr);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Y;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:e,initialUrl:r,targetBrowserUrl:i}){let o=e!==void 0?this.urlHandlingStrategy.merge(e,r):r,s=i??o;return s instanceof Y?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:e,finalUrl:r,initialUrl:i}){r&&e?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,i),this.routerState=e):this.rawUrlTree=i}routerState=_o(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:e}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e??this.rawUrlTree)}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:()=>d(Tc),providedIn:"root"})}return t})(),Tc=(()=>{class t extends Xo{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{e(r.url,r.state,"popstate")})})}handleRouterEvent(e,r){e instanceof de?this.updateStateMemento():e instanceof Z?this.commitTransition(r):e instanceof Tt?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof Et?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof J&&(e.code===U.GuardRejected||e.code===U.NoDataFromResolver)?this.restoreHistory(r):e instanceof $e?this.restoreHistory(r,!0):e instanceof j&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,{extras:r,id:i}){let{replaceUrl:o,state:s}=r;if(this.location.isCurrentPathEqualTo(e)||o){let a=this.browserPageId,c=h(h({},s),this.generateNgRouterState(i,a));this.location.replaceState(e,"",c)}else{let a=h(h({},s),this.generateNgRouterState(i,this.browserPageId+1));this.location.go(e,"",a)}}restoreHistory(e,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,o=this.currentPageId-i;o!==0?this.location.historyGo(o):this.getCurrentUrlTree()===e.finalUrl&&o===0&&(this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,r){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:r}:{navigationId:e}}static \u0275fac=(()=>{let e;return function(i){return(e||(e=kr(t)))(i||t)}})();static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Ir(t,n){t.events.pipe(G(e=>e instanceof j||e instanceof J||e instanceof $e||e instanceof Z),T(e=>e instanceof j||e instanceof Z?0:(e instanceof J?e.code===U.Redirect||e.code===U.SupersededByNewNavigation:!1)?2:1),G(e=>e!==2),Ce(1)).subscribe(()=>{n()})}var Ec={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},bc={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},H=(()=>{class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=d(Br);stateManager=d(Xo);options=d(Ee,{optional:!0})||{};pendingTasks=d(tt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=d(Nt);urlSerializer=d(fe);location=d(Ae);urlHandlingStrategy=d(Cr);_events=new Q;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=d(Wo);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=d(be,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!d(_t,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}eventsSubscription=new $n;subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(i!==null&&o!==null){if(this.stateManager.handleRouterEvent(r,o),r instanceof J&&r.code!==U.Redirect&&r.code!==U.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof j)this.navigated=!0;else if(r instanceof Be){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),c=h({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||wc(i.source)},s);this.scheduleNavigation(a,ir,null,c,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}Ic(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),ir,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,r,i)=>{this.navigateToSyncWithBrowser(e,i,r)})}navigateToSyncWithBrowser(e,r,i){let o={replaceUrl:!0},s=i?.navigationId?i:null;if(i){let c=h({},i);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(o.state=c)}let a=this.parseUrl(e);this.scheduleNavigation(a,r,s,o)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(Pn),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,r={}){let{relativeTo:i,queryParams:o,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=h(h({},this.currentUrlTree.queryParams),o);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=o||null}l!==null&&(l=this.removeEmptyProps(l));let g;try{let b=i?i.snapshot:this.routerState.snapshot.root;g=Mo(b)}catch{(typeof e[0]!="string"||e[0][0]!=="/")&&(e=[]),g=this.currentUrlTree.root}return Ao(g,e,l,u??null)}navigateByUrl(e,r={skipLocationChange:!1}){let i=le(e)?e:this.parseUrl(e),o=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(o,ir,null,r)}navigate(e,r={skipLocationChange:!1}){return Cc(e),this.navigateByUrl(this.createUrlTree(e,r),r)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,r){let i;if(r===!0?i=h({},Ec):r===!1?i=h({},bc):i=r,le(e))return ao(this.currentUrlTree,e,i);let o=this.parseUrl(e);return ao(this.currentUrlTree,o,i)}removeEmptyProps(e){return Object.entries(e).reduce((r,[i,o])=>(o!=null&&(r[i]=o),r),{})}scheduleNavigation(e,r,i,o,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((g,b)=>{a=g,c=b});let l=this.pendingTasks.add();return Ir(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:o,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(g=>Promise.reject(g))}static \u0275fac=function(r){return new(r||t)};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Cc(t){for(let n=0;n<t.length;n++)if(t[n]==null)throw new S(4008,!1)}function Ic(t){return!(t instanceof Et)&&!(t instanceof Be)}var Mr=(()=>{class t{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new Q;constructor(e,r,i,o,s,a){this.router=e,this.route=r,this.tabIndexAttribute=i,this.renderer=o,this.el=s,this.locationStrategy=a;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=e.events.subscribe(u=>{u instanceof j&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(e){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(e){e==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(le(e)?this.routerLinkInput=e:this.routerLinkInput=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0"))}onClick(e,r,i,o,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(e!==0||r||i||o||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let e=this.urlTree;this.href=e!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(e)):null;let r=this.href===null?null:hi(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(e,r){let i=this.renderer,o=this.el.nativeElement;r!==null?i.setAttribute(o,e,r):i.removeAttribute(o,e)}get urlTree(){return this.routerLinkInput===null?null:le(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||t)(V(H),V(K),Zn("tabindex"),V(zr),V(jr),V(ct))};static \u0275dir=st({type:t,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,i){r&1&&Si("click",function(s){return i.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Ri("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",jt],skipLocationChange:[2,"skipLocationChange","skipLocationChange",jt],replaceUrl:[2,"replaceUrl","replaceUrl",jt],routerLink:"routerLink"},features:[Qe]})}return t})(),Mc=(()=>{class t{router;element;renderer;cdr;link;links;classes=[];routerEventsSubscription;linkInputChangesSubscription;_isActive=!1;get isActive(){return this._isActive}routerLinkActiveOptions={exact:!1};ariaCurrentWhenActive;isActiveChange=new ve;constructor(e,r,i,o,s){this.router=e,this.element=r,this.renderer=i,this.cdr=o,this.link=s,this.routerEventsSubscription=e.events.subscribe(a=>{a instanceof j&&this.update()})}ngAfterContentInit(){f(this.links.changes,f(null)).pipe(Ye()).subscribe(e=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let e=[...this.links.toArray(),this.link].filter(r=>!!r).map(r=>r.onChanges);this.linkInputChangesSubscription=D(e).pipe(Ye()).subscribe(r=>{this._isActive!==this.isLinkActive(this.router)(r)&&this.update()})}set routerLinkActive(e){let r=Array.isArray(e)?e:e.split(" ");this.classes=r.filter(i=>!!i)}ngOnChanges(e){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let e=this.hasActiveLinks();this.classes.forEach(r=>{e?this.renderer.addClass(this.element.nativeElement,r):this.renderer.removeClass(this.element.nativeElement,r)}),e&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==e&&(this._isActive=e,this.cdr.markForCheck(),this.isActiveChange.emit(e))})}isLinkActive(e){let r=Ac(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return i=>{let o=i.urlTree;return o?e.isActive(o,r):!1}}hasActiveLinks(){let e=this.isLinkActive(this.router);return this.link&&e(this.link)||this.links.some(e)}static \u0275fac=function(r){return new(r||t)(V(H),V(jr),V(zr),V(Lt),V(Mr,8))};static \u0275dir=st({type:t,selectors:[["","routerLinkActive",""]],contentQueries:function(r,i,o){if(r&1&&Ti(o,Mr,5),r&2){let s;Ei(s=bi())&&(i.links=s)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],features:[Qe]})}return t})();function Ac(t){return!!t.paths}var Ut=class{};var Jo=(()=>{class t{router;injector;preloadingStrategy;loader;subscription;constructor(e,r,i,o){this.router=e,this.injector=r,this.preloadingStrategy=i,this.loader=o}setUpPreloading(){this.subscription=this.router.events.pipe(G(e=>e instanceof j),te(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(e,r){let i=[];for(let o of r){o.providers&&!o._injector&&(o._injector=kt(o.providers,e,`Route: ${o.path}`));let s=o._injector??e,a=o._loadedInjector??s;(o.loadChildren&&!o._loadedRoutes&&o.canLoad===void 0||o.loadComponent&&!o._loadedComponent)&&i.push(this.preloadConfig(s,o)),(o.children||o._loadedRoutes)&&i.push(this.processRoutes(a,o.children??o._loadedRoutes))}return D(i).pipe(Ye())}preloadConfig(e,r){return this.preloadingStrategy.preload(r,()=>{let i;r.loadChildren&&r.canLoad===void 0?i=this.loader.loadChildren(e,r):i=f(null);let o=i.pipe(N(s=>s===null?f(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??e,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return D([o,s]).pipe(Ye())}else return o})}static \u0275fac=function(r){return new(r||t)(m(H),m(ae),m(Ut),m(br))};static \u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Ln=new C(""),Yo=(()=>{class t{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(e,r,i,o,s={}){this.urlSerializer=e,this.transitions=r,this.viewportScroller=i,this.zone=o,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof de?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=e.navigationTrigger,this.restoredId=e.restoredState?e.restoredState.navigationId:0):e instanceof j?(this.lastId=e.id,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.urlAfterRedirects).fragment)):e instanceof Z&&e.code===Fe.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof ze&&(e.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(e.position):e.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(e.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(e,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new ze(e,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){pi()};static \u0275prov=y({token:t,factory:t.\u0275fac})}return t})();function Dc(t,...n){return Ke([{provide:be,multi:!0,useValue:t},[],{provide:K,useFactory:Zo,deps:[H]},{provide:qr,multi:!0,useFactory:Ko},n.map(e=>e.\u0275providers)])}function Zo(t){return t.routerState.root}function Ge(t,n){return{\u0275kind:t,\u0275providers:n}}function Oc(t={}){return Ge(4,[{provide:Ln,useFactory:()=>{let e=d(Wr),r=d(B),i=d(Nt),o=d(fe);return new Yo(o,i,e,r,t)}}])}function Ko(){let t=d(et);return n=>{let e=t.get(Hr);if(n!==e.components[0])return;let r=t.get(H),i=t.get(Qo);t.get(jn)===1&&r.initialNavigation(),t.get(rs,null,xr.Optional)?.setUpPreloading(),t.get(Ln,null,xr.Optional)?.init(),r.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var Qo=new C("",{factory:()=>new Q}),jn=new C("",{providedIn:"root",factory:()=>1});function es(){let t=[{provide:jn,useValue:0},Vr(()=>{let n=d(et);return n.get(Oi,Promise.resolve()).then(()=>new Promise(r=>{let i=n.get(H),o=n.get(Qo);Ir(i,()=>{r(!0)}),n.get(Nt).afterPreactivation=()=>(r(!0),o.closed?f(void 0):o),i.initialNavigation()}))})];return Ge(2,t)}function ts(){let t=[Vr(()=>{d(H).setUpLocationChangeListener()}),{provide:jn,useValue:2}];return Ge(3,t)}var rs=new C("");function ns(t){return Ge(0,[{provide:rs,useExisting:Jo},{provide:Ut,useExisting:t}])}function is(){return Ge(8,[Dn,{provide:_t,useExisting:Dn}])}function os(t){ni("NgRouterViewTransitions");let n=[{provide:Nn,useValue:Go},{provide:Un,useValue:h({skipNextTransition:!!t?.skipInitialTransition},t)}];return Ge(9,n)}var ss=[Ae,{provide:fe,useClass:ue},H,Te,{provide:K,useFactory:Zo,deps:[H]},br,[]],_c=(()=>{class t{constructor(){}static forRoot(e,r){return{ngModule:t,providers:[ss,[],{provide:be,multi:!0,useValue:e},[],r?.errorHandler?{provide:xn,useValue:r.errorHandler}:[],{provide:Ee,useValue:r||{}},r?.useHash?Nc():Uc(),Pc(),r?.preloadingStrategy?ns(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?xc(r):[],r?.bindToComponentInputs?is().\u0275providers:[],r?.enableViewTransitions?os().\u0275providers:[],kc()]}}static forChild(e){return{ngModule:t,providers:[{provide:be,multi:!0,useValue:e}]}}static \u0275fac=function(r){return new(r||t)};static \u0275mod=ot({type:t});static \u0275inj=Ze({})}return t})();function Pc(){return{provide:Ln,useFactory:()=>{let t=d(Wr),n=d(B),e=d(Ee),r=d(Nt),i=d(fe);return e.scrollOffset&&t.setOffset(e.scrollOffset),new Yo(i,r,t,n,e)}}}function Nc(){return{provide:ct,useClass:Pi}}function Uc(){return{provide:ct,useClass:_i}}function xc(t){return[t.initialNavigation==="disabled"?ts().\u0275providers:[],t.initialNavigation==="enabledBlocking"?es().\u0275providers:[]]}var kn=new C("");function kc(){return[{provide:kn,useFactory:Ko},{provide:qr,multi:!0,useExisting:kn}]}export{en as a,ws as b,ne as c,rn as d,Xt as e,re as f,Ki as g,Hs as h,Gs as i,Ws as j,Xs as k,Js as l,so as m,Zs as n,K as o,An as p,On as q,H as r,Mr as s,Mc as t,Dc as u,Oc as v,es as w,_c as x};
