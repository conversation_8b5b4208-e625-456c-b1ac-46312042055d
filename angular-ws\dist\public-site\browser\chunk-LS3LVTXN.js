import{a as Y,q as fe,t as he,u as me}from"./chunk-D6WDCTDG.js";import{Jb as nt,Kb as st,R as E,S as Nt,Ta as ee,U as zt,Va as ie,Wa as ne,X as g,Y as qt,Zb as K,ab as $,bb as Dt,ca as Zt,cb as j,eb as G,f as V,fa as Qt,ga as I,ia as Jt,jb as se,mb as re,na as Xt,qa as te,qc as pe,ra as H,rb as oe,uc as ue,va as vt,wb as ae,wc as de,xb as le,yb as ce,yc as rt}from"./chunk-BL4EGCPV.js";import{a as S}from"./chunk-4CLCTAJ7.js";function oi(t,n){return t?t.classList?t.classList.contains(n):new RegExp("(^| )"+n+"( |$)","gi").test(t.className):!1}function xt(t,n){if(t&&n){let e=i=>{oi(t,i)||(t.classList?t.classList.add(i):t.className+=" "+i)};[n].flat().filter(Boolean).forEach(i=>i.split(" ").forEach(e))}}function ai(){return window.innerWidth-document.documentElement.offsetWidth}function z(t){for(let n of document?.styleSheets)try{for(let e of n?.cssRules)for(let i of e?.style)if(t.test(i))return{name:i,value:e.style.getPropertyValue(i).trim()}}catch{}return null}function $i(t="p-overflow-hidden"){let n=z(/-scrollbar-width$/);n?.name&&document.body.style.setProperty(n.name,ai()+"px"),xt(document.body,t)}function q(t,n){if(t&&n){let e=i=>{t.classList?t.classList.remove(i):t.className=t.className.replace(new RegExp("(^|\\b)"+i.split(" ").join("|")+"(\\b|$)","gi")," ")};[n].flat().filter(Boolean).forEach(i=>i.split(" ").forEach(e))}}function Wi(t="p-overflow-hidden"){let n=z(/-scrollbar-width$/);n?.name&&document.body.style.removeProperty(n.name),q(document.body,t)}function ge(t){let n={width:0,height:0};return t&&(t.style.visibility="hidden",t.style.display="block",n.width=t.offsetWidth,n.height=t.offsetHeight,t.style.display="none",t.style.visibility="visible"),n}function ye(){let t=window,n=document,e=n.documentElement,i=n.getElementsByTagName("body")[0],s=t.innerWidth||e.clientWidth||i.clientWidth,r=t.innerHeight||e.clientHeight||i.clientHeight;return{width:s,height:r}}function li(){let t=document.documentElement;return(window.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}function ci(){let t=document.documentElement;return(window.pageYOffset||t.scrollTop)-(t.clientTop||0)}function Ui(t,n,e=!0){var i,s,r,o;if(t){let a=t.offsetParent?{width:t.offsetWidth,height:t.offsetHeight}:ge(t),c=a.height,l=a.width,p=n.offsetHeight,d=n.offsetWidth,u=n.getBoundingClientRect(),f=ci(),y=li(),b=ye(),v,A,L="top";u.top+p+c>b.height?(v=u.top+f-c,L="bottom",v<0&&(v=f)):v=p+u.top+f,u.left+l>b.width?A=Math.max(0,u.left+y+d-l):A=u.left+y,t.style.top=v+"px",t.style.left=A+"px",t.style.transformOrigin=L,e&&(t.style.marginTop=L==="bottom"?`calc(${(s=(i=z(/-anchor-gutter$/))==null?void 0:i.value)!=null?s:"2px"} * -1)`:(o=(r=z(/-anchor-gutter$/))==null?void 0:r.value)!=null?o:"")}}function Bi(t,n){t&&(typeof n=="string"?t.style.cssText=n:Object.entries(n||{}).forEach(([e,i])=>t.style[e]=i))}function Se(t,n){if(t instanceof HTMLElement){let e=t.offsetWidth;if(n){let i=getComputedStyle(t);e+=parseFloat(i.marginLeft)+parseFloat(i.marginRight)}return e}return 0}function Vi(t,n,e=!0){var i,s,r,o;if(t){let a=t.offsetParent?{width:t.offsetWidth,height:t.offsetHeight}:ge(t),c=n.offsetHeight,l=n.getBoundingClientRect(),p=ye(),d,u,f="top";l.top+c+a.height>p.height?(d=-1*a.height,f="bottom",l.top+d<0&&(d=-1*l.top)):d=c,a.width>p.width?u=l.left*-1:l.left+a.width>p.width?u=(l.left+a.width-p.width)*-1:u=0,t.style.top=d+"px",t.style.left=u+"px",t.style.transformOrigin=f,e&&(t.style.marginTop=f==="bottom"?`calc(${(s=(i=z(/-anchor-gutter$/))==null?void 0:i.value)!=null?s:"2px"} * -1)`:(o=(r=z(/-anchor-gutter$/))==null?void 0:r.value)!=null?o:"")}}function Z(t){return typeof HTMLElement=="object"?t instanceof HTMLElement:t&&typeof t=="object"&&t!==null&&t.nodeType===1&&typeof t.nodeName=="string"}function Pt(t){let n=t;return t&&typeof t=="object"&&(t.hasOwnProperty("current")?n=t.current:t.hasOwnProperty("el")&&(t.el.hasOwnProperty("nativeElement")?n=t.el.nativeElement:n=t.el)),Z(n)?n:void 0}function ji(t,n){let e=Pt(t);if(e)e.appendChild(n);else throw new Error("Cannot append "+n+" to "+t)}function Tt(t,n={}){if(Z(t)){let e=(i,s)=>{var r,o;let a=(r=t?.$attrs)!=null&&r[i]?[(o=t?.$attrs)==null?void 0:o[i]]:[];return[s].flat().reduce((c,l)=>{if(l!=null){let p=typeof l;if(p==="string"||p==="number")c.push(l);else if(p==="object"){let d=Array.isArray(l)?e(i,l):Object.entries(l).map(([u,f])=>i==="style"&&(f||f===0)?`${u.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}:${f}`:f?u:void 0);c=d.length?c.concat(d.filter(u=>!!u)):c}}return c},a)};Object.entries(n).forEach(([i,s])=>{if(s!=null){let r=i.match(/^on(.+)/);r?t.addEventListener(r[1].toLowerCase(),s):i==="p-bind"||i==="pBind"?Tt(t,s):(s=i==="class"?[...new Set(e("class",s))].join(" ").trim():i==="style"?e("style",s).join(";").trim():s,(t.$attrs=t.$attrs||{})&&(t.$attrs[i]=s),t.setAttribute(i,s))}})}}function Gi(t,n={},...e){if(t){let i=document.createElement(t);return Tt(i,n),i.append(...e),i}}function Ki(t,n){if(t){t.style.opacity="0";let e=+new Date,i="0",s=function(){i=`${+t.style.opacity+(new Date().getTime()-e)/n}`,t.style.opacity=i,e=+new Date,+i<1&&(window.requestAnimationFrame&&requestAnimationFrame(s)||setTimeout(s,16))};s()}}function pi(t,n){return Z(t)?Array.from(t.querySelectorAll(n)):[]}function Yi(t,n){return Z(t)?t.matches(n)?t:t.querySelector(n):null}function zi(t,n){t&&document.activeElement!==t&&t.focus(n)}function qi(t,n){if(Z(t)){let e=t.getAttribute(n);return isNaN(e)?e==="true"||e==="false"?e==="true":e:+e}}function be(t,n=""){let e=pi(t,`button:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${n},
            [href][clientHeight][clientWidth]:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${n},
            input:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${n},
            select:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${n},
            textarea:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${n},
            [tabIndex]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${n},
            [contenteditable]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${n}`),i=[];for(let s of e)getComputedStyle(s).display!="none"&&getComputedStyle(s).visibility!="hidden"&&i.push(s);return i}function Zi(t,n){let e=be(t,n);return e.length>0?e[0]:null}function Mt(t){if(t){let n=t.offsetHeight,e=getComputedStyle(t);return n-=parseFloat(e.paddingTop)+parseFloat(e.paddingBottom)+parseFloat(e.borderTopWidth)+parseFloat(e.borderBottomWidth),n}return 0}function Ee(t){if(t){let n=t.parentNode;return n&&n instanceof ShadowRoot&&n.host&&(n=n.host),n}return null}function Qi(t){var n;if(t){let e=(n=Ee(t))==null?void 0:n.childNodes,i=0;if(e)for(let s=0;s<e.length;s++){if(e[s]===t)return i;e[s].nodeType===1&&i++}}return-1}function Ji(t,n){let e=be(t,n);return e.length>0?e[e.length-1]:null}function _e(t){if(t){let n=t.getBoundingClientRect();return{top:n.top+(window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0),left:n.left+(window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0)}}return{top:"auto",left:"auto"}}function kt(t,n){if(t){let e=t.offsetHeight;if(n){let i=getComputedStyle(t);e+=parseFloat(i.marginTop)+parseFloat(i.marginBottom)}return e}return 0}function Xi(){if(window.getSelection)return window.getSelection().toString();if(document.getSelection)return document.getSelection().toString()}function ui(t){return!!(t!==null&&typeof t<"u"&&t.nodeName&&Ee(t))}function tn(t,n){var e;if(t)switch(t){case"document":return document;case"window":return window;case"body":return document.body;case"@next":return n?.nextElementSibling;case"@prev":return n?.previousElementSibling;case"@parent":return n?.parentElement;case"@grandparent":return(e=n?.parentElement)==null?void 0:e.parentElement;default:if(typeof t=="string")return document.querySelector(t);let s=Pt((r=>!!(r&&r.constructor&&r.call&&r.apply))(t)?t():t);return s?.nodeType===9||ui(s)?s:void 0}}function Ft(t){if(t){let n=t.offsetWidth,e=getComputedStyle(t);return n-=parseFloat(e.paddingLeft)+parseFloat(e.paddingRight)+parseFloat(e.borderLeftWidth)+parseFloat(e.borderRightWidth),n}return 0}function en(t){return!!(t&&t.offsetParent!=null)}function nn(){return"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0}function ve(t){var n;t&&("remove"in Element.prototype?t.remove():(n=t.parentNode)==null||n.removeChild(t))}function sn(t,n){let e=Pt(t);if(e)e.removeChild(n);else throw new Error("Cannot remove "+n+" from "+t)}function rn(t,n){let e=getComputedStyle(t).getPropertyValue("borderTopWidth"),i=e?parseFloat(e):0,s=getComputedStyle(t).getPropertyValue("paddingTop"),r=s?parseFloat(s):0,o=t.getBoundingClientRect(),c=n.getBoundingClientRect().top+document.body.scrollTop-(o.top+document.body.scrollTop)-i-r,l=t.scrollTop,p=t.clientHeight,d=kt(n);c<0?t.scrollTop=l+c:c+d>p&&(t.scrollTop=l+c-p+d)}function Te(t,n="",e){Z(t)&&e!==null&&e!==void 0&&t.setAttribute(n,e)}function Ce(){let t=new Map;return{on(n,e){let i=t.get(n);return i?i.push(e):i=[e],t.set(n,i),this},off(n,e){let i=t.get(n);return i&&i.splice(i.indexOf(e)>>>0,1),this},emit(n,e){let i=t.get(n);i&&i.slice().map(s=>{s(e)})},clear(){t.clear()}}}var di=Object.defineProperty,Oe=Object.getOwnPropertySymbols,fi=Object.prototype.hasOwnProperty,hi=Object.prototype.propertyIsEnumerable,Ae=(t,n,e)=>n in t?di(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e,mi=(t,n)=>{for(var e in n||(n={}))fi.call(n,e)&&Ae(t,e,n[e]);if(Oe)for(var e of Oe(n))hi.call(n,e)&&Ae(t,e,n[e]);return t};function W(t){return t==null||t===""||Array.isArray(t)&&t.length===0||!(t instanceof Date)&&typeof t=="object"&&Object.keys(t).length===0}function Ht(t,n,e=new WeakSet){if(t===n)return!0;if(!t||!n||typeof t!="object"||typeof n!="object"||e.has(t)||e.has(n))return!1;e.add(t).add(n);let i=Array.isArray(t),s=Array.isArray(n),r,o,a;if(i&&s){if(o=t.length,o!=n.length)return!1;for(r=o;r--!==0;)if(!Ht(t[r],n[r],e))return!1;return!0}if(i!=s)return!1;let c=t instanceof Date,l=n instanceof Date;if(c!=l)return!1;if(c&&l)return t.getTime()==n.getTime();let p=t instanceof RegExp,d=n instanceof RegExp;if(p!=d)return!1;if(p&&d)return t.toString()==n.toString();let u=Object.keys(t);if(o=u.length,o!==Object.keys(n).length)return!1;for(r=o;r--!==0;)if(!Object.prototype.hasOwnProperty.call(n,u[r]))return!1;for(r=o;r--!==0;)if(a=u[r],!Ht(t[a],n[a],e))return!1;return!0}function gi(t,n){return Ht(t,n)}function Re(t){return!!(t&&t.constructor&&t.call&&t.apply)}function m(t){return!W(t)}function Ct(t,n){if(!t||!n)return null;try{let e=t[n];if(m(e))return e}catch{}if(Object.keys(t).length){if(Re(n))return n(t);if(n.indexOf(".")===-1)return t[n];{let e=n.split("."),i=t;for(let s=0,r=e.length;s<r;++s){if(i==null)return null;i=i[e[s]]}return i}}return null}function $t(t,n,e){return e?Ct(t,e)===Ct(n,e):gi(t,n)}function cn(t,n){if(t!=null&&n&&n.length){for(let e of n)if($t(t,e))return!0}return!1}function pn(t,n){let e=-1;if(m(t))try{e=t.findLastIndex(n)}catch{e=t.lastIndexOf([...t].reverse().find(n))}return e}function N(t,n=!0){return t instanceof Object&&t.constructor===Object&&(n||Object.keys(t).length!==0)}function R(t,...n){return Re(t)?t(...n):t}function k(t,n=!0){return typeof t=="string"&&(n||t!=="")}function Le(t){return k(t)?t.replace(/(-|_)/g,"").toLowerCase():t}function Ot(t,n="",e={}){let i=Le(n).split("."),s=i.shift();return s?N(t)?Ot(R(t[Object.keys(t).find(r=>Le(r)===s)||""],e),i.join("."),e):void 0:R(t,e)}function At(t,n=!0){return Array.isArray(t)&&(n||t.length!==0)}function un(t){return t instanceof Date&&t.constructor===Date}function we(t){return m(t)&&!isNaN(t)}function dn(t=""){return m(t)&&t.length===1&&!!t.match(/\S| /)}function T(t,n){if(n){let e=n.test(t);return n.lastIndex=0,e}return!1}function ot(...t){let n=(e={},i={})=>{let s=mi({},e);return Object.keys(i).forEach(r=>{N(i[r])&&r in e&&N(e[r])?s[r]=n(e[r],i[r]):s[r]=i[r]}),s};return t.reduce((e,i,s)=>s===0?i:n(e,i),{})}function U(t){return t&&t.replace(/\/\*(?:(?!\*\/)[\s\S])*\*\/|[\r\n\t]+/g,"").replace(/ {2,}/g," ").replace(/ ([{:}]) /g,"$1").replace(/([;,]) /g,"$1").replace(/ !/g,"!").replace(/: /g,":")}function C(t){if(t&&/[\xC0-\xFF\u0100-\u017E]/.test(t)){let e={A:/[\xC0-\xC5\u0100\u0102\u0104]/g,AE:/[\xC6]/g,C:/[\xC7\u0106\u0108\u010A\u010C]/g,D:/[\xD0\u010E\u0110]/g,E:/[\xC8-\xCB\u0112\u0114\u0116\u0118\u011A]/g,G:/[\u011C\u011E\u0120\u0122]/g,H:/[\u0124\u0126]/g,I:/[\xCC-\xCF\u0128\u012A\u012C\u012E\u0130]/g,IJ:/[\u0132]/g,J:/[\u0134]/g,K:/[\u0136]/g,L:/[\u0139\u013B\u013D\u013F\u0141]/g,N:/[\xD1\u0143\u0145\u0147\u014A]/g,O:/[\xD2-\xD6\xD8\u014C\u014E\u0150]/g,OE:/[\u0152]/g,R:/[\u0154\u0156\u0158]/g,S:/[\u015A\u015C\u015E\u0160]/g,T:/[\u0162\u0164\u0166]/g,U:/[\xD9-\xDC\u0168\u016A\u016C\u016E\u0170\u0172]/g,W:/[\u0174]/g,Y:/[\xDD\u0176\u0178]/g,Z:/[\u0179\u017B\u017D]/g,a:/[\xE0-\xE5\u0101\u0103\u0105]/g,ae:/[\xE6]/g,c:/[\xE7\u0107\u0109\u010B\u010D]/g,d:/[\u010F\u0111]/g,e:/[\xE8-\xEB\u0113\u0115\u0117\u0119\u011B]/g,g:/[\u011D\u011F\u0121\u0123]/g,i:/[\xEC-\xEF\u0129\u012B\u012D\u012F\u0131]/g,ij:/[\u0133]/g,j:/[\u0135]/g,k:/[\u0137,\u0138]/g,l:/[\u013A\u013C\u013E\u0140\u0142]/g,n:/[\xF1\u0144\u0146\u0148\u014B]/g,p:/[\xFE]/g,o:/[\xF2-\xF6\xF8\u014D\u014F\u0151]/g,oe:/[\u0153]/g,r:/[\u0155\u0157\u0159]/g,s:/[\u015B\u015D\u015F\u0161]/g,t:/[\u0163\u0165\u0167]/g,u:/[\xF9-\xFC\u0169\u016B\u016D\u016F\u0171\u0173]/g,w:/[\u0175]/g,y:/[\xFD\xFF\u0177]/g,z:/[\u017A\u017C\u017E]/g};for(let i in e)t=t.replace(e[i],i)}return t}function Lt(t){return k(t)?t.replace(/(_)/g,"-").replace(/[A-Z]/g,(n,e)=>e===0?n:"-"+n.toLowerCase()).toLowerCase():t}function Wt(t){return k(t)?t.replace(/[A-Z]/g,(n,e)=>e===0?n:"."+n.toLowerCase()).toLowerCase():t}var Rt={};function Ie(t="pui_id_"){return Rt.hasOwnProperty(t)||(Rt[t]=0),Rt[t]++,`${t}${Rt[t]}`}function yi(){let t=[],n=(o,a,c=999)=>{let l=s(o,a,c),p=l.value+(l.key===o?0:c)+1;return t.push({key:o,value:p}),p},e=o=>{t=t.filter(a=>a.value!==o)},i=(o,a)=>s(o,a).value,s=(o,a,c=0)=>[...t].reverse().find(l=>a?!0:l.key===o)||{key:o,value:c},r=o=>o&&parseInt(o.style.zIndex,10)||0;return{get:r,set:(o,a,c)=>{a&&(a.style.zIndex=String(n(o,!0,c)))},clear:o=>{o&&(e(r(o)),o.style.zIndex="")},getCurrent:o=>i(o,!0)}}var mn=yi();var Ne=["*"];var _=(()=>{class t{static STARTS_WITH="startsWith";static CONTAINS="contains";static NOT_CONTAINS="notContains";static ENDS_WITH="endsWith";static EQUALS="equals";static NOT_EQUALS="notEquals";static IN="in";static LESS_THAN="lt";static LESS_THAN_OR_EQUAL_TO="lte";static GREATER_THAN="gt";static GREATER_THAN_OR_EQUAL_TO="gte";static BETWEEN="between";static IS="is";static IS_NOT="isNot";static BEFORE="before";static AFTER="after";static DATE_IS="dateIs";static DATE_IS_NOT="dateIsNot";static DATE_BEFORE="dateBefore";static DATE_AFTER="dateAfter"}return t})(),Rn=(()=>{class t{static AND="and";static OR="or"}return t})(),wn=(()=>{class t{filter(e,i,s,r,o){let a=[];if(e)for(let c of e)for(let l of i){let p=Ct(c,l);if(this.filters[r](p,s,o)){a.push(c);break}}return a}filters={startsWith:(e,i,s)=>{if(i==null||i.trim()==="")return!0;if(e==null)return!1;let r=C(i.toString()).toLocaleLowerCase(s);return C(e.toString()).toLocaleLowerCase(s).slice(0,r.length)===r},contains:(e,i,s)=>{if(i==null||typeof i=="string"&&i.trim()==="")return!0;if(e==null)return!1;let r=C(i.toString()).toLocaleLowerCase(s);return C(e.toString()).toLocaleLowerCase(s).indexOf(r)!==-1},notContains:(e,i,s)=>{if(i==null||typeof i=="string"&&i.trim()==="")return!0;if(e==null)return!1;let r=C(i.toString()).toLocaleLowerCase(s);return C(e.toString()).toLocaleLowerCase(s).indexOf(r)===-1},endsWith:(e,i,s)=>{if(i==null||i.trim()==="")return!0;if(e==null)return!1;let r=C(i.toString()).toLocaleLowerCase(s),o=C(e.toString()).toLocaleLowerCase(s);return o.indexOf(r,o.length-r.length)!==-1},equals:(e,i,s)=>i==null||typeof i=="string"&&i.trim()===""?!0:e==null?!1:e.getTime&&i.getTime?e.getTime()===i.getTime():e==i?!0:C(e.toString()).toLocaleLowerCase(s)==C(i.toString()).toLocaleLowerCase(s),notEquals:(e,i,s)=>i==null||typeof i=="string"&&i.trim()===""?!1:e==null?!0:e.getTime&&i.getTime?e.getTime()!==i.getTime():e==i?!1:C(e.toString()).toLocaleLowerCase(s)!=C(i.toString()).toLocaleLowerCase(s),in:(e,i)=>{if(i==null||i.length===0)return!0;for(let s=0;s<i.length;s++)if($t(e,i[s]))return!0;return!1},between:(e,i)=>i==null||i[0]==null||i[1]==null?!0:e==null?!1:e.getTime?i[0].getTime()<=e.getTime()&&e.getTime()<=i[1].getTime():i[0]<=e&&e<=i[1],lt:(e,i,s)=>i==null?!0:e==null?!1:e.getTime&&i.getTime?e.getTime()<i.getTime():e<i,lte:(e,i,s)=>i==null?!0:e==null?!1:e.getTime&&i.getTime?e.getTime()<=i.getTime():e<=i,gt:(e,i,s)=>i==null?!0:e==null?!1:e.getTime&&i.getTime?e.getTime()>i.getTime():e>i,gte:(e,i,s)=>i==null?!0:e==null?!1:e.getTime&&i.getTime?e.getTime()>=i.getTime():e>=i,is:(e,i,s)=>this.filters.equals(e,i,s),isNot:(e,i,s)=>this.filters.notEquals(e,i,s),before:(e,i,s)=>this.filters.lt(e,i,s),after:(e,i,s)=>this.filters.gt(e,i,s),dateIs:(e,i)=>i==null?!0:e==null?!1:e.toDateString()===i.toDateString(),dateIsNot:(e,i)=>i==null?!0:e==null?!1:e.toDateString()!==i.toDateString(),dateBefore:(e,i)=>i==null?!0:e==null?!1:e.getTime()<i.getTime(),dateAfter:(e,i)=>i==null?!0:e==null?!1:(e.setHours(0,0,0,0),e.getTime()>i.getTime())};register(e,i){this.filters[e]=i}static \u0275fac=function(i){return new(i||t)};static \u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var In=(()=>{class t{clickSource=new V;clickObservable=this.clickSource.asObservable();add(e){e&&this.clickSource.next(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var Nn=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=$({type:t,selectors:[["p-header"]],standalone:!1,ngContentSelectors:Ne,decls:1,vars:0,template:function(i,s){i&1&&(nt(),st(0))},encapsulation:2})}return t})(),Dn=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=$({type:t,selectors:[["p-footer"]],standalone:!1,ngContentSelectors:Ne,decls:1,vars:0,template:function(i,s){i&1&&(nt(),st(0))},encapsulation:2})}return t})(),xn=(()=>{class t{template;type;name;constructor(e){this.template=e}getType(){return this.name}static \u0275fac=function(i){return new(i||t)(ne(ee))};static \u0275dir=j({type:t,selectors:[["","pTemplate",""]],inputs:{type:"type",name:[0,"pTemplate","name"]}})}return t})(),Pn=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=Dt({type:t});static \u0275inj=Nt({imports:[fe]})}return t})(),Mn=(()=>{class t{static STARTS_WITH="startsWith";static CONTAINS="contains";static NOT_CONTAINS="notContains";static ENDS_WITH="endsWith";static EQUALS="equals";static NOT_EQUALS="notEquals";static NO_FILTER="noFilter";static LT="lt";static LTE="lte";static GT="gt";static GTE="gte";static IS="is";static IS_NOT="isNot";static BEFORE="before";static AFTER="after";static CLEAR="clear";static APPLY="apply";static MATCH_ALL="matchAll";static MATCH_ANY="matchAny";static ADD_RULE="addRule";static REMOVE_RULE="removeRule";static ACCEPT="accept";static REJECT="reject";static CHOOSE="choose";static UPLOAD="upload";static CANCEL="cancel";static PENDING="pending";static FILE_SIZE_TYPES="fileSizeTypes";static DAY_NAMES="dayNames";static DAY_NAMES_SHORT="dayNamesShort";static DAY_NAMES_MIN="dayNamesMin";static MONTH_NAMES="monthNames";static MONTH_NAMES_SHORT="monthNamesShort";static FIRST_DAY_OF_WEEK="firstDayOfWeek";static TODAY="today";static WEEK_HEADER="weekHeader";static WEAK="weak";static MEDIUM="medium";static STRONG="strong";static PASSWORD_PROMPT="passwordPrompt";static EMPTY_MESSAGE="emptyMessage";static EMPTY_FILTER_MESSAGE="emptyFilterMessage";static SHOW_FILTER_MENU="showFilterMenu";static HIDE_FILTER_MENU="hideFilterMenu";static SELECTION_MESSAGE="selectionMessage";static ARIA="aria";static SELECT_COLOR="selectColor";static BROWSE_FILES="browseFiles"}return t})(),kn=(()=>{class t{dragStartSource=new V;dragStopSource=new V;dragStart$=this.dragStartSource.asObservable();dragStop$=this.dragStopSource.asObservable();startDrag(e){this.dragStartSource.next(e)}stopDrag(e){this.dragStopSource.next(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=E({token:t,factory:t.\u0275fac})}return t})();var Si=Object.defineProperty,bi=Object.defineProperties,Ei=Object.getOwnPropertyDescriptors,wt=Object.getOwnPropertySymbols,Pe=Object.prototype.hasOwnProperty,Me=Object.prototype.propertyIsEnumerable,De=(t,n,e)=>n in t?Si(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e,x=(t,n)=>{for(var e in n||(n={}))Pe.call(n,e)&&De(t,e,n[e]);if(wt)for(var e of wt(n))Me.call(n,e)&&De(t,e,n[e]);return t},Ut=(t,n)=>bi(t,Ei(n)),M=(t,n)=>{var e={};for(var i in t)Pe.call(t,i)&&n.indexOf(i)<0&&(e[i]=t[i]);if(t!=null&&wt)for(var i of wt(t))n.indexOf(i)<0&&Me.call(t,i)&&(e[i]=t[i]);return e};function $n(...t){return ot(...t)}var _i=Ce(),O=_i;function xe(t,n){At(t)?t.push(...n||[]):N(t)&&Object.assign(t,n)}function vi(t){return N(t)&&t.hasOwnProperty("value")&&t.hasOwnProperty("type")?t.value:t}function Ti(t){return t.replaceAll(/ /g,"").replace(/[^\w]/g,"-")}function Bt(t="",n=""){return Ti(`${k(t,!1)&&k(n,!1)?`${t}-`:t}${n}`)}function ke(t="",n=""){return`--${Bt(t,n)}`}function Ci(t=""){let n=(t.match(/{/g)||[]).length,e=(t.match(/}/g)||[]).length;return(n+e)%2!==0}function Fe(t,n="",e="",i=[],s){if(k(t)){let r=/{([^}]*)}/g,o=t.trim();if(Ci(o))return;if(T(o,r)){let a=o.replaceAll(r,p=>{let u=p.replace(/{|}/g,"").split(".").filter(f=>!i.some(y=>T(f,y)));return`var(${ke(e,Lt(u.join("-")))}${m(s)?`, ${s}`:""})`}),c=/(\d+\s+[\+\-\*\/]\s+\d+)/g,l=/var\([^)]+\)/g;return T(a.replace(l,"0"),c)?`calc(${a})`:a}return o}else if(we(t))return t}function Oi(t,n,e){k(n,!1)&&t.push(`${n}:${e};`)}function Q(t,n){return t?`${t}{${n}}`:""}var J=(...t)=>Ai(h.getTheme(),...t),Ai=(t={},n,e,i)=>{if(n){let{variable:s,options:r}=h.defaults||{},{prefix:o,transform:a}=t?.options||r||{},l=T(n,/{([^}]*)}/g)?n:`{${n}}`;return i==="value"||W(i)&&a==="strict"?h.getTokenValue(n):Fe(l,void 0,o,[s.excludedKeyRegex],e)}return""};function Li(t,n={}){let e=h.defaults.variable,{prefix:i=e.prefix,selector:s=e.selector,excludedKeyRegex:r=e.excludedKeyRegex}=n,o=(l,p="")=>Object.entries(l).reduce((d,[u,f])=>{let y=T(u,r)?Bt(p):Bt(p,Lt(u)),b=vi(f);if(N(b)){let{variables:v,tokens:A}=o(b,y);xe(d.tokens,A),xe(d.variables,v)}else d.tokens.push((i?y.replace(`${i}-`,""):y).replaceAll("-",".")),Oi(d.variables,ke(y),Fe(b,y,i,[r]));return d},{variables:[],tokens:[]}),{variables:a,tokens:c}=o(t,i);return{value:a,tokens:c,declarations:a.join(""),css:Q(s,a.join(""))}}var D={regex:{rules:{class:{pattern:/^\.([a-zA-Z][\w-]*)$/,resolve(t){return{type:"class",selector:t,matched:this.pattern.test(t.trim())}}},attr:{pattern:/^\[(.*)\]$/,resolve(t){return{type:"attr",selector:`:root${t}`,matched:this.pattern.test(t.trim())}}},media:{pattern:/^@media (.*)$/,resolve(t){return{type:"media",selector:`${t}{:root{[CSS]}}`,matched:this.pattern.test(t.trim())}}},system:{pattern:/^system$/,resolve(t){return{type:"system",selector:"@media (prefers-color-scheme: dark){:root{[CSS]}}",matched:this.pattern.test(t.trim())}}},custom:{resolve(t){return{type:"custom",selector:t,matched:!0}}}},resolve(t){let n=Object.keys(this.rules).filter(e=>e!=="custom").map(e=>this.rules[e]);return[t].flat().map(e=>{var i;return(i=n.map(s=>s.resolve(e)).find(s=>s.matched))!=null?i:this.rules.custom.resolve(e)})}},_toVariables(t,n){return Li(t,{prefix:n?.prefix})},getCommon({name:t="",theme:n={},params:e,set:i,defaults:s}){var r,o,a,c,l,p,d;let{preset:u,options:f}=n,y,b,v,A,L,F,w;if(m(u)&&f.transform!=="strict"){let{primitive:at,semantic:lt,extend:ct}=u,tt=lt||{},{colorScheme:pt}=tt,ut=M(tt,["colorScheme"]),dt=ct||{},{colorScheme:ft}=dt,et=M(dt,["colorScheme"]),it=pt||{},{dark:ht}=it,mt=M(it,["dark"]),gt=ft||{},{dark:yt}=gt,St=M(gt,["dark"]),bt=m(at)?this._toVariables({primitive:at},f):{},Et=m(ut)?this._toVariables({semantic:ut},f):{},_t=m(mt)?this._toVariables({light:mt},f):{},jt=m(ht)?this._toVariables({dark:ht},f):{},Gt=m(et)?this._toVariables({semantic:et},f):{},Kt=m(St)?this._toVariables({light:St},f):{},Yt=m(yt)?this._toVariables({dark:yt},f):{},[Be,Ve]=[(r=bt.declarations)!=null?r:"",bt.tokens],[je,Ge]=[(o=Et.declarations)!=null?o:"",Et.tokens||[]],[Ke,Ye]=[(a=_t.declarations)!=null?a:"",_t.tokens||[]],[ze,qe]=[(c=jt.declarations)!=null?c:"",jt.tokens||[]],[Ze,Qe]=[(l=Gt.declarations)!=null?l:"",Gt.tokens||[]],[Je,Xe]=[(p=Kt.declarations)!=null?p:"",Kt.tokens||[]],[ti,ei]=[(d=Yt.declarations)!=null?d:"",Yt.tokens||[]];y=this.transformCSS(t,Be,"light","variable",f,i,s),b=Ve;let ii=this.transformCSS(t,`${je}${Ke}`,"light","variable",f,i,s),ni=this.transformCSS(t,`${ze}`,"dark","variable",f,i,s);v=`${ii}${ni}`,A=[...new Set([...Ge,...Ye,...qe])];let si=this.transformCSS(t,`${Ze}${Je}color-scheme:light`,"light","variable",f,i,s),ri=this.transformCSS(t,`${ti}color-scheme:dark`,"dark","variable",f,i,s);L=`${si}${ri}`,F=[...new Set([...Qe,...Xe,...ei])],w=R(u.css,{dt:J})}return{primitive:{css:y,tokens:b},semantic:{css:v,tokens:A},global:{css:L,tokens:F},style:w}},getPreset({name:t="",preset:n={},options:e,params:i,set:s,defaults:r,selector:o}){var a,c,l;let p,d,u;if(m(n)&&e.transform!=="strict"){let f=t.replace("-directive",""),y=n,{colorScheme:b,extend:v,css:A}=y,L=M(y,["colorScheme","extend","css"]),F=v||{},{colorScheme:w}=F,at=M(F,["colorScheme"]),lt=b||{},{dark:ct}=lt,tt=M(lt,["dark"]),pt=w||{},{dark:ut}=pt,dt=M(pt,["dark"]),ft=m(L)?this._toVariables({[f]:x(x({},L),at)},e):{},et=m(tt)?this._toVariables({[f]:x(x({},tt),dt)},e):{},it=m(ct)?this._toVariables({[f]:x(x({},ct),ut)},e):{},[ht,mt]=[(a=ft.declarations)!=null?a:"",ft.tokens||[]],[gt,yt]=[(c=et.declarations)!=null?c:"",et.tokens||[]],[St,bt]=[(l=it.declarations)!=null?l:"",it.tokens||[]],Et=this.transformCSS(f,`${ht}${gt}`,"light","variable",e,s,r,o),_t=this.transformCSS(f,St,"dark","variable",e,s,r,o);p=`${Et}${_t}`,d=[...new Set([...mt,...yt,...bt])],u=R(A,{dt:J})}return{css:p,tokens:d,style:u}},getPresetC({name:t="",theme:n={},params:e,set:i,defaults:s}){var r;let{preset:o,options:a}=n,c=(r=o?.components)==null?void 0:r[t];return this.getPreset({name:t,preset:c,options:a,params:e,set:i,defaults:s})},getPresetD({name:t="",theme:n={},params:e,set:i,defaults:s}){var r;let o=t.replace("-directive",""),{preset:a,options:c}=n,l=(r=a?.directives)==null?void 0:r[o];return this.getPreset({name:o,preset:l,options:c,params:e,set:i,defaults:s})},applyDarkColorScheme(t){return!(t.darkModeSelector==="none"||t.darkModeSelector===!1)},getColorSchemeOption(t,n){var e;return this.applyDarkColorScheme(t)?this.regex.resolve(t.darkModeSelector===!0?n.options.darkModeSelector:(e=t.darkModeSelector)!=null?e:n.options.darkModeSelector):[]},getLayerOrder(t,n={},e,i){let{cssLayer:s}=n;return s?`@layer ${R(s.order||"primeui",e)}`:""},getCommonStyleSheet({name:t="",theme:n={},params:e,props:i={},set:s,defaults:r}){let o=this.getCommon({name:t,theme:n,params:e,set:s,defaults:r}),a=Object.entries(i).reduce((c,[l,p])=>c.push(`${l}="${p}"`)&&c,[]).join(" ");return Object.entries(o||{}).reduce((c,[l,p])=>{if(p?.css){let d=U(p?.css),u=`${l}-variables`;c.push(`<style type="text/css" data-primevue-style-id="${u}" ${a}>${d}</style>`)}return c},[]).join("")},getStyleSheet({name:t="",theme:n={},params:e,props:i={},set:s,defaults:r}){var o;let a={name:t,theme:n,params:e,set:s,defaults:r},c=(o=t.includes("-directive")?this.getPresetD(a):this.getPresetC(a))==null?void 0:o.css,l=Object.entries(i).reduce((p,[d,u])=>p.push(`${d}="${u}"`)&&p,[]).join(" ");return c?`<style type="text/css" data-primevue-style-id="${t}-variables" ${l}>${U(c)}</style>`:""},createTokens(t={},n,e="",i="",s={}){return Object.entries(t).forEach(([r,o])=>{let a=T(r,n.variable.excludedKeyRegex)?e:e?`${e}.${Wt(r)}`:Wt(r),c=i?`${i}.${r}`:r;N(o)?this.createTokens(o,n,a,c,s):(s[a]||(s[a]={paths:[],computed(l,p={}){var d,u;return this.paths.length===1?(d=this.paths[0])==null?void 0:d.computed(this.paths[0].scheme,p.binding):l&&l!=="none"?(u=this.paths.find(f=>f.scheme===l))==null?void 0:u.computed(l,p.binding):this.paths.map(f=>f.computed(f.scheme,p[f.scheme]))}}),s[a].paths.push({path:c,value:o,scheme:c.includes("colorScheme.light")?"light":c.includes("colorScheme.dark")?"dark":"none",computed(l,p={}){let d=/{([^}]*)}/g,u=o;if(p.name=this.path,p.binding||(p.binding={}),T(o,d)){let y=o.trim().replaceAll(d,A=>{var L;let F=A.replace(/{|}/g,""),w=(L=s[F])==null?void 0:L.computed(l,p);return At(w)&&w.length===2?`light-dark(${w[0].value},${w[1].value})`:w?.value}),b=/(\d+\w*\s+[\+\-\*\/]\s+\d+\w*)/g,v=/var\([^)]+\)/g;u=T(y.replace(v,"0"),b)?`calc(${y})`:y}return W(p.binding)&&delete p.binding,{colorScheme:l,path:this.path,paths:p,value:u.includes("undefined")?void 0:u}}}))}),s},getTokenValue(t,n,e){var i;let r=(c=>c.split(".").filter(p=>!T(p.toLowerCase(),e.variable.excludedKeyRegex)).join("."))(n),o=n.includes("colorScheme.light")?"light":n.includes("colorScheme.dark")?"dark":void 0,a=[(i=t[r])==null?void 0:i.computed(o)].flat().filter(c=>c);return a.length===1?a[0].value:a.reduce((c={},l)=>{let p=l,{colorScheme:d}=p,u=M(p,["colorScheme"]);return c[d]=u,c},void 0)},getSelectorRule(t,n,e,i){return e==="class"||e==="attr"?Q(m(n)?`${t}${n},${t} ${n}`:t,i):Q(t,m(n)?Q(n,i):i)},transformCSS(t,n,e,i,s={},r,o,a){if(m(n)){let{cssLayer:c}=s;if(i!=="style"){let l=this.getColorSchemeOption(s,o);n=e==="dark"?l.reduce((p,{type:d,selector:u})=>(m(u)&&(p+=u.includes("[CSS]")?u.replace("[CSS]",n):this.getSelectorRule(u,a,d,n)),p),""):Q(a??":root",n)}if(c){let l={name:"primeui",order:"primeui"};N(c)&&(l.name=R(c.name,{name:t,type:i})),m(l.name)&&(n=Q(`@layer ${l.name}`,n),r?.layerNames(l.name))}return n}return""}},h={defaults:{variable:{prefix:"p",selector:":root",excludedKeyRegex:/^(primitive|semantic|components|directives|variables|colorscheme|light|dark|common|root|states|extend|css)$/gi},options:{prefix:"p",darkModeSelector:"system",cssLayer:!1}},_theme:void 0,_layerNames:new Set,_loadedStyleNames:new Set,_loadingStyles:new Set,_tokens:{},update(t={}){let{theme:n}=t;n&&(this._theme=Ut(x({},n),{options:x(x({},this.defaults.options),n.options)}),this._tokens=D.createTokens(this.preset,this.defaults),this.clearLoadedStyleNames())},get theme(){return this._theme},get preset(){var t;return((t=this.theme)==null?void 0:t.preset)||{}},get options(){var t;return((t=this.theme)==null?void 0:t.options)||{}},get tokens(){return this._tokens},getTheme(){return this.theme},setTheme(t){this.update({theme:t}),O.emit("theme:change",t)},getPreset(){return this.preset},setPreset(t){this._theme=Ut(x({},this.theme),{preset:t}),this._tokens=D.createTokens(t,this.defaults),this.clearLoadedStyleNames(),O.emit("preset:change",t),O.emit("theme:change",this.theme)},getOptions(){return this.options},setOptions(t){this._theme=Ut(x({},this.theme),{options:t}),this.clearLoadedStyleNames(),O.emit("options:change",t),O.emit("theme:change",this.theme)},getLayerNames(){return[...this._layerNames]},setLayerNames(t){this._layerNames.add(t)},getLoadedStyleNames(){return this._loadedStyleNames},isStyleNameLoaded(t){return this._loadedStyleNames.has(t)},setLoadedStyleName(t){this._loadedStyleNames.add(t)},deleteLoadedStyleName(t){this._loadedStyleNames.delete(t)},clearLoadedStyleNames(){this._loadedStyleNames.clear()},getTokenValue(t){return D.getTokenValue(this.tokens,t,this.defaults)},getCommon(t="",n){return D.getCommon({name:t,theme:this.theme,params:n,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},getComponent(t="",n){let e={name:t,theme:this.theme,params:n,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return D.getPresetC(e)},getDirective(t="",n){let e={name:t,theme:this.theme,params:n,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return D.getPresetD(e)},getCustomPreset(t="",n,e,i){let s={name:t,preset:n,options:this.options,selector:e,params:i,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return D.getPreset(s)},getLayerOrderCSS(t=""){return D.getLayerOrder(t,this.options,{names:this.getLayerNames()},this.defaults)},transformCSS(t="",n,e="style",i){return D.transformCSS(t,n,i,e,this.options,{layerNames:this.setLayerNames.bind(this)},this.defaults)},getCommonStyleSheet(t="",n,e={}){return D.getCommonStyleSheet({name:t,theme:this.theme,params:n,props:e,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},getStyleSheet(t,n,e={}){return D.getStyleSheet({name:t,theme:this.theme,params:n,props:e,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},onStyleMounted(t){this._loadingStyles.add(t)},onStyleUpdated(t){this._loadingStyles.add(t)},onStyleLoaded(t,{name:n}){this._loadingStyles.size&&(this._loadingStyles.delete(n),O.emit(`theme:${n}:load`,t),!this._loadingStyles.size&&O.emit("theme:load"))}};var Ri=0,He=(()=>{class t{document=g(Y);use(e,i={}){let s=!1,r=e,o=null,{immediate:a=!0,manual:c=!1,name:l=`style_${++Ri}`,id:p=void 0,media:d=void 0,nonce:u=void 0,first:f=!1,props:y={}}=i;if(this.document){if(o=this.document.querySelector(`style[data-primeng-style-id="${l}"]`)||p&&this.document.getElementById(p)||this.document.createElement("style"),!o.isConnected){r=e,Tt(o,{type:"text/css",media:d,nonce:u});let b=this.document.head;f&&b.firstChild?b.insertBefore(o,b.firstChild):b.appendChild(o),Te(o,"data-primeng-style-id",l)}return o.textContent!==r&&(o.textContent=r),{id:p,name:l,el:o,css:r}}}static \u0275fac=function(i){return new(i||t)};static \u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var X={_loadedStyleNames:new Set,getLoadedStyleNames(){return this._loadedStyleNames},isStyleNameLoaded(t){return this._loadedStyleNames.has(t)},setLoadedStyleName(t){this._loadedStyleNames.add(t)},deleteLoadedStyleName(t){this._loadedStyleNames.delete(t)},clearLoadedStyleNames(){this._loadedStyleNames.clear()}},wi=({dt:t})=>`
*,
::before,
::after {
    box-sizing: border-box;
}

/* Non ng overlay animations */
.p-connected-overlay {
    opacity: 0;
    transform: scaleY(0.8);
    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1),
        opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.p-connected-overlay-visible {
    opacity: 1;
    transform: scaleY(1);
}

.p-connected-overlay-hidden {
    opacity: 0;
    transform: scaleY(1);
    transition: opacity 0.1s linear;
}

/* NG based overlay animations */
.p-connected-overlay-enter-from {
    opacity: 0;
    transform: scaleY(0.8);
}

.p-connected-overlay-leave-to {
    opacity: 0;
}

.p-connected-overlay-enter-active {
    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1),
        opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.p-connected-overlay-leave-active {
    transition: opacity 0.1s linear;
}

/* Toggleable Content */
.p-toggleable-content-enter-from,
.p-toggleable-content-leave-to {
    max-height: 0;
}

.p-toggleable-content-enter-to,
.p-toggleable-content-leave-from {
    max-height: 1000px;
}

.p-toggleable-content-leave-active {
    overflow: hidden;
    transition: max-height 0.45s cubic-bezier(0, 1, 0, 1);
}

.p-toggleable-content-enter-active {
    overflow: hidden;
    transition: max-height 1s ease-in-out;
}

.p-disabled,
.p-disabled * {
    cursor: default;
    pointer-events: none;
    user-select: none;
}

.p-disabled,
.p-component:disabled {
    opacity: ${t("disabled.opacity")};
}

.pi {
    font-size: ${t("icon.size")};
}

.p-icon {
    width: ${t("icon.size")};
    height: ${t("icon.size")};
}

.p-unselectable-text {
    user-select: none;
}

.p-overlay-mask {
    background: ${t("mask.background")};
    color: ${t("mask.color")};
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.p-overlay-mask-enter {
    animation: p-overlay-mask-enter-animation ${t("mask.transition.duration")} forwards;
}

.p-overlay-mask-leave {
    animation: p-overlay-mask-leave-animation ${t("mask.transition.duration")} forwards;
}
/* Temporarily disabled, distrupts PrimeNG overlay animations */
/* @keyframes p-overlay-mask-enter-animation {
    from {
        background: transparent;
    }
    to {
        background: ${t("mask.background")};
    }
}
@keyframes p-overlay-mask-leave-animation {
    from {
        background: ${t("mask.background")};
    }
    to {
        background: transparent;
    }
}*/

.p-iconwrapper {
    display: inline-flex;
    justify-content: center;
    align-items: center;
}
`,Ii=({dt:t})=>`
.p-hidden-accessible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}

.p-hidden-accessible input,
.p-hidden-accessible select {
    transform: scale(0);
}

.p-overflow-hidden {
    overflow: hidden;
    padding-right: ${t("scrollbar.width")};
}

/* @todo move to baseiconstyle.ts */

.p-icon {
    display: inline-block;
    vertical-align: baseline;
}

.p-icon-spin {
    -webkit-animation: p-icon-spin 2s infinite linear;
    animation: p-icon-spin 2s infinite linear;
}

@-webkit-keyframes p-icon-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}

@keyframes p-icon-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}
`,P=(()=>{class t{name="base";useStyle=g(He);theme=void 0;css=void 0;classes={};inlineStyles={};load=(e,i={},s=r=>r)=>{let r=s(R(e,{dt:J}));return r?this.useStyle.use(U(r),S({name:this.name},i)):{}};loadCSS=(e={})=>this.load(this.css,e);loadTheme=(e={},i="")=>this.load(this.theme,e,(s="")=>h.transformCSS(e.name||this.name,`${s}${i}`));loadGlobalCSS=(e={})=>this.load(Ii,e);loadGlobalTheme=(e={},i="")=>this.load(wi,e,(s="")=>h.transformCSS(e.name||this.name,`${s}${i}`));getCommonTheme=e=>h.getCommon(this.name,e);getComponentTheme=e=>h.getComponent(this.name,e);getDirectiveTheme=e=>h.getDirective(this.name,e);getPresetTheme=(e,i,s)=>h.getCustomPreset(this.name,e,i,s);getLayerOrderThemeCSS=()=>h.getLayerOrderCSS(this.name);getStyleSheet=(e="",i={})=>{if(this.css){let s=R(this.css,{dt:J}),r=U(`${s}${e}`),o=Object.entries(i).reduce((a,[c,l])=>a.push(`${c}="${l}"`)&&a,[]).join(" ");return`<style type="text/css" data-primeng-style-id="${this.name}" ${o}>${r}</style>`}return""};getCommonThemeStyleSheet=(e,i={})=>h.getCommonStyleSheet(this.name,e,i);getThemeStyleSheet=(e,i={})=>{let s=[h.getStyleSheet(this.name,e,i)];if(this.theme){let r=this.name==="base"?"global-style":`${this.name}-style`,o=R(this.theme,{dt:J}),a=U(h.transformCSS(r,o)),c=Object.entries(i).reduce((l,[p,d])=>l.push(`${p}="${d}"`)&&l,[]).join(" ");s.push(`<style type="text/css" data-primeng-style-id="${r}" ${c}>${a}</style>`)}return s.join("")};static \u0275fac=function(i){return new(i||t)};static \u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var Ni=(()=>{class t{theme=H(void 0);csp=H({nonce:void 0});isThemeChanged=!1;document=g(Y);baseStyle=g(P);constructor(){rt(()=>{O.on("theme:change",e=>{de(()=>{this.isThemeChanged=!0,this.theme.set(e)})})}),rt(()=>{let e=this.theme();this.document&&e&&(this.isThemeChanged||this.onThemeChange(e),this.isThemeChanged=!1)})}ngOnDestroy(){h.clearLoadedStyleNames(),O.clear()}onThemeChange(e){h.setTheme(e),this.document&&this.loadCommonTheme()}loadCommonTheme(){if(this.theme()!=="none"&&!h.isStyleNameLoaded("common")){let{primitive:e,semantic:i,global:s,style:r}=this.baseStyle.getCommonTheme?.()||{},o={nonce:this.csp?.()?.nonce};this.baseStyle.load(e?.css,S({name:"primitive-variables"},o)),this.baseStyle.load(i?.css,S({name:"semantic-variables"},o)),this.baseStyle.load(s?.css,S({name:"global-variables"},o)),this.baseStyle.loadGlobalTheme(S({name:"global-style"},o),r),h.setLoadedStyleName("common")}}setThemeConfig(e){let{theme:i,csp:s}=e||{};i&&this.theme.set(i),s&&this.csp.set(s)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Vt=(()=>{class t extends Ni{ripple=H(!1);platformId=g(vt);inputStyle=H(null);inputVariant=H(null);overlayOptions={};csp=H({nonce:void 0});filterMatchModeOptions={text:[_.STARTS_WITH,_.CONTAINS,_.NOT_CONTAINS,_.ENDS_WITH,_.EQUALS,_.NOT_EQUALS],numeric:[_.EQUALS,_.NOT_EQUALS,_.LESS_THAN,_.LESS_THAN_OR_EQUAL_TO,_.GREATER_THAN,_.GREATER_THAN_OR_EQUAL_TO],date:[_.DATE_IS,_.DATE_IS_NOT,_.DATE_BEFORE,_.DATE_AFTER]};translation={startsWith:"Starts with",contains:"Contains",notContains:"Not contains",endsWith:"Ends with",equals:"Equals",notEquals:"Not equals",noFilter:"No Filter",lt:"Less than",lte:"Less than or equal to",gt:"Greater than",gte:"Greater than or equal to",is:"Is",isNot:"Is not",before:"Before",after:"After",dateIs:"Date is",dateIsNot:"Date is not",dateBefore:"Date is before",dateAfter:"Date is after",clear:"Clear",apply:"Apply",matchAll:"Match All",matchAny:"Match Any",addRule:"Add Rule",removeRule:"Remove Rule",accept:"Yes",reject:"No",choose:"Choose",upload:"Upload",cancel:"Cancel",pending:"Pending",fileSizeTypes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],chooseYear:"Choose Year",chooseMonth:"Choose Month",chooseDate:"Choose Date",prevDecade:"Previous Decade",nextDecade:"Next Decade",prevYear:"Previous Year",nextYear:"Next Year",prevMonth:"Previous Month",nextMonth:"Next Month",prevHour:"Previous Hour",nextHour:"Next Hour",prevMinute:"Previous Minute",nextMinute:"Next Minute",prevSecond:"Previous Second",nextSecond:"Next Second",am:"am",pm:"pm",dateFormat:"mm/dd/yy",firstDayOfWeek:0,today:"Today",weekHeader:"Wk",weak:"Weak",medium:"Medium",strong:"Strong",passwordPrompt:"Enter a password",emptyMessage:"No results found",searchMessage:"Search results are available",selectionMessage:"{0} items selected",emptySelectionMessage:"No selected item",emptySearchMessage:"No results found",emptyFilterMessage:"No results found",fileChosenMessage:"Files",noFileChosenMessage:"No file chosen",aria:{trueLabel:"True",falseLabel:"False",nullLabel:"Not Selected",star:"1 star",stars:"{star} stars",selectAll:"All items selected",unselectAll:"All items unselected",close:"Close",previous:"Previous",next:"Next",navigation:"Navigation",scrollTop:"Scroll Top",moveTop:"Move Top",moveUp:"Move Up",moveDown:"Move Down",moveBottom:"Move Bottom",moveToTarget:"Move to Target",moveToSource:"Move to Source",moveAllToTarget:"Move All to Target",moveAllToSource:"Move All to Source",pageLabel:"{page}",firstPageLabel:"First Page",lastPageLabel:"Last Page",nextPageLabel:"Next Page",prevPageLabel:"Previous Page",rowsPerPageLabel:"Rows per page",previousPageLabel:"Previous Page",jumpToPageDropdownLabel:"Jump to Page Dropdown",jumpToPageInputLabel:"Jump to Page Input",selectRow:"Row Selected",unselectRow:"Row Unselected",expandRow:"Row Expanded",collapseRow:"Row Collapsed",showFilterMenu:"Show Filter Menu",hideFilterMenu:"Hide Filter Menu",filterOperator:"Filter Operator",filterConstraint:"Filter Constraint",editRow:"Row Edit",saveEdit:"Save Edit",cancelEdit:"Cancel Edit",listView:"List View",gridView:"Grid View",slide:"Slide",slideNumber:"{slideNumber}",zoomImage:"Zoom Image",zoomIn:"Zoom In",zoomOut:"Zoom Out",rotateRight:"Rotate Right",rotateLeft:"Rotate Left",listLabel:"Option List",selectColor:"Select a color",removeLabel:"Remove",browseFiles:"Browse Files",maximizeLabel:"Maximize"}};zIndex={modal:1100,overlay:1e3,menu:1e3,tooltip:1100};translationSource=new V;translationObserver=this.translationSource.asObservable();getTranslation(e){return this.translation[e]}setTranslation(e){this.translation=S(S({},this.translation),e),this.translationSource.next(this.translation)}setConfig(e){let{csp:i,ripple:s,inputStyle:r,inputVariant:o,theme:a,overlayOptions:c,translation:l,filterMatchModeOptions:p}=e||{};i&&this.csp.set(i),s&&this.ripple.set(s),r&&this.inputStyle.set(r),o&&this.inputVariant.set(o),c&&(this.overlayOptions=c),l&&this.setTranslation(l),p&&(this.filterMatchModeOptions=p),a&&this.setThemeConfig({theme:a,csp:i})}static \u0275fac=(()=>{let e;return function(s){return(e||(e=I(t)))(s||t)}})();static \u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Di=new zt("PRIME_NG_CONFIG");function ms(...t){let n=t?.map(i=>({provide:Di,useValue:i,multi:!1})),e=se(()=>{let i=g(Vt);t?.forEach(s=>i.setConfig(s))});return qt([...n,e])}var $e=(()=>{class t extends P{name="common";static \u0275fac=(()=>{let e;return function(s){return(e||(e=I(t)))(s||t)}})();static \u0275prov=E({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),It=(()=>{class t{document=g(Y);platformId=g(vt);el=g(te);injector=g(Jt);cd=g(pe);renderer=g(ie);config=g(Vt);baseComponentStyle=g($e);baseStyle=g(P);scopedStyleEl;rootEl;dt;get styleOptions(){return{nonce:this.config?.csp().nonce}}get _name(){return this.constructor.name.replace(/^_/,"").toLowerCase()}get componentStyle(){return this._componentStyle}attrSelector=Ie("pc");themeChangeListeners=[];_getHostInstance(e){if(e)return e?this.hostName?e.name===this.hostName?e:this._getHostInstance(e.parentInstance):e.parentInstance:void 0}_getOptionValue(e,i="",s={}){return Ot(e,i,s)}ngOnInit(){this.document&&this._loadStyles()}ngAfterViewInit(){this.rootEl=this.el?.nativeElement,this.rootEl&&this.rootEl?.setAttribute(this.attrSelector,"")}ngOnChanges(e){if(this.document&&!me(this.platformId)){let{dt:i}=e;i&&i.currentValue&&(this._loadScopedThemeStyles(i.currentValue),this._themeChangeListener(()=>this._loadScopedThemeStyles(i.currentValue)))}}ngOnDestroy(){this._unloadScopedThemeStyles(),this.themeChangeListeners.forEach(e=>O.off("theme:change",e))}_loadStyles(){let e=()=>{X.isStyleNameLoaded("base")||(this.baseStyle.loadGlobalCSS(this.styleOptions),X.setLoadedStyleName("base")),this._loadThemeStyles()};e(),this._themeChangeListener(()=>e())}_loadCoreStyles(){!X.isStyleNameLoaded("base")&&this._name&&(this.baseComponentStyle.loadCSS(this.styleOptions),this.componentStyle&&this.componentStyle?.loadCSS(this.styleOptions),X.setLoadedStyleName(this.componentStyle?.name))}_loadThemeStyles(){if(!h.isStyleNameLoaded("common")){let{primitive:e,semantic:i,global:s,style:r}=this.componentStyle?.getCommonTheme?.()||{};this.baseStyle.load(e?.css,S({name:"primitive-variables"},this.styleOptions)),this.baseStyle.load(i?.css,S({name:"semantic-variables"},this.styleOptions)),this.baseStyle.load(s?.css,S({name:"global-variables"},this.styleOptions)),this.baseStyle.loadGlobalTheme(S({name:"global-style"},this.styleOptions),r),h.setLoadedStyleName("common")}if(!h.isStyleNameLoaded(this.componentStyle?.name)&&this.componentStyle?.name){let{css:e,style:i}=this.componentStyle?.getComponentTheme?.()||{};this.componentStyle?.load(e,S({name:`${this.componentStyle?.name}-variables`},this.styleOptions)),this.componentStyle?.loadTheme(S({name:`${this.componentStyle?.name}-style`},this.styleOptions),i),h.setLoadedStyleName(this.componentStyle?.name)}if(!h.isStyleNameLoaded("layer-order")){let e=this.componentStyle?.getLayerOrderThemeCSS?.();this.baseStyle.load(e,S({name:"layer-order",first:!0},this.styleOptions)),h.setLoadedStyleName("layer-order")}this.dt&&(this._loadScopedThemeStyles(this.dt),this._themeChangeListener(()=>this._loadScopedThemeStyles(this.dt)))}_loadScopedThemeStyles(e){let{css:i}=this.componentStyle?.getPresetTheme?.(e,`[${this.attrSelector}]`)||{},s=this.componentStyle?.load(i,S({name:`${this.attrSelector}-${this.componentStyle?.name}`},this.styleOptions));this.scopedStyleEl=s?.el}_unloadScopedThemeStyles(){this.scopedStyleEl?.remove()}_themeChangeListener(e=()=>{}){X.clearLoadedStyleNames(),O.on("theme:change",e),this.themeChangeListeners.push(e)}cx(e,i){let s=this.parent?this.parent.componentStyle?.classes?.[e]:this.componentStyle?.classes?.[e];return typeof s=="function"?s({instance:this}):typeof s=="string"?s:e}sx(e){let i=this.componentStyle?.inlineStyles?.[e];return typeof i=="function"?i({instance:this}):typeof i=="string"?i:S({},i)}get parent(){return this.parentInstance}static \u0275fac=function(i){return new(i||t)};static \u0275dir=j({type:t,inputs:{dt:"dt"},features:[K([$e,P]),Zt]})}return t})();var xi=["*"],Pi=`
.p-icon {
    display: inline-block;
    vertical-align: baseline;
}

.p-icon-spin {
    -webkit-animation: p-icon-spin 2s infinite linear;
    animation: p-icon-spin 2s infinite linear;
}

@-webkit-keyframes p-icon-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}

@keyframes p-icon-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}
`,Mi=(()=>{class t extends P{name="baseicon";inlineStyles=Pi;static \u0275fac=(()=>{let e;return function(s){return(e||(e=I(t)))(s||t)}})();static \u0275prov=E({token:t,factory:t.\u0275fac})}return t})();var We=(()=>{class t extends It{label;spin=!1;styleClass;role;ariaLabel;ariaHidden;ngOnInit(){super.ngOnInit(),this.getAttributes()}getAttributes(){let e=W(this.label);this.role=e?void 0:"img",this.ariaLabel=e?void 0:this.label,this.ariaHidden=e}getClassNames(){return`p-icon ${this.styleClass?this.styleClass+" ":""}${this.spin?"p-icon-spin":""}`}static \u0275fac=(()=>{let e;return function(s){return(e||(e=I(t)))(s||t)}})();static \u0275cmp=$({type:t,selectors:[["ng-component"]],hostAttrs:[1,"p-component","p-iconwrapper"],inputs:{label:"label",spin:[2,"spin","spin",ue],styleClass:"styleClass"},features:[K([Mi]),G],ngContentSelectors:xi,decls:1,vars:0,template:function(i,s){i&1&&(nt(),st(0))},encapsulation:2,changeDetection:0})}return t})();var Ws=(()=>{class t extends We{static \u0275fac=(()=>{let e;return function(s){return(e||(e=I(t)))(s||t)}})();static \u0275cmp=$({type:t,selectors:[["ChevronDownIcon"]],features:[G],decls:2,vars:5,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M7.01744 10.398C6.91269 10.3985 6.8089 10.378 6.71215 10.3379C6.61541 10.2977 6.52766 10.2386 6.45405 10.1641L1.13907 4.84913C1.03306 4.69404 0.985221 4.5065 1.00399 4.31958C1.02276 4.13266 1.10693 3.95838 1.24166 3.82747C1.37639 3.69655 1.55301 3.61742 1.74039 3.60402C1.92777 3.59062 2.11386 3.64382 2.26584 3.75424L7.01744 8.47394L11.769 3.75424C11.9189 3.65709 12.097 3.61306 12.2748 3.62921C12.4527 3.64535 12.6199 3.72073 12.7498 3.84328C12.8797 3.96582 12.9647 4.12842 12.9912 4.30502C13.0177 4.48162 12.9841 4.662 12.8958 4.81724L7.58083 10.1322C7.50996 10.2125 7.42344 10.2775 7.32656 10.3232C7.22968 10.3689 7.12449 10.3944 7.01744 10.398Z","fill","currentColor"]],template:function(i,s){i&1&&(Qt(),ae(0,"svg",0),ce(1,"path",1),le()),i&2&&(oe(s.getClassNames()),re("aria-label",s.ariaLabel)("aria-hidden",s.ariaHidden)("role",s.role))},encapsulation:2})}return t})();var ki=({dt:t})=>`
/* For PrimeNG */
.p-ripple {
    overflow: hidden;
    position: relative;
}

.p-ink {
    display: block;
    position: absolute;
    background: ${t("ripple.background")};
    border-radius: 100%;
    transform: scale(0);
}

.p-ink-active {
    animation: ripple 0.4s linear;
}

.p-ripple-disabled .p-ink {
    display: none !important;
}

@keyframes ripple {
    100% {
        opacity: 0;
        transform: scale(2.5);
    }
}
`,Fi={root:"p-ink"},Ue=(()=>{class t extends P{name="ripple";theme=ki;classes=Fi;static \u0275fac=(()=>{let e;return function(s){return(e||(e=I(t)))(s||t)}})();static \u0275prov=E({token:t,factory:t.\u0275fac})}return t})();var Zs=(()=>{class t extends It{zone=g(Xt);_componentStyle=g(Ue);animationListener;mouseDownListener;timeout;constructor(){super(),rt(()=>{he(this.platformId)&&(this.config.ripple()?this.zone.runOutsideAngular(()=>{this.create(),this.mouseDownListener=this.renderer.listen(this.el.nativeElement,"mousedown",this.onMouseDown.bind(this))}):this.remove())})}ngAfterViewInit(){super.ngAfterViewInit()}onMouseDown(e){let i=this.getInk();if(!i||this.document.defaultView?.getComputedStyle(i,null).display==="none")return;if(q(i,"p-ink-active"),!Mt(i)&&!Ft(i)){let a=Math.max(Se(this.el.nativeElement),kt(this.el.nativeElement));i.style.height=a+"px",i.style.width=a+"px"}let s=_e(this.el.nativeElement),r=e.pageX-s.left+this.document.body.scrollTop-Ft(i)/2,o=e.pageY-s.top+this.document.body.scrollLeft-Mt(i)/2;this.renderer.setStyle(i,"top",o+"px"),this.renderer.setStyle(i,"left",r+"px"),xt(i,"p-ink-active"),this.timeout=setTimeout(()=>{let a=this.getInk();a&&q(a,"p-ink-active")},401)}getInk(){let e=this.el.nativeElement.children;for(let i=0;i<e.length;i++)if(typeof e[i].className=="string"&&e[i].className.indexOf("p-ink")!==-1)return e[i];return null}resetInk(){let e=this.getInk();e&&q(e,"p-ink-active")}onAnimationEnd(e){this.timeout&&clearTimeout(this.timeout),q(e.currentTarget,"p-ink-active")}create(){let e=this.renderer.createElement("span");this.renderer.addClass(e,"p-ink"),this.renderer.appendChild(this.el.nativeElement,e),this.renderer.setAttribute(e,"aria-hidden","true"),this.renderer.setAttribute(e,"role","presentation"),this.animationListener||(this.animationListener=this.renderer.listen(e,"animationend",this.onAnimationEnd.bind(this)))}remove(){let e=this.getInk();e&&(this.mouseDownListener&&this.mouseDownListener(),this.animationListener&&this.animationListener(),this.mouseDownListener=null,this.animationListener=null,ve(e))}ngOnDestroy(){this.config&&this.config.ripple()&&this.remove(),super.ngOnDestroy()}static \u0275fac=function(i){return new(i||t)};static \u0275dir=j({type:t,selectors:[["","pRipple",""]],hostAttrs:[1,"p-ripple"],features:[K([Ue]),G]})}return t})();export{oi as a,xt as b,$i as c,q as d,Wi as e,ye as f,li as g,ci as h,Ui as i,Bi as j,Se as k,Vi as l,ji as m,Gi as n,Ki as o,pi as p,Yi as q,zi as r,qi as s,be as t,Zi as u,Mt as v,Qi as w,Ji as x,kt as y,Xi as z,tn as A,Ft as B,en as C,nn as D,sn as E,rn as F,Te as G,W as H,gi as I,m as J,Ct as K,$t as L,cn as M,pn as N,R as O,un as P,dn as Q,C as R,Ie as S,_ as T,Rn as U,wn as V,In as W,Nn as X,Dn as Y,xn as Z,Pn as _,Mn as $,kn as aa,$n as ba,P as ca,ms as da,It as ea,We as fa,Ws as ga,Zs as ha};
