using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Buckets;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Articles
{
    [RemoteService(false)]
    public class ReadOnlyArticleFileAppService : ApplicationService, IReadOnlyArticleFileAppService
    {
        protected readonly IRepository<ArticleFile, int> _repository;
        protected readonly IRepository<Article, int> _articleRepository;
        protected readonly IRepository<BucketFile, int> _bucketFileRepository;

        public ReadOnlyArticleFileAppService(
            IRepository<ArticleFile, int> repository,
            IRepository<Article, int> articleRepository,
            IRepository<BucketFile, int> bucketFileRepository)
        {
            _repository = repository;
            _articleRepository = articleRepository;
            _bucketFileRepository = bucketFileRepository;
        }

        public virtual async Task<ArticleFileDto> GetAsync(int id)
        {
            var query = await _repository.GetQueryableAsync();
            var articleFile = await query
                .Include(af => af.Article)
                .Include(af => af.BucketFile)
                .FirstOrDefaultAsync(af => af.Id == id);

            if (articleFile == null)
            {
                throw new EntityNotFoundException(typeof(ArticleFile), id);
            }

            return ObjectMapper.Map<ArticleFile, ArticleFileDto>(articleFile);
        }

        public virtual async Task<PagedResultDto<ArticleFileDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var query = await _repository.GetQueryableAsync();
            query = query
                .Include(af => af.Article)
                .Include(af => af.BucketFile)
                .OrderBy(input.Sorting ?? "CreationTime desc");

            var totalCount = await query.CountAsync();
            query = query.Skip(input.SkipCount).Take(input.MaxResultCount);
            var items = await query.ToListAsync();

            return new PagedResultDto<ArticleFileDto>(
                totalCount,
                ObjectMapper.Map<List<ArticleFile>, List<ArticleFileDto>>(items)
            );
        }

        public virtual async Task<List<ArticleFileDto>> GetByArticleIdAsync(int articleId)
        {
            var query = await _repository.GetQueryableAsync();
            var articleFiles = await query
                .Include(af => af.BucketFile)
                .Where(af => af.ArticleId == articleId)
                .OrderByDescending(af => af.IsPrimary)
                .ThenBy(af => af.CreationTime)
                .ToListAsync();
            return ObjectMapper.Map<List<ArticleFile>, List<ArticleFileDto>>(articleFiles);
        }

        public virtual async Task<List<ArticleFileDto>> GetPrimaryFilesAsync(int articleId)
        {
            var query = await _repository.GetQueryableAsync();
            var primaryFiles = await query
                .Include(af => af.BucketFile)
                .Where(af => af.ArticleId == articleId && af.IsPrimary)
                .OrderBy(af => af.CreationTime)
                .ToListAsync();
            return ObjectMapper.Map<List<ArticleFile>, List<ArticleFileDto>>(primaryFiles);
        }
    }
}