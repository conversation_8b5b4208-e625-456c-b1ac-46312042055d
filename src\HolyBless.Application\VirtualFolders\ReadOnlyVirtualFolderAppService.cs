using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Enums;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.VirtualFolders
{
    [AllowAnonymous]
    public class ReadOnlyVirtualFolderAppService : HolyBlessAppService, IReadOnlyVirtualFolderAppService
    {
        protected readonly IRepository<VirtualDiskFolder, int> _virtualFolderRepository;
        protected readonly IRepository<FolderToFile> _folderToBucketFileRepository;
        protected readonly IRepository<VirtualDiskFolderTree, int> _folderTreeRepository;

        public ReadOnlyVirtualFolderAppService(
             IRepository<VirtualDiskFolder, int> virtualFolderRepository,
             IRepository<FolderToFile> folderToBucketFileRepository,
             IRepository<VirtualDiskFolderTree, int> folderTreeRepository,
             IRequestContextService requestContextService,
             ICachedFileUrlAppService cachedFileUrlAppService
            ) : base(cachedFileUrlAppService, requestContextService)
        {
            _folderToBucketFileRepository = folderToBucketFileRepository;
            _folderTreeRepository = folderTreeRepository;
            _virtualFolderRepository = virtualFolderRepository;
        }

        /// <summary>
        /// Get a paginated list of virtual folders based on search criteria.
        /// Usage: Virtual folder list page API
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<VirtualFolderDto>> GetListAsync(VirtualFolderSearchDto input)
        {
            var queryable = await _virtualFolderRepository.GetQueryableAsync();
            var lang = _requestContextService!.GetLanguageCode();
            var query = queryable
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == input.ChannelId)
                .WhereIf(!string.IsNullOrEmpty(input.ContentCode), x => x.ContentCode == input.ContentCode && x.LanguageCode == lang);

            var totalCount = await query.CountAsync();

            var virtualFolders = await query
                .OrderBy(input.Sorting ?? "Weight desc, CreationTime desc")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var virtualFolderDtos = ObjectMapper.Map<List<VirtualDiskFolder>, List<VirtualFolderDto>>(virtualFolders);

            return new PagedResultDto<VirtualFolderDto>(totalCount, virtualFolderDtos);
        }

        /// <summary>
        /// Get a paginated list of files within a virtual folder with sorting support.
        /// Similar to GetAlbumFilesAsync but with pagination and sorting.
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<VirtualFolderFileDto>> GetFolderFilesAsync(VirtualFolderFileSearchDto input)
        {
            var queryable = await _folderToBucketFileRepository.GetQueryableAsync();
            var lang = _requestContextService!.GetLanguageCode();
            var query = queryable
                .Include(x => x.BucketFile)
                .WhereIf(input.FolderId.HasValue, x => x.FolderId == input.FolderId)
                .WhereIf(!string.IsNullOrEmpty(input.ContentCode), x => input.ContentCode == x.Folder.ContentCode && x.Folder.LanguageCode == lang);

            var totalCount = await query.CountAsync();

            var folderFiles = await query
                .OrderBy(input.Sorting ?? "BucketFile.DeliveryDate")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var folderFileDtos = folderFiles.Select(ftf => new VirtualFolderFileDto
            {
                FileId = ftf.BucketFileId,
                FileName = ftf.BucketFile.FileName,
                Title = ftf.Title ?? ftf.BucketFile.Title
            }).ToList();

            await FillFileUrls(folderFileDtos);

            return new PagedResultDto<VirtualFolderFileDto>(totalCount, folderFileDtos);
        }
    }
}