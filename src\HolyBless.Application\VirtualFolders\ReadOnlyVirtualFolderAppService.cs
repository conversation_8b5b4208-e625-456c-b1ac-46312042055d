using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.VirtualFolders
{
    [AllowAnonymous]
    public class ReadOnlyVirtualFolderAppService : HolyBlessAppService, IReadOnlyVirtualFolderAppService
    {
        protected readonly IRepository<VirtualDiskFolder, int> _virtualFolderRepository;
        protected readonly IRepository<FolderToFile> _folderToBucketFileRepository;
        protected readonly IRepository<VirtualDiskFolderTree, int> _folderTreeRepository;

        public ReadOnlyVirtualFolderAppService(
             IRepository<VirtualDiskFolder, int> virtualFolderRepository,
             IRepository<FolderToFile> folderToBucketFileRepository,
             IRepository<VirtualDiskFolderTree, int> folderTreeRepository,
             IRequestContextService requestContextService,
             ICachedFileUrlAppService cachedFileUrlAppService
            ) : base(cachedFileUrlAppService, requestContextService)
        {
            _folderToBucketFileRepository = folderToBucketFileRepository;
            _folderTreeRepository = folderTreeRepository;
            _virtualFolderRepository = virtualFolderRepository;
        }

        /// <summary>
        /// Get a paginated list of virtual folders based on search criteria.
        /// Usage: Virtual folder list page API
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<VirtualFolderDto>> GetListAsync(VirtualFolderSearchDto input)
        {
            var queryable = await _virtualFolderRepository.GetQueryableAsync();

            var query = queryable
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == input.ChannelId);

            var totalCount = await query.CountAsync();

            var virtualFolders = await query
                .OrderBy(input.Sorting ?? "Weight desc, CreationTime desc")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var virtualFolderDtos = ObjectMapper.Map<List<VirtualDiskFolder>, List<VirtualFolderDto>>(virtualFolders);

            return new PagedResultDto<VirtualFolderDto>(totalCount, virtualFolderDtos);
        }

        [RemoteService(false)]
        public async Task<string> GetFolderTreeJson(int rootFolderId)
        {
            var tree = await _folderTreeRepository.GetAsync(rootFolderId);
            return tree.TreeJsonData;
        }

        public async Task<VirtualFolderTreeDto> GetFolderTreeWithFilesAsync(int rootFolderId)
        {
            var rootFolder = await _virtualFolderRepository.GetAsync(rootFolderId);
            if (rootFolder == null)
            {
                throw new EntityNotFoundException(typeof(VirtualDiskFolder), rootFolderId);
            }
            var allFolders = await _virtualFolderRepository.GetListAsync();
            var allFolderToFileMappings = await _folderToBucketFileRepository.GetListAsync();
            return await BuildTree(rootFolder, allFolders, allFolderToFileMappings);
        }

        private async Task<VirtualFolderTreeDto> BuildTree(
            VirtualDiskFolder folder,
            List<VirtualDiskFolder> allFolders,
            List<FolderToFile> allFolderToFileMappings)
        {
            var folderDto = new VirtualFolderTreeDto
            {
                Id = folder.Id,
                FolderName = folder.FolderName
            };
            folderDto.BucketFiles = allFolderToFileMappings
                .Where(mapping => mapping.FolderId == folder.Id)
                .Select(mapping => new VirtualFolderFileDto
                {
                    FileId = mapping.BucketFileId,
                    FileName = mapping.BucketFile.FileName,
                    Title = mapping.BucketFile.Title
                })
                .ToList();
            await FillFileUrls(folderDto.BucketFiles);
            var childFolders = allFolders
                .Where(f => f.ParentFolderId == folder.Id)
                .ToList();
            folderDto.Children = (await Task.WhenAll(childFolders
               .Select(child => BuildTree(child, allFolders, allFolderToFileMappings))))
               .ToList();
            return folderDto;
        }
    }

    /// <summary>
    /// Get files in a virtual folder with pagination and sorting support.
    /// </summary>
    /// <param name="input">Search criteria for virtual folder files.</param>
    /// <returns>PagedResultDto of VirtualFolderFileDto</returns>
    public async Task<PagedResultDto<VirtualFolderFileDto>> GetVirtualFolderFilesAsync(SearchVirtualFolderDto input)
    {
        var queryable = await _folderToBucketFileRepository.GetQueryableAsync();

        var query = queryable
            .Include(f => f.BucketFile)
            .Where(f => f.FolderId == input.FolderId && !f.IsDeleted);

        var totalCount = await query.CountAsync();

        var files = await query
            .OrderBy(input.Sorting ?? "Weight desc, CreationTime desc")
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var fileDtos = files.Select(f => new VirtualFolderFileDto
        {
            FileId = f.BucketFileId,
            FileName = f.BucketFile.FileName,
            Title = f.BucketFile.Title
        }).ToList();
        await FillFileUrls(fileDtos);

        return new PagedResultDto<VirtualFolderFileDto>(totalCount, fileDtos);
    }
}