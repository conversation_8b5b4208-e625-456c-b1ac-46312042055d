using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Enums;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.VirtualFolders
{
    [AllowAnonymous]
    public class ReadOnlyVirtualFolderAppService : HolyBlessAppService, IReadOnlyVirtualFolderAppService
    {
        protected readonly IRepository<VirtualDiskFolder, int> _virtualFolderRepository;
        protected readonly IRepository<FolderToFile> _folderToBucketFileRepository;
        protected readonly IRepository<VirtualDiskFolderTree, int> _folderTreeRepository;

        public ReadOnlyVirtualFolderAppService(
             IRepository<VirtualDiskFolder, int> virtualFolderRepository,
             IRepository<FolderToFile> folderToBucketFileRepository,
             IRepository<VirtualDiskFolderTree, int> folderTreeRepository,
             IRequestContextService requestContextService,
             ICachedFileUrlAppService cachedFileUrlAppService
            ) : base(cachedFileUrlAppService, requestContextService)
        {
            _folderToBucketFileRepository = folderToBucketFileRepository;
            _folderTreeRepository = folderTreeRepository;
            _virtualFolderRepository = virtualFolderRepository;
        }

        /// <summary>
        /// Get a paginated list of virtual folders based on search criteria.
        /// Usage: Virtual folder list page API
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<VirtualFolderDto>> GetListAsync(VirtualFolderSearchDto input)
        {
            var queryable = await _virtualFolderRepository.GetQueryableAsync();
            var lang = _requestContextService!.GetLanguageCode();
            var query = queryable
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == input.ChannelId)
                .WhereIf(!string.IsNullOrEmpty(input.ContentCode), x => x.ContentCode == input.ContentCode && x.LanguageCode == lang);

            var totalCount = await query.CountAsync();

            var virtualFolders = await query
                .OrderBy(input.Sorting ?? "Weight desc, CreationTime desc")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var virtualFolderDtos = ObjectMapper.Map<List<VirtualDiskFolder>, List<VirtualFolderDto>>(virtualFolders);

            return new PagedResultDto<VirtualFolderDto>(totalCount, virtualFolderDtos);
        }

        /// <summary>
        /// Get a paginated list of files within a virtual folder with sorting support.
        /// Similar to GetAlbumFilesAsync but with pagination and sorting.
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<VirtualFolderFileDto>> GetFolderFilesAsync(VirtualFolderFileSearchDto input)
        {
            var queryable = await _folderToBucketFileRepository.GetQueryableAsync();
            var lang = _requestContextService!.GetLanguageCode();
            var query = queryable
                .Include(x => x.BucketFile)
                .WhereIf(input.FolderId.HasValue, x => x.FolderId == input.FolderId)
                .WhereIf(!string.IsNullOrEmpty(input.ContentCode), x => input.ContentCode == x.Folder.ContentCode && x.Folder.LanguageCode == lang);

            var totalCount = await query.CountAsync();

            var folderFiles = await query
                .OrderBy(input.Sorting ?? "BucketFile.DeliveryDate")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var folderFileDtos = folderFiles.Select(ftf => new VirtualFolderFileDto
            {
                FileId = ftf.BucketFileId,
                FileName = ftf.BucketFile.FileName,
                Title = ftf.Title ?? ftf.BucketFile.Title
            }).ToList();

            await FillFileUrls(folderFileDtos);

            return new PagedResultDto<VirtualFolderFileDto>(totalCount, folderFileDtos);
        }

        /// <summary>
        /// Get the virtual folder tree structure for a specific folder.
        /// Usage: For folder navigation, folder detail page left side menu
        /// </summary>
        /// <param name="folderId"></param>
        /// <returns></returns>
        public async Task<List<VirtualFolderTreeDto>> GetVirtualFolderTreeAsync(int folderId)
        {
            // Get all descendant folders using recursive query
            var allFolders = await GetAllDescendantFoldersAsync(folderId);

            // Convert all folders to VirtualFolderHierarchyDto
            var folderDtos = ObjectMapper.Map<List<VirtualDiskFolder>, List<VirtualFolderTreeDto>>(allFolders);

            // Create a dictionary for quick lookup
            var folderDict = folderDtos.ToDictionary(x => x.Id);

            // Build the tree structure
            var rootFolders = new List<VirtualFolderTreeDto>();

            foreach (var folder in folderDtos)
            {
                if (folder.ParentFolderId.HasValue && folderDict.TryGetValue(folder.ParentFolderId.Value, out var parentDto))
                {
                    // Add as child to parent
                    parentDto.Children.Add(folder);
                }
                else if (folder.ParentFolderId == folderId)
                {
                    // Add as root folder (direct child of the specified folder)
                    folder.IsRoot = true;
                    rootFolders.Add(folder);
                }
            }

            return rootFolders;
        }

        /// <summary>
        /// Get the virtual folder tree structure by folder content code and language code (in request header).
        /// Usage: For folder navigation, folder detail page left side menu
        /// </summary>
        /// <param name="contentCode"></param>
        /// <returns></returns>
        public async Task<List<VirtualFolderTreeDto>> GetVirtualFolderTreeAsync(string contentCode)
        {
            var folderId = await GetMatchedId(contentCode);
            if (folderId == 0) return [];
            return await GetVirtualFolderTreeAsync(folderId);
        }

        [RemoteService(false)]
        public async Task<string> GetFolderTreeJson(int rootFolderId)
        {
            var tree = await _folderTreeRepository.GetAsync(rootFolderId);
            return tree.TreeJsonData;
        }

        private async Task<List<VirtualDiskFolder>> GetAllDescendantFoldersAsync(int folderId)
        {
            var queryable = await _virtualFolderRepository.GetQueryableAsync();
            var result = new List<VirtualDiskFolder>();
            var currentLevelParentIds = new List<int> { folderId };

            while (currentLevelParentIds.Any())
            {
                // Get children of current level parents
                var children = await queryable
                    .Where(x => x.ParentFolderId.HasValue && currentLevelParentIds.Contains(x.ParentFolderId.Value))
                    .OrderByDescending(x => x.Weight)
                    .ToListAsync();

                if (!children.Any())
                    break;

                result.AddRange(children);
                currentLevelParentIds = children.Select(x => x.Id).ToList();
            }

            return result.OrderBy(x => x.ParentFolderId).ThenBy(x => x.FolderName).ToList();
        }

        private async Task<int> GetMatchedId(string contentCode)
        {
            var lang = _requestContextService!.GetLanguageCode();
            var folder = await _virtualFolderRepository.FirstOrDefaultAsync(x => x.ContentCode == contentCode && x.LanguageCode == lang);
            if (folder == null) return 0;
            return folder.Id;
        }
    }
}