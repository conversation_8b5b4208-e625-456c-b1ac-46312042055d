## Migration Overview

* Source database is MySQL, Target database is PostgreSQL
* In appsettings.json, there are two connection string, one point to MySQL and the other to PostgreSQL
* Using Dappr.Net as the migration tool

## Migration Chapter

* Source SQL
```sql
select 1 as BookId, 0 as Views, 0 as Likes, Id, parent_id,name,
description, acc.weigh , FROM_UNIXTIME( acc.createtime) as createtime , 
FROM_UNIXTIME(acc.updatetime) as updatetime 
from holybless.admincms_cms_channeltwo acc 
order by acc.parent_id , acc.weigh 
```

 If above SQL result, the parent_id value is 0, then change it to null

 * Target SQL
 ```sql
 INSERT INTO public."Chapters"
("EBookId","Views","Likes", "Id", "ParentChapterId", "Title", "Content", "Weight", "CreationTime", "LastModificationTime")
```

