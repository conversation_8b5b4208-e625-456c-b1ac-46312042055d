import{p as s,x as p}from"./chunk-CNIH62FZ.js";import{q as i}from"./chunk-D6WDCTDG.js";import{ab as n,yb as m}from"./chunk-BL4EGCPV.js";import"./chunk-4CLCTAJ7.js";var r=class t{navigateTo(e){console.log("Navigating to:",e)}static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275cmp=n({type:t,selectors:[["app-home"]],decls:1,vars:0,template:function(o,a){o&1&&m(0,"router-outlet")},dependencies:[i,p,s],styles:["[_nghost-%COMP%]{flex:1;display:flex}"]})}};export{r as HomeComponent};
