using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Channels.Dtos;
using HolyBless.Entities.Channels;
using HolyBless.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Channels
{
    public class ReadOnlyChannelAppService : HolyBlessAppService, IReadOnlyChannelAppService
    {
        protected readonly IRepository<Channel, int> _repository;

        public ReadOnlyChannelAppService(
            IRepository<Channel, int> repository,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService
            ) : base(cachedFileUrlAppService, requestContextService)
        {
            _repository = repository;
        }

        [RemoteService(false)]
        public virtual async Task<ChannelDto> GetAsync(int id)
        {
            var channel = await _repository.FirstOrDefaultAsync(x => x.Id == id);
            Check.NotNull(channel, nameof(channel));
            var channelDto = ObjectMapper.Map<Channel, ChannelDto>(channel);
            return channelDto;
        }

        [RemoteService(false)]
        public virtual async Task<PagedResultDto<ChannelDto>> GetListAsync(ChannelSearchDto input)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .WhereIf(!input.LanguageCode.IsNullOrWhiteSpace(), x => x.LanguageCode.ToLower() == input.LanguageCode!.ToLower())
                .OrderBy(input.Sorting ?? "Weight")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var channels = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<ChannelDto>(
                totalCount,
                ObjectMapper.Map<List<Channel>, List<ChannelDto>>(channels)
            );
        }

        [RemoteService(false)]
        public virtual async Task<List<ChannelDto>> GetAllListByLanguageAsync(string languageCode = "zh-Hans")
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.LanguageCode == null || x.LanguageCode.ToLower() == languageCode.ToLower())
                .OrderBy(x => x.Weight);
            var channels = await AsyncExecuter.ToListAsync(query);
            return ObjectMapper.Map<List<Channel>, List<ChannelDto>>(channels);
        }

        /// <summary>
        /// Get a channel by its content code and language code (from request header).
        /// </summary>
        /// <param name="contentCode"></param>
        /// <returns></returns>
        public async Task<ChannelDto?> GetMatchedChannelAsync(string contentCode)
        {
            var lang = _requestContextService!.GetLanguageCode();
            var channel = await _repository.FirstOrDefaultAsync(x => x.ContentCode == contentCode && x.LanguageCode == lang);
            if (channel == null) return null;
            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        /// <summary>
        /// Get the channel tree structure for a specific language.
        /// Usage: Render UI top main menu and its menu items.
        /// </summary>
        /// <param name="languageCode"></param>
        /// <returns></returns>
        public virtual async Task<List<ChannelTreeDto>> GetChannelTreeAsync(string languageCode)
        {
            var queryable = await _repository.GetQueryableAsync();
            queryable = queryable
                .Where(x => x.LanguageCode.ToLower() == languageCode.ToLower())
                .OrderBy(x => x.ParentChannelId).ThenBy(x => x.Weight);

            var allChannels = await AsyncExecuter.ToListAsync(queryable);

            // Convert all channels to ChannelTreeDto
            var channelDtos = ObjectMapper.Map<List<Channel>, List<ChannelTreeDto>>(allChannels);

            // Create a dictionary for quick lookup
            var channelDict = channelDtos.ToDictionary(x => x.Id);

            // Build the tree structure
            var rootChannels = new List<ChannelTreeDto>();

            foreach (var channel in channelDtos)
            {
                var parentChannel = allChannels.FirstOrDefault(x => x.Id == channel.Id)?.ParentChannelId;

                if (parentChannel.HasValue && channelDict.TryGetValue(parentChannel.Value, out var parentDto))
                {
                    if (channel.ChannelSource == null)
                    {
                        channel.ChannelSource = parentDto.ChannelSource;
                    }
                    // Add as child to parent
                    parentDto.Children.Add(channel);
                }
                else if (channel.IsRoot)
                {
                    // Add as root channel
                    rootChannels.Add(channel);
                }
            }

            return rootChannels;
        }
    }
}