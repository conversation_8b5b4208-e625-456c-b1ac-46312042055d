import{b as l}from"./chunk-T2K2OPF3.js";import{R as a,W as o}from"./chunk-BL4EGCPV.js";import{a as r}from"./chunk-4CLCTAJ7.js";var s=class i{constructor(e){this.restService=e;this.apiName="Default";this.getArticleAggregate=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-article/article-aggregate/${e}`},r({apiName:this.apiName},t));this.getArticleAggregatesByChapterId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-article/article-aggregates-by-chapter-id/${e}`},r({apiName:this.apiName},t));this.getArticleAggregatesByCollectionId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-article/article-aggregates-by-collection-id/${e}`},r({apiName:this.apiName},t));this.getRelatedArticlesByFileIdByFileId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-article/related-articles-by-file-id/${e}`},r({apiName:this.apiName},t))}static{this.\u0275fac=function(t){return new(t||i)(o(l))}}static{this.\u0275prov=a({token:i,factory:i.\u0275fac,providedIn:"root"})}};export{s as a};
