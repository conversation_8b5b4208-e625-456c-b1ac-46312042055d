import{b as B}from"./chunk-D6WDCTDG.js";import{Hb as Q,Q as D,S as b,U as d,Va as T,Wa as s,Zb as F,bb as E,ca as k,cb as u,eb as h,f as q,ga as R,i as z,ib as Y,ma as A,mb as K,p as Z,pb as J,qa as G,qc as ee,ra as M,uc as j,v as X,wc as c,xc as w}from"./chunk-BL4EGCPV.js";import{a as o,b as l}from"./chunk-4CLCTAJ7.js";var ue=(()=>{class t{_renderer;_elementRef;onChange=n=>{};onTouched=()=>{};constructor(n,i){this._renderer=n,this._elementRef=i}setProperty(n,i){this._renderer.setProperty(this._elementRef.nativeElement,n,i)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static \u0275fac=function(i){return new(i||t)(s(T),s(G))};static \u0275dir=u({type:t})}return t})(),be=(()=>{class t extends ue{static \u0275fac=(()=>{let n;return function(r){return(n||(n=R(t)))(r||t)}})();static \u0275dir=u({type:t,features:[h]})}return t})(),de=new d("");var Ae={provide:de,useExisting:D(()=>ce),multi:!0};function Me(){let t=B()?B().getUserAgent():"";return/android (\d+)/.test(t.toLowerCase())}var Ee=new d(""),ce=(()=>{class t extends ue{_compositionMode;_composing=!1;constructor(n,i,r){super(n,i),this._compositionMode=r,this._compositionMode==null&&(this._compositionMode=!Me())}writeValue(n){let i=n??"";this.setProperty("value",i)}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static \u0275fac=function(i){return new(i||t)(s(T),s(G),s(Ee,8))};static \u0275dir=u({type:t,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(i,r){i&1&&Q("input",function(m){return r._handleInput(m.target.value)})("blur",function(){return r.onTouched()})("compositionstart",function(){return r._compositionStart()})("compositionend",function(m){return r._compositionEnd(m.target.value)})},standalone:!1,features:[F([Ae]),h]})}return t})();function Fe(t){return t==null||we(t)===0}function we(t){return t==null?null:Array.isArray(t)||typeof t=="string"?t.length:t instanceof Set?t.size:null}var he=new d(""),Ie=new d("");function Se(t){return Fe(t.value)?{required:!0}:null}function te(t){return null}function fe(t){return t!=null}function ge(t){return Y(t)?z(t):t}function pe(t){let e={};return t.forEach(n=>{e=n!=null?o(o({},e),n):e}),Object.keys(e).length===0?null:e}function me(t,e){return e.map(n=>n(t))}function Ne(t){return!t.validate}function _e(t){return t.map(e=>Ne(e)?e:n=>e.validate(n))}function Oe(t){if(!t)return null;let e=t.filter(fe);return e.length==0?null:function(n){return pe(me(n,e))}}function ve(t){return t!=null?Oe(_e(t)):null}function xe(t){if(!t)return null;let e=t.filter(fe);return e.length==0?null:function(n){let i=me(n,e).map(ge);return X(i).pipe(Z(pe))}}function ye(t){return t!=null?xe(_e(t)):null}function ne(t,e){return t===null?[e]:Array.isArray(t)?[...t,e]:[t,e]}function Pe(t){return t._rawValidators}function ke(t){return t._rawAsyncValidators}function U(t){return t?Array.isArray(t)?t:[t]:[]}function S(t,e){return Array.isArray(t)?t.includes(e):t===e}function ie(t,e){let n=U(e);return U(t).forEach(r=>{S(n,r)||n.push(r)}),n}function re(t,e){return U(e).filter(n=>!S(t,n))}var N=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(e){this._rawValidators=e||[],this._composedValidatorFn=ve(this._rawValidators)}_setAsyncValidators(e){this._rawAsyncValidators=e||[],this._composedAsyncValidatorFn=ye(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(e){this._onDestroyCallbacks.push(e)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(e=>e()),this._onDestroyCallbacks=[]}reset(e=void 0){this.control&&this.control.reset(e)}hasError(e,n){return this.control?this.control.hasError(e,n):!1}getError(e,n){return this.control?this.control.getError(e,n):null}},H=class extends N{name;get formDirective(){return null}get path(){return null}},V=class extends N{_parent=null;name=null;valueAccessor=null},L=class{_cd;constructor(e){this._cd=e}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},Re={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},Mt=l(o({},Re),{"[class.ng-submitted]":"isSubmitted"}),Et=(()=>{class t extends L{constructor(n){super(n)}static \u0275fac=function(i){return new(i||t)(s(V,2))};static \u0275dir=u({type:t,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(i,r){i&2&&J("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)},standalone:!1,features:[h]})}return t})();var _="VALID",I="INVALID",f="PENDING",v="DISABLED",p=class{},O=class extends p{value;source;constructor(e,n){super(),this.value=e,this.source=n}},y=class extends p{pristine;source;constructor(e,n){super(),this.pristine=e,this.source=n}},C=class extends p{touched;source;constructor(e,n){super(),this.touched=e,this.source=n}},g=class extends p{status;source;constructor(e,n){super(),this.status=e,this.source=n}};function Ge(t){return(x(t)?t.validators:t)||null}function Te(t){return Array.isArray(t)?ve(t):t||null}function je(t,e){return(x(e)?e.asyncValidators:t)||null}function Be(t){return Array.isArray(t)?ye(t):t||null}function x(t){return t!=null&&!Array.isArray(t)&&typeof t=="object"}var $=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(e,n){this._assignValidators(e),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(e){this._rawValidators=this._composedValidatorFn=e}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(e){this._rawAsyncValidators=this._composedAsyncValidatorFn=e}get parent(){return this._parent}get status(){return c(this.statusReactive)}set status(e){c(()=>this.statusReactive.set(e))}_status=w(()=>this.statusReactive());statusReactive=M(void 0);get valid(){return this.status===_}get invalid(){return this.status===I}get pending(){return this.status==f}get disabled(){return this.status===v}get enabled(){return this.status!==v}errors;get pristine(){return c(this.pristineReactive)}set pristine(e){c(()=>this.pristineReactive.set(e))}_pristine=w(()=>this.pristineReactive());pristineReactive=M(!0);get dirty(){return!this.pristine}get touched(){return c(this.touchedReactive)}set touched(e){c(()=>this.touchedReactive.set(e))}_touched=w(()=>this.touchedReactive());touchedReactive=M(!1);get untouched(){return!this.touched}_events=new q;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(e){this._assignValidators(e)}setAsyncValidators(e){this._assignAsyncValidators(e)}addValidators(e){this.setValidators(ie(e,this._rawValidators))}addAsyncValidators(e){this.setAsyncValidators(ie(e,this._rawAsyncValidators))}removeValidators(e){this.setValidators(re(e,this._rawValidators))}removeAsyncValidators(e){this.setAsyncValidators(re(e,this._rawAsyncValidators))}hasValidator(e){return S(this._rawValidators,e)}hasAsyncValidator(e){return S(this._rawAsyncValidators,e)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(e={}){let n=this.touched===!1;this.touched=!0;let i=e.sourceControl??this;this._parent&&!e.onlySelf&&this._parent.markAsTouched(l(o({},e),{sourceControl:i})),n&&e.emitEvent!==!1&&this._events.next(new C(!0,i))}markAllAsTouched(e={}){this.markAsTouched({onlySelf:!0,emitEvent:e.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(e))}markAsUntouched(e={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let i=e.sourceControl??this;this._forEachChild(r=>{r.markAsUntouched({onlySelf:!0,emitEvent:e.emitEvent,sourceControl:i})}),this._parent&&!e.onlySelf&&this._parent._updateTouched(e,i),n&&e.emitEvent!==!1&&this._events.next(new C(!1,i))}markAsDirty(e={}){let n=this.pristine===!0;this.pristine=!1;let i=e.sourceControl??this;this._parent&&!e.onlySelf&&this._parent.markAsDirty(l(o({},e),{sourceControl:i})),n&&e.emitEvent!==!1&&this._events.next(new y(!1,i))}markAsPristine(e={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let i=e.sourceControl??this;this._forEachChild(r=>{r.markAsPristine({onlySelf:!0,emitEvent:e.emitEvent})}),this._parent&&!e.onlySelf&&this._parent._updatePristine(e,i),n&&e.emitEvent!==!1&&this._events.next(new y(!0,i))}markAsPending(e={}){this.status=f;let n=e.sourceControl??this;e.emitEvent!==!1&&(this._events.next(new g(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.markAsPending(l(o({},e),{sourceControl:n}))}disable(e={}){let n=this._parentMarkedDirty(e.onlySelf);this.status=v,this.errors=null,this._forEachChild(r=>{r.disable(l(o({},e),{onlySelf:!0}))}),this._updateValue();let i=e.sourceControl??this;e.emitEvent!==!1&&(this._events.next(new O(this.value,i)),this._events.next(new g(this.status,i)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(l(o({},e),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!0))}enable(e={}){let n=this._parentMarkedDirty(e.onlySelf);this.status=_,this._forEachChild(i=>{i.enable(l(o({},e),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent}),this._updateAncestors(l(o({},e),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(i=>i(!1))}_updateAncestors(e,n){this._parent&&!e.onlySelf&&(this._parent.updateValueAndValidity(e),e.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(e){this._parent=e}getRawValue(){return this.value}updateValueAndValidity(e={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let i=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===_||this.status===f)&&this._runAsyncValidator(i,e.emitEvent)}let n=e.sourceControl??this;e.emitEvent!==!1&&(this._events.next(new O(this.value,n)),this._events.next(new g(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.updateValueAndValidity(l(o({},e),{sourceControl:n}))}_updateTreeValidity(e={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(e)),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?v:_}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(e,n){if(this.asyncValidator){this.status=f,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1};let i=ge(this.asyncValidator(this));this._asyncValidationSubscription=i.subscribe(r=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(r,{emitEvent:n,shouldHaveEmitted:e})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let e=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,e}return!1}setErrors(e,n={}){this.errors=e,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(e){let n=e;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((i,r)=>i&&i._find(r),this)}getError(e,n){let i=n?this.get(n):this;return i&&i.errors?i.errors[e]:null}hasError(e,n){return!!this.getError(e,n)}get root(){let e=this;for(;e._parent;)e=e._parent;return e}_updateControlsErrors(e,n,i){this.status=this._calculateStatus(),e&&this.statusChanges.emit(this.status),(e||i)&&this._events.next(new g(this.status,n)),this._parent&&this._parent._updateControlsErrors(e,n,i)}_initObservables(){this.valueChanges=new A,this.statusChanges=new A}_calculateStatus(){return this._allControlsDisabled()?v:this.errors?I:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(f)?f:this._anyControlsHaveStatus(I)?I:_}_anyControlsHaveStatus(e){return this._anyControls(n=>n.status===e)}_anyControlsDirty(){return this._anyControls(e=>e.dirty)}_anyControlsTouched(){return this._anyControls(e=>e.touched)}_updatePristine(e,n){let i=!this._anyControlsDirty(),r=this.pristine!==i;this.pristine=i,this._parent&&!e.onlySelf&&this._parent._updatePristine(e,n),r&&this._events.next(new y(this.pristine,n))}_updateTouched(e={},n){this.touched=this._anyControlsTouched(),this._events.next(new C(this.touched,n)),this._parent&&!e.onlySelf&&this._parent._updateTouched(e,n)}_onDisabledChange=[];_registerOnCollectionChange(e){this._onCollectionChange=e}_setUpdateStrategy(e){x(e)&&e.updateOn!=null&&(this._updateOn=e.updateOn)}_parentMarkedDirty(e){let n=this._parent&&this._parent.dirty;return!e&&!!n&&!this._parent._anyControlsDirty()}_find(e){return null}_assignValidators(e){this._rawValidators=Array.isArray(e)?e.slice():e,this._composedValidatorFn=Te(this._rawValidators)}_assignAsyncValidators(e){this._rawAsyncValidators=Array.isArray(e)?e.slice():e,this._composedAsyncValidatorFn=Be(this._rawAsyncValidators)}};var W=new d("",{providedIn:"root",factory:()=>P}),P="always";function Ue(t,e){return[...e.path,t]}function He(t,e,n=P){$e(t,e),e.valueAccessor.writeValue(t.value),(t.disabled||n==="always")&&e.valueAccessor.setDisabledState?.(t.disabled),We(t,e),ze(t,e),qe(t,e),Le(t,e)}function se(t,e){t.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(e)})}function Le(t,e){if(e.valueAccessor.setDisabledState){let n=i=>{e.valueAccessor.setDisabledState(i)};t.registerOnDisabledChange(n),e._registerOnDestroy(()=>{t._unregisterOnDisabledChange(n)})}}function $e(t,e){let n=Pe(t);e.validator!==null?t.setValidators(ne(n,e.validator)):typeof n=="function"&&t.setValidators([n]);let i=ke(t);e.asyncValidator!==null?t.setAsyncValidators(ne(i,e.asyncValidator)):typeof i=="function"&&t.setAsyncValidators([i]);let r=()=>t.updateValueAndValidity();se(e._rawValidators,r),se(e._rawAsyncValidators,r)}function We(t,e){e.valueAccessor.registerOnChange(n=>{t._pendingValue=n,t._pendingChange=!0,t._pendingDirty=!0,t.updateOn==="change"&&Ce(t,e)})}function qe(t,e){e.valueAccessor.registerOnTouched(()=>{t._pendingTouched=!0,t.updateOn==="blur"&&t._pendingChange&&Ce(t,e),t.updateOn!=="submit"&&t.markAsTouched()})}function Ce(t,e){t._pendingDirty&&t.markAsDirty(),t.setValue(t._pendingValue,{emitModelToViewChange:!1}),e.viewToModelUpdate(t._pendingValue),t._pendingChange=!1}function ze(t,e){let n=(i,r)=>{e.valueAccessor.writeValue(i),r&&e.viewToModelUpdate(i)};t.registerOnChange(n),e._registerOnDestroy(()=>{t._unregisterOnChange(n)})}function Ze(t,e){if(!t.hasOwnProperty("model"))return!1;let n=t.model;return n.isFirstChange()?!0:!Object.is(e,n.currentValue)}function Xe(t){return Object.getPrototypeOf(t.constructor)===be}function Ye(t,e){if(!e)return null;Array.isArray(e);let n,i,r;return e.forEach(a=>{a.constructor===ce?n=a:Xe(a)?i=a:r=a}),r||i||n||null}function oe(t,e){let n=t.indexOf(e);n>-1&&t.splice(n,1)}function ae(t){return typeof t=="object"&&t!==null&&Object.keys(t).length===2&&"value"in t&&"disabled"in t}var Ke=class extends ${defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(e=null,n,i){super(Ge(n),je(i,n)),this._applyFormState(e),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),x(n)&&(n.nonNullable||n.initialValueIsDefault)&&(ae(e)?this.defaultValue=e.value:this.defaultValue=e)}setValue(e,n={}){this.value=this._pendingValue=e,this._onChange.length&&n.emitModelToViewChange!==!1&&this._onChange.forEach(i=>i(this.value,n.emitViewToModelChange!==!1)),this.updateValueAndValidity(n)}patchValue(e,n={}){this.setValue(e,n)}reset(e=this.defaultValue,n={}){this._applyFormState(e),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(e){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(e){this._onChange.push(e)}_unregisterOnChange(e){oe(this._onChange,e)}registerOnDisabledChange(e){this._onDisabledChange.push(e)}_unregisterOnDisabledChange(e){oe(this._onDisabledChange,e)}_forEachChild(e){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(e){ae(e)?(this.value=this._pendingValue=e.value,e.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=e}};var Je={provide:V,useExisting:D(()=>Qe)},le=Promise.resolve(),Qe=(()=>{class t extends V{_changeDetectorRef;callSetDisabledState;control=new Ke;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new A;constructor(n,i,r,a,m,De){super(),this._changeDetectorRef=m,this.callSetDisabledState=De,this._parent=n,this._setValidators(i),this._setAsyncValidators(r),this.valueAccessor=Ye(this,a)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){let i=n.name.previousValue;this.formDirective.removeControl({name:i,path:this._getPath(i)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),Ze(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){He(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(n){le.then(()=>{this.control.setValue(n,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(n){let i=n.isDisabled.currentValue,r=i!==0&&j(i);le.then(()=>{r&&!this.control.disabled?this.control.disable():!r&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(n){return this._parent?Ue(n,this._parent):[n]}static \u0275fac=function(i){return new(i||t)(s(H,9),s(he,10),s(Ie,10),s(de,10),s(ee,8),s(W,8))};static \u0275dir=u({type:t,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[F([Je]),h,k]})}return t})();var et=new d("");var tt=(()=>{class t{_validator=te;_onChange;_enabled;ngOnChanges(n){if(this.inputName in n){let i=this.normalizeInput(n[this.inputName].currentValue);this._enabled=this.enabled(i),this._validator=this._enabled?this.createValidator(i):te,this._onChange&&this._onChange()}}validate(n){return this._validator(n)}registerOnValidatorChange(n){this._onChange=n}enabled(n){return n!=null}static \u0275fac=function(i){return new(i||t)};static \u0275dir=u({type:t,features:[k]})}return t})();var nt={provide:he,useExisting:D(()=>it),multi:!0};var it=(()=>{class t extends tt{required;inputName="required";normalizeInput=j;createValidator=n=>Se;enabled(n){return n}static \u0275fac=(()=>{let n;return function(r){return(n||(n=R(t)))(r||t)}})();static \u0275dir=u({type:t,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(i,r){i&2&&K("required",r._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[F([nt]),h]})}return t})();var Ve=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=E({type:t});static \u0275inj=b({})}return t})();var wt=(()=>{class t{static withConfig(n){return{ngModule:t,providers:[{provide:W,useValue:n.callSetDisabledState??P}]}}static \u0275fac=function(i){return new(i||t)};static \u0275mod=E({type:t});static \u0275inj=b({imports:[Ve]})}return t})(),It=(()=>{class t{static withConfig(n){return{ngModule:t,providers:[{provide:et,useValue:n.warnOnNgModelWithFormControl??"always"},{provide:W,useValue:n.callSetDisabledState??P}]}}static \u0275fac=function(i){return new(i||t)};static \u0275mod=E({type:t});static \u0275inj=b({imports:[Ve]})}return t})();export{de as a,ce as b,V as c,Et as d,Qe as e,it as f,wt as g,It as h};
