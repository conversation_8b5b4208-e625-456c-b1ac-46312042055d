using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Dapper;
using HolyBless.MySqlMigration.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MySql.Data.MySqlClient;
using Npgsql;

namespace HolyBless.MySqlMigration.Services
{
    /// <summary>
    /// Service for migrating chapter data from MySQL to PostgreSQL
    /// </summary>
    public class ChapterMigrationService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<ChapterMigrationService> _logger;
        private readonly string _mysqlConnectionString;
        private readonly string _postgresConnectionString;

        public ChapterMigrationService(IConfiguration configuration, ILogger<ChapterMigrationService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _mysqlConnectionString = _configuration.GetConnectionString("MySQL") 
                ?? throw new InvalidOperationException("MySQL connection string not found");
            _postgresConnectionString = _configuration.GetConnectionString("PostgreSQL") 
                ?? throw new InvalidOperationException("PostgreSQL connection string not found");
        }

        /// <summary>
        /// Execute the complete chapter migration process
        /// </summary>
        /// <returns></returns>
        public async Task<int> MigrateChaptersAsync()
        {
            try
            {
                _logger.LogInformation("Starting chapter migration from MySQL to PostgreSQL");

                // Step 1: Validate connections
                await ValidateConnectionsAsync();

                // Step 2: Read data from MySQL
                var chapters = await ReadChaptersFromMySqlAsync();
                _logger.LogInformation($"Retrieved {chapters.Count} chapters from MySQL");

                if (chapters.Count == 0)
                {
                    _logger.LogWarning("No chapters found in MySQL database");
                    return 0;
                }

                // Step 3: Insert data into PostgreSQL
                var insertedCount = await InsertChaptersToPostgreSqlAsync(chapters);
                _logger.LogInformation($"Successfully migrated {insertedCount} chapters to PostgreSQL");

                return insertedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during chapter migration");
                throw;
            }
        }

        /// <summary>
        /// Validate database connections before starting migration
        /// </summary>
        /// <returns></returns>
        private async Task ValidateConnectionsAsync()
        {
            _logger.LogInformation("Validating database connections...");

            // Test MySQL connection
            try
            {
                using var mysqlConnection = new MySqlConnection(_mysqlConnectionString);
                await mysqlConnection.OpenAsync();
                _logger.LogInformation("MySQL connection validated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect to MySQL database");
                throw new InvalidOperationException("MySQL connection failed", ex);
            }

            // Test PostgreSQL connection
            try
            {
                using var postgresConnection = new NpgsqlConnection(_postgresConnectionString);
                await postgresConnection.OpenAsync();
                _logger.LogInformation("PostgreSQL connection validated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect to PostgreSQL database");
                throw new InvalidOperationException("PostgreSQL connection failed", ex);
            }
        }

        /// <summary>
        /// Read chapter data from MySQL using the specified SQL query
        /// </summary>
        /// <returns></returns>
        private async Task<List<ChapterMigrationModel>> ReadChaptersFromMySqlAsync()
        {
            const string sourceQuery = @"
                select 1 as BookId, 0 as Views, 0 as Likes, Id, parent_id, name,
                description, acc.weigh, FROM_UNIXTIME(acc.createtime) as createtime, 
                FROM_UNIXTIME(acc.updatetime) as updatetime 
                from holybless.admincms_cms_channeltwo acc 
                order by acc.parent_id, acc.weigh";

            using var connection = new MySqlConnection(_mysqlConnectionString);
            await connection.OpenAsync();

            var chapters = await connection.QueryAsync<ChapterMigrationModel>(sourceQuery);
            return chapters.ToList();
        }

        /// <summary>
        /// Insert chapter data into PostgreSQL
        /// </summary>
        /// <param name="chapters"></param>
        /// <returns></returns>
        private async Task<int> InsertChaptersToPostgreSqlAsync(List<ChapterMigrationModel> chapters)
        {
            const string insertQuery = @"
                INSERT INTO public.""Chapters""
                (""EBookId"", ""Views"", ""Likes"", ""Id"", ""ParentChapterId"", ""Title"", ""Content"", ""Weight"", ""CreationTime"", ""LastModificationTime"")
                VALUES (@EBookId, @Views, @Likes, @Id, @ParentChapterId, @Title, @Content, @Weight, @CreationTime, @LastModificationTime)";

            using var connection = new NpgsqlConnection(_postgresConnectionString);
            await connection.OpenAsync();

            using var transaction = await connection.BeginTransactionAsync();
            try
            {
                var insertedCount = 0;

                foreach (var chapter in chapters)
                {
                    var parameters = new
                    {
                        EBookId = chapter.BookId,
                        Views = chapter.Views,
                        Likes = chapter.Likes,
                        Id = chapter.Id,
                        ParentChapterId = chapter.ParentChapterId, // This handles the 0 -> null transformation
                        Title = chapter.name,
                        Content = chapter.description,
                        Weight = chapter.weigh,
                        CreationTime = chapter.createtime,
                        LastModificationTime = chapter.updatetime
                    };

                    var result = await connection.ExecuteAsync(insertQuery, parameters, transaction);
                    insertedCount += result;

                    if (insertedCount % 100 == 0)
                    {
                        _logger.LogInformation($"Processed {insertedCount} chapters...");
                    }
                }

                await transaction.CommitAsync();
                return insertedCount;
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
